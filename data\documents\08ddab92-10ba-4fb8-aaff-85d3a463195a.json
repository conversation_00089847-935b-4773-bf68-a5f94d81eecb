{"doc_id": "08ddab92-10ba-4fb8-aaff-85d3a463195a", "title": "item291_US_Harnessing AI to accelerate innovations (1)", "text": "Harnessing AI to accelerate innovations\r\nIdentifying patient unmet need with Data and AI\r\n\r\nEvery day, doctors have their work cut out for them. They’re constantly bombarded with new clinical data, product information and innovation. And through it all, they’re tasked with delivering outstanding care for the ones they serve.\r\n\r\nAs partners in care, we asked ourselves how we could turn something overwhelming into an advantage. Medical Engagement.ai is a first-in-industry effort built in partnership with data and advanced analytics teams to turn real world data and clinical input into a measure of “unmet medical need”: proactively and predictively identifying patients who aren’t benefitting from the most effective treatments for their specific disease, or who may be lagging behind the latest recommended guidelines. In the U.S. alone, we’ve started to identify medical unmet needs in 7 different disease areas, engaged with more than 5,000 additional HCPs, and identified approximately 75,000 patients with one or more unmet medical needs that can be addressed through partnership with customers.\r\n\r\nThe AI and advanced analytic algorithms enable our Medical Science Liaisons to partner with physicians, nurses, and institutions to determine which patients can benefit from appropriate treatments (as recommended by the latest clinical guidelines). It allows us to predict which approved clinical and product information would help our customers address those unmet needs — and connect them with this information when they need it.\r\n\r\nWorking together with medical providers and the people they serve, we can not only help change individual lives — but create a healthier world.", "metadata": {"original_filename": "item291_US_Harnessing AI to accelerate innovations (1).txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:51:56.747812", "updated_at": "2025-08-28T21:51:56.747812", "word_count": 1687}