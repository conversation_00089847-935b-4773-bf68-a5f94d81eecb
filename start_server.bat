@echo off
chcp 65001 >nul
echo Starting Document Analysis System...
echo.

echo Checking Python environment...
python --version
if %errorlevel% neq 0 (
    echo Error: Python is not installed or not in PATH
    pause
    exit /b 1
)

echo.
echo Installing/updating dependencies...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Starting API server...
echo Server will be available at: http://127.0.0.1:8000
echo Press Ctrl+C to stop the server
echo.

python -m uvicorn src.main:app --host 127.0.0.1 --port 8000 --reload

pause
