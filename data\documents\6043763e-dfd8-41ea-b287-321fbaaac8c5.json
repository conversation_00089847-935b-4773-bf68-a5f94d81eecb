{"doc_id": "6043763e-dfd8-41ea-b287-321fbaaac8c5", "title": "item261_<PERSON>_Sag, <PERSON>, professor of law, artificial intelligence, machine learning, and data science, Emory University School of Law", "text": "  STATEMENT OF DANA RAO, EXECUTIVE VICE PRESIDENT, GENERAL \r\n   COUNSEL, AND CHIEF TRUST OFFICER, ADOBE, INC., SAN JOSE, \r\n                           CALIFORNIA\r\n\r\n    Mr. <PERSON>. Chair <PERSON><PERSON>, Ranking Member <PERSON><PERSON>, and Members of \r\nthe Committee, thank you for the opportunity to testify here \r\ntoday.\r\n    My name is <PERSON>, and I am general counsel, and, as \r\n<PERSON> <PERSON><PERSON> noted, chief trust officer at Adobe. I am happy \r\nto provide you with this secret certificate you need to get \r\nthat title, if you would like, after the hearing.\r\n    Since our founding in 1982, Adobe has pioneered \r\ntransformative technologies in all types of digital creation, \r\nfrom digital documents like PDF to image editing with \r\nPhotoshop. Our products allow our customers who range from \r\naspiring artists to wartime photojournalists, to advertisers \r\nand more, to unleash their creativity, protect their craft, \r\nempower their businesses in a digital world.\r\n    AI is the latest disruptive technology we have been \r\nincorporating into our tools help creators realize their \r\npotential. You have all seen the magic of text to image \r\ngenerative AI. Type in the prompt, cat driving a 1950s \r\nsportscar through the desert, and in seconds you will see \r\nmultiple variations of a cat on a retro road trip appear before \r\nyour eyes.\r\n    We have launched generative AI in our own tools, Adobe \r\nFirefly, and has provided--this proved to be wildly popular \r\nwith our creative professionals and consumers alike. In my \r\nwritten testimony, I explore a comprehensive framework for \r\nresponsible AI development that includes addressing \r\nmisinformation, harmful bias, creative rights, and intellectual \r\nproperty.\r\n    Today, given Adobe's focus and our millions of creative \r\ncustomers and our leadership in AI, I will focus on how the \r\nUnited States can continue to lead the world in AI development \r\nby both supporting the access to data that AI requires and \r\nstrengthening creator rights.\r\n    The question of data access is critical for the development \r\nof AI because AI is only as powerful and as good as the data on \r\nwhich it is trained. Like the human brain, AI learns from the \r\ninformation you give it.\r\n    In the AI's case, the data it is trained on. Training on a \r\nlarger dataset can help ensure your results are more accurate \r\nbecause the AI has more facts to learn from. A larger dataset \r\nwill also help the AI avoid perpetuating harmful biases in its \r\nresults by giving it a wider breadth of experiences from which \r\nit can build its understanding of the world. More data means \r\nbetter answers and fewer biases.\r\n    Given those technical realities, United States and \r\ngovernments should support access to data to ensure that AI \r\ninnovation can flourish accurately and responsibly. However, \r\none of the most important implications of AI's need for data is \r\nthe impact on copyright and creators' rights.\r\n    There are many outstanding questions in this space, \r\nincluding whether creating an AI model, which is a software \r\nprogram, from a set of images, is a permitted fair use. And \r\nwhether that analysis changes if the output of that AI model \r\ncreates an image that is substantially similar to an image on \r\nwhich it is trained.\r\n    These questions will certainly be addressed by courts and \r\nperhaps Congress, and we are prepared to help assist in those \r\ndiscussions. Adobe recognized the potential impact of AI on \r\ncreators and society, and we have taken several steps.\r\n    First, we trained our own generative AI tool, Adobe \r\nFirefly, only on licensed images from our Adobe Stock \r\nCollection, which is a stock photography collection, openly \r\nlicensed content, and works that are in the public domain where \r\nthe copyright has expired. This approach supports creators and \r\ncustomers by training on a dataset that is designed to be \r\ncommercially safe.\r\n    In addition, we are advocating for other steps we can all \r\ntake to strengthen creators' rights. First, we believe creators \r\nshould be able to attach a ``Do Not Train'' tag to their work. \r\nWith industry and Government support, we can ensure AI data \r\ncrawlers will read and respect this tag, giving creators the \r\noption to keep their data out of AI training datasets.\r\n    Second, creators using AI tools want to ensure they can \r\nobtain copyright protection over their work in this new era of \r\nAI-assisted digital creation. An AI output alone may not \r\nreceive copyright protection, but we believe the combination of \r\nhuman expression and AI expression will and should.\r\n    Content editing tools should enable creators to obtain a \r\ncopyright by allowing them to distinguish the AI work from the \r\nhuman work. In my written testimony, I discuss our open \r\nstandards-based technology content credentials, which can help \r\nenable both of these creator protections.\r\n    Finally, even though Adobe has trained its AI on permitted \r\nwork, we understand the concern that an artist can be \r\neconomically dispossessed by an AI trained on their work that \r\ngenerates arts in their style, in the Frank Sinatra example you \r\ngave.\r\n    We believe artists should be protected against this type of \r\neconomic harm, and we propose Congress establish a new Federal \r\nanti-impersonation right that would give artists a right to \r\nenforce against someone intentionally attempting to impersonate \r\ntheir style or likeness.\r\n    Holding people accountable who misuse AI tools is a \r\nsolution we believe goes to the heart of some of the issues our \r\ncustomers have, and this new right would help address that \r\nconcern. The United States has led the world through \r\ntechnological transformations in the past, and we have all \r\nlearned it is important to be proactively responsible to the \r\nimpact of these technologies.\r\n    Pairing innovation with responsible innovation will ensure \r\nthat AI ultimately becomes a transformative and true benefit to \r\nour society. Thank you, Chair Coons, Ranking Member Tillis, and \r\nMembers of the Committee.\r\n    [The prepared statement of Mr. Rao appears as a submission \r\nfor the record.]\r\n    Chair Coons. Thank you, Mr. Rao. Professor.\r\n\r\n    STATEMENT OF MATTHEW SAG, PROFESSOR OF LAW, ARTIFICIAL \r\n    INTELLIGENCE, MACHINE LEARNING, AND DATA SCIENCE, EMORY \r\n           UNIVERSITY SCHOOL OF LAW, ATLANTA, GEORGIA\r\n\r\n    Professor Sag. Chair Coons, Ranking Member Tillis, Members \r\nof the Subcommittee, thank you for the opportunity to testify \r\nhere today. I am a professor of law in AI, machine learning, \r\nand data science at Emory University, where I was hired as part \r\nof Emory's AI Humanity Initiative.\r\n    Although we are still a long way from the science fiction \r\nversion of artificial general intelligence that thinks, feels, \r\nand refuses to open the pod bay doors, recent advances in \r\nmachine learning and artificial intelligence have captured the \r\npublic's attention and apparently lawmakers' interest.\r\n    We now have large language models, or LLMs, that can pass \r\nthe bar exam, carry on a conversation, create new music and new \r\nvisual art. Nonetheless, copyright law does not and should not \r\nrecognize computer systems as authors. Even where an AI \r\nproduces images, text, or music that is indistinguishable from \r\nhuman authored works, it makes no sense to think of a machine \r\nlearning program as the author.\r\n    The Copyright Act rightly reserves copyrights for original \r\nworks of authorship. As the Supreme Court explained long ago in \r\nthe 1884 case of Burrow-Giles Lithographic, authorship entails \r\noriginal, intellectual conception. An AI can't produce a work \r\nthat reflects its own original intellectual conception because \r\nit has none.\r\n    Thus, when AI models produce content with little or no \r\nhuman oversight, there is no copyright in those outputs. \r\nHowever, humans using AI as tools of expression may claim \r\nauthorship if the final form of the work reflects their \r\noriginal intellectual conception in sufficient detail. And I \r\nhave elaborated in my written submissions how this will depend \r\non the circumstances.\r\n    Training generative AI on copyrighted works is usually fair \r\nuse because it falls into the category of non-expressive use. \r\nCourts addressing technologies such as reverse engineering, \r\nsearch engines, and plagiarism detection software have held \r\nthat these non-expressive uses are fair use. These cases \r\nreflect copyright's fundamental distinction between protectable \r\noriginal expression and unprotect-able facts, ideas, and \r\nabstractions.\r\n    Whether training an LLM is in non-expressive use depends \r\nultimately on the outputs of the model. If an LLM is trained \r\nproperly and operated with appropriate safeguards, its outputs \r\nwill not resemble its inputs in a way that would trigger a \r\ncopyright liability. Training such an LLM on copyrighted works \r\nwould thus be justified under current fair use principles.\r\n    It is important to understand that generative AI are not \r\ndesigned to copy original expression. One of the most common \r\nmisconceptions about generative AI is the notion that the \r\ntraining data is somehow copied into the model. Machine \r\nlearning models are influenced by the data. They would be \r\npretty useless without it. But they typically don't copy the \r\ndata in any literal sense.\r\n    So rather than thinking of an LLM as copying the training \r\ndata like a scribe in a monastery, it makes more sense to think \r\nof it as learning from the training data like a student. If an \r\nLLM like GPT3 is working as intended, it doesn't copy the \r\ntraining data at all. The only copying that takes place is when \r\nthe training corpus is assembled and pre-processed, and that is \r\nwhat you need a fair use justification for. Whether a \r\ngenerative AI produces truly new content or simply conjures up \r\nan infringing cut and paste of works in the training data \r\ndepends on how it is trained.\r\n    Accordingly, companies should adopt best practices to \r\nreduce the risk of copyright infringement and other related \r\nharms, and I have elaborated on some of these best practices in \r\nmy written submission. Failure to adopt best practices may \r\npotentially undermine claims of fair use.\r\n    Generative AI does not, in my opinion, require a major \r\noverhaul of the U.S. copyright system at this time.\r\n    If Congress is considering new legislation in relation to \r\nAI and copyright, that legislation should be targeted at \r\nclarifying the application of existing fair use jurisprudence, \r\nnot overhauling it.\r\n    Israel, Singapore, and South Korea have recently \r\nincorporated fair use into their copyright statutes because \r\nthese countries recognize that the flexibility of the fair use \r\ndoctrine gives U.S. companies and U.S. researchers a \r\nsignificant competitive advantage.\r\n    Several other jurisdictions, most notably Japan, the United \r\nKingdom, and the European Union, have specifically adopted \r\nexemptions for text data mining that allow use of copyrighted \r\nworks as training for machine learning and other purposes.\r\n    Copyright law should encourage the developers of generative \r\nAI to act responsibly. However, if our laws become overly \r\nrestrictive, then corporations and researchers will simply move \r\nkey aspects of technology development overseas to our \r\ncompetitors.\r\n    Thank you very much.\r\n    [The prepared statement of Professor Sag appears as a \r\nsubmission for the record.]\r\n    Chair Coons. Thank you, Professor. Ms. Ortiz.\r\n\r\n      STATEMENT OF KARLA ORTIZ, CONCEPT ARTIST, ILLUSTRA-\r\n        TOR, AND FINE ARTIST, SAN FRANCISCO, CALIFORNIA\r\n\r\n    Ms. Ortiz. Yes. Chairman Coons, Ranking Member Tillis, and \r\nesteemed Members of the Committee, it is an honor to testify \r\nbefore you today about AI and copyright. My name is Karla \r\nOrtiz. I am a concept artist, illustrator, and fine artist, and \r\nyou may not know my name, but you know my work.\r\n    My paintings have shaped the worlds of blockbuster Marvel \r\nfilms and TV shows, including ``Guardians of the Galaxy 3,'' \r\n``Black Panther,'' ``Loki,'' you know, but specifically, the \r\none I am most happiest of is that I, my work helped shape the \r\nlook of Doctor Strange in the first ``Doctor Strange'' movie.\r\n    I have to brag about that a little bit, sir. I love what I \r\ndo. I love my craft. Artists train their entire lives to be \r\nable to bring the imaginary to life. All of us who engage in \r\nthis craft love every little bit of it. Through hard work, \r\nsupport of loved ones, and dedication, I have been able to make \r\na good living from my craft via the entertainment industry, an \r\nindustry that thrives when artists' rights to consent, credit, \r\nand compensation are respected.\r\n    I have never worried about my future as an artist until \r\nnow. Generative AI is unlike any other technology that has come \r\nbefore. It is a technology that uniquely consumes and exploits \r\nthe hard work, creativity, and innovation of others. No other \r\ntool is like this. What I found, when first researching AI, \r\nhorrified me.\r\n    I found that almost the entirety of my work, the work of \r\nalmost every artist I know, and the work of hundreds of \r\nthousands of artists had been taken without our consent, \r\ncredit, or compensation. These works were stolen and used to \r\ntrain for profit technologies with datasets that contain \r\nbillions of image and text data pairs.\r\n    Through my research, I learned many AI companies gather \r\ncopyrighted training data by relying on a practice called data \r\nlaundering. This is where a company outsources its data \r\ncollection to a third party under the pretext of research to \r\nthen immediately use commercially. I found these companies use \r\nbig terms like ``publicly available data'' or ``openly licensed \r\ncontent'' to disguise their extensive reliance on copyrighted \r\nworks.\r\n    No matter what they are saying, these models are illegally \r\ntrained on copyrighted works. To add even more insult to \r\ninjury, I found that these for-profit companies were not only \r\npermitting users to use our full names to generate imagery but \r\nencouraging it. For example, Polish artist Frederic Koski had \r\nhad his name used as a prompt in AI products over 400,000 \r\ntimes, and those are the lower end of the estimate.\r\n    My own name, Karla Ortiz, has also been used by these \r\ncompanies thousands of times. Never once did I give consent. \r\nNever once have I gotten credit. Never once have I gotten \r\ncompensation. It should come as no surprise that major \r\nproductions are replacing artists with generative AI.\r\n    Goldman Sachs estimates that generative AI will diminish or \r\noutright destroy approximately 300 million full-time jobs \r\nworldwide. As Ranking Member Tillis mentioned earlier, \r\ncopyright-reliant industries alone contribute $1.8 trillion \r\nvalue to the U.S. GDP, accounting for 7.76 percent of the \r\nentire U.S. economy. This is an industry that employs 9.6 \r\nmillion American workers alone.\r\n    The game plan is simple, to go as fast as possible, to \r\ncreate mesmerizing tales of progress, and to normalize the \r\nexploitation of artists as quickly as possible. They hope when \r\nwe catch our breath, it will be too late to right the wrongs, \r\nand exploiting Americans will become an accepted way of doing \r\nthings.\r\n    But that game can't succeed as we are here now, giving this \r\nthe urgency it so desperately deserves. Congress should act to \r\nensure what we call the 3Cs and a T: consent, credit, \r\ncompensation, and transparency.\r\n    The work of artists like myself were taken without our \r\nconsent, credit, nor compensation, and then used to compete \r\nwith us directly in our own markets--an outrageous act that \r\nunder any other context would immediately be seen as unfair, \r\nimmoral, and illegal.\r\n    Senators, there is a fundamental fairness issue here. I am \r\nasking Congress to address this by enacting laws that require \r\nthese companies to obtain consent, give credit, pay \r\ncompensation, and be transparent. Thank you.", "metadata": {"original_filename": "item261_<PERSON>_Sag, <PERSON>, professor of law, artificial intelligence, machine learning, and data science, Emory University School of Law.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:35:14.139079", "updated_at": "2025-08-28T21:35:14.139079", "word_count": 15974}