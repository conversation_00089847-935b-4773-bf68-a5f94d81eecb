#!/usr/bin/env python3
"""
🚀 高级分析API路由 - PANTOS升级版分析接口
集成AI模型编排和新分析模块
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field

# 导入现有模块
from src.models.schemas import DocumentResponse, AnalysisRequest
from src.services.document_service import DocumentService, get_document_service
from src.services.analysis_service import AnalysisService, get_analysis_service

# 导入新模块
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))

# 尝试导入新模块，如果失败则使用模拟功能
try:
    from infrastructure.api_gateway.model_orchestrator import (
        model_orchestrator, AnalysisRequest as OrchestratorRequest, 
        TaskComplexity, ModelType
    )
    MODEL_ORCHESTRATOR_AVAILABLE = True
except ImportError:
    MODEL_ORCHESTRATOR_AVAILABLE = False
    # 创建模拟类
    class TaskComplexity:
        MEDIUM = "medium"
    
    class OrchestratorRequest:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)

try:
    from services.narrative_analyzer.temporal_framing import analyze_temporal_framing
    TEMPORAL_FRAMING_AVAILABLE = True
except ImportError:
    TEMPORAL_FRAMING_AVAILABLE = False

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v2/analysis", tags=["高级分析"])

# 批量作业状态管理
batch_advanced_jobs = {}

# 并发控制配置
DEFAULT_ADVANCED_CONCURRENCY = 3  # 默认并发数
MAX_ADVANCED_CONCURRENCY = 10     # 最大并发数

class AdvancedAnalysisRequest(BaseModel):
    """高级分析请求模型"""
    doc_id: str = Field(..., description="文档ID")
    tasks: List[str] = Field(..., description="分析任务列表")
    priority: int = Field(default=0, description="任务优先级")
    use_ai_orchestrator: bool = Field(default=True, description="是否使用AI模型编排")
    enable_new_tasks: bool = Field(default=True, description="是否启用新分析任务")
    analysis_config: Optional[Dict[str, Any]] = Field(default=None, description="分析配置")

class AdvancedAnalysisResponse(BaseModel):
    """高级分析响应模型"""
    success: bool
    analysis_id: str
    doc_id: str
    results: Dict[str, Any]
    orchestrator_info: Optional[Dict[str, Any]] = None
    performance_metrics: Dict[str, Any]
    timestamp: str

class TaskCapability(BaseModel):
    """任务能力模型"""
    task_id: str
    task_name: str
    description: str
    version: str
    is_available: bool
    estimated_time: float
    supported_models: List[str]

class BatchAdvancedAnalysisRequest(BaseModel):
    """批量高级分析请求模型"""
    requests: List[AdvancedAnalysisRequest] = Field(..., description="高级分析请求列表")
    concurrency: Optional[int] = Field(default=3, ge=1, le=10, description="并发处理数量，默认3，最大10")
    batch_name: Optional[str] = Field(default=None, description="批次名称")
    priority: int = Field(default=0, description="批次优先级")

class BatchAdvancedAnalysisResponse(BaseModel):
    """批量高级分析响应模型"""
    success: bool
    batch_id: str
    total_tasks: int
    status: str = Field(default="pending", description="批次状态: pending, processing, completed, failed")
    completed: int = Field(default=0, description="已完成任务数")
    failed: int = Field(default=0, description="失败任务数")
    processing: int = Field(default=0, description="正在处理任务数")
    progress_percentage: float = Field(default=0.0, description="完成百分比")
    concurrency: int = Field(default=3, description="并发数量")
    estimated_remaining_time: Optional[int] = Field(default=None, description="预估剩余时间（秒）")
    current_processing_docs: List[str] = Field(default=[], description="当前正在处理的文档")
    created_at: datetime
    updated_at: datetime
    results: Optional[Dict[str, Any]] = Field(default=None, description="批次结果")
    message: str = Field(default="", description="状态消息")

@router.get("/capabilities", response_model=List[TaskCapability])
async def get_analysis_capabilities():
    """获取系统分析能力清单"""
    
    capabilities = [
        # 原有任务
        TaskCapability(
            task_id="actor_relation",
            task_name="行为者关系分析",
            description="识别文档中的行为者及其相互关系",
            version="v1.0",
            is_available=True,
            estimated_time=3.0,
            supported_models=["zhipu_glm4_5", "openai_gpt4"]
        ),
        TaskCapability(
            task_id="role_framing",
            task_name="角色塑造检测",
            description="检测英雄/受害者/反派的角色塑造模式",
            version="v1.0",
            is_available=True,
            estimated_time=2.5,
            supported_models=["zhipu_glm4_5", "openai_gpt4"]
        ),
        TaskCapability(
            task_id="problem_scope",
            task_name="问题范围策略",
            description="识别问题扩大化或缩小化策略",
            version="v1.0",
            is_available=True,
            estimated_time=2.8,
            supported_models=["zhipu_glm4_5", "openai_gpt4"]
        ),
        TaskCapability(
            task_id="causal_mechanism",
            task_name="因果机制分析",
            description="分析问题的因果归因方式",
            version="v1.0",
            is_available=True,
            estimated_time=3.2,
            supported_models=["zhipu_glm4_5", "openai_gpt4"]
        ),
        
        # 新增任务
        TaskCapability(
            task_id="temporal_framing",
            task_name="时间框架分析",
            description="分析政策叙事中的时间维度策略",
            version="v1.0",
            is_available=TEMPORAL_FRAMING_AVAILABLE,
            estimated_time=2.0,
            supported_models=["zhipu_glm4_5", "specialized_nlp"]
        ),
        TaskCapability(
            task_id="spatial_framing",
            task_name="空间框架分析",
            description="分析地理边界和空间叙事构建",
            version="v1.0",
            is_available=False,  # 开发中
            estimated_time=3.5,
            supported_models=["zhipu_glm4_5", "multimodal_model"]
        ),
        TaskCapability(
            task_id="discourse_coalition",
            task_name="话语联盟分析",
            description="识别叙事联盟和话语竞争模式",
            version="v1.0",
            is_available=False,  # 开发中
            estimated_time=4.0,
            supported_models=["zhipu_glm4_5", "network_model"]
        )
    ]
    
    return capabilities

@router.post("/advanced", response_model=AdvancedAnalysisResponse)
async def run_advanced_analysis(
    request: AdvancedAnalysisRequest,
    background_tasks: BackgroundTasks,
    document_service: DocumentService = Depends(get_document_service),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """执行高级智能分析"""
    
    start_time = datetime.now()
    analysis_id = f"adv_analysis_{int(start_time.timestamp())}"
    
    logger.info(f"开始高级分析: {analysis_id}, 文档: {request.doc_id}")
    
    try:
        # 1. 获取文档
        document = await document_service.get_document(request.doc_id)
        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        # 2. 分析任务分类和路由
        traditional_tasks = []
        new_tasks = []
        
        for task in request.tasks:
            if task in ["actor_relation", "role_framing", "problem_scope", "causal_mechanism"]:
                traditional_tasks.append(task)
            elif task in ["temporal_framing", "spatial_framing", "discourse_coalition"]:
                new_tasks.append(task)
            else:
                logger.warning(f"未知任务类型: {task}")
        
        results = {}
        orchestrator_info = None
        
        # 3. 处理传统任务
        if traditional_tasks:
            if request.use_ai_orchestrator and MODEL_ORCHESTRATOR_AVAILABLE:
                # 使用AI模型编排器
                orchestrator_results = await _process_with_orchestrator(
                    document, traditional_tasks, request
                )
                results.update(orchestrator_results["results"])
                orchestrator_info = orchestrator_results["orchestrator_info"]
            else:
                # 使用原有分析服务
                traditional_results = await _process_traditional_tasks(
                    document, traditional_tasks, analysis_service
                )
                results.update(traditional_results)
        
        # 4. 处理新任务
        if new_tasks and request.enable_new_tasks:
            new_results = await _process_new_tasks(document, new_tasks)
            results.update(new_results)
        
        # 5. 计算性能指标
        execution_time = (datetime.now() - start_time).total_seconds()
        performance_metrics = {
            "total_execution_time": execution_time,
            "tasks_completed": len(results),
            "traditional_tasks": len(traditional_tasks),
            "new_tasks": len([task for task in new_tasks if task in results]),
            "orchestrator_used": request.use_ai_orchestrator and orchestrator_info is not None
        }
        
        # 6. 后台保存结果
        background_tasks.add_task(
            _save_advanced_analysis_results,
            analysis_id, request.doc_id, results, performance_metrics
        )
        
        logger.info(f"高级分析完成: {analysis_id}, 用时: {execution_time:.2f}秒")
        
        return AdvancedAnalysisResponse(
            success=True,
            analysis_id=analysis_id,
            doc_id=request.doc_id,
            results=results,
            orchestrator_info=orchestrator_info,
            performance_metrics=performance_metrics,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"高级分析失败: {e}")
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return AdvancedAnalysisResponse(
            success=False,
            analysis_id=analysis_id,
            doc_id=request.doc_id,
            results={"error": str(e)},
            performance_metrics={
                "total_execution_time": execution_time,
                "error": True
            },
            timestamp=datetime.now().isoformat()
        )

@router.get("/orchestrator/status")
async def get_orchestrator_status():
    """获取AI模型编排器状态"""
    
    if not MODEL_ORCHESTRATOR_AVAILABLE:
        raise HTTPException(status_code=503, detail="AI模型编排器不可用")
    
    try:
        status = await model_orchestrator.get_system_status()
        return {
            "success": True,
            "status": status,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取编排器状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/orchestrator/analytics")
async def get_orchestrator_analytics(
    task_type: Optional[str] = None,
    hours: int = 24
):
    """获取AI模型编排器性能分析"""
    
    if not MODEL_ORCHESTRATOR_AVAILABLE:
        raise HTTPException(status_code=503, detail="AI模型编排器不可用")
    
    try:
        analytics = await model_orchestrator.get_performance_analytics(
            task_type=task_type,
            time_range_hours=hours
        )
        return {
            "success": True,
            "analytics": analytics,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取编排器分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/batch-advanced", response_model=BatchAdvancedAnalysisResponse)
async def run_batch_advanced_analysis(
    request: BatchAdvancedAnalysisRequest,
    background_tasks: BackgroundTasks,
    document_service: DocumentService = Depends(get_document_service),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """批量高级分析（支持并发控制）"""

    batch_id = f"batch_adv_{int(datetime.now().timestamp())}"
    concurrency = min(request.concurrency or DEFAULT_ADVANCED_CONCURRENCY, MAX_ADVANCED_CONCURRENCY)

    logger.info(f"开始批量高级分析: {batch_id}, 任务数: {len(request.requests)}, 并发数: {concurrency}")

    # 验证所有文档是否存在
    for req in request.requests:
        doc = await document_service.get_document(req.doc_id)
        if not doc:
            raise HTTPException(
                status_code=404,
                detail=f"文档 {req.doc_id} 不存在"
            )

    # 创建批量作业状态
    batch_advanced_jobs[batch_id] = {
        "status": "pending",
        "total": len(request.requests),
        "completed": 0,
        "failed": 0,
        "processing": 0,
        "progress_percentage": 0.0,
        "concurrency": concurrency,
        "batch_name": request.batch_name or f"批量高级分析_{batch_id}",
        "priority": request.priority,
        "requests": request.requests,
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
        "start_time": None,
        "current_processing_docs": [],
        "results": {},
        "estimated_remaining_time": None
    }

    # 启动后台批量处理
    background_tasks.add_task(
        process_batch_advanced_job,
        batch_id,
        request,
        document_service,
        analysis_service
    )

    return BatchAdvancedAnalysisResponse(
        success=True,
        batch_id=batch_id,
        total_tasks=len(request.requests),
        status="pending",
        concurrency=concurrency,
        created_at=batch_advanced_jobs[batch_id]["created_at"],
        updated_at=batch_advanced_jobs[batch_id]["updated_at"],
        message="批量高级分析已启动，请稍后查询结果"
    )

@router.get("/batch-advanced/status/{batch_id}", response_model=BatchAdvancedAnalysisResponse)
async def get_batch_advanced_status(batch_id: str):
    """获取批量高级分析状态"""

    if batch_id not in batch_advanced_jobs:
        raise HTTPException(
            status_code=404,
            detail=f"批量作业 {batch_id} 不存在"
        )

    job = batch_advanced_jobs[batch_id]

    # 计算预估剩余时间
    estimated_remaining_time = None
    if job["start_time"] and job["completed"] > 0:
        import time
        elapsed_time = time.time() - job["start_time"]
        avg_time_per_task = elapsed_time / job["completed"]
        remaining_tasks = job["total"] - job["completed"]
        estimated_remaining_time = int(avg_time_per_task * remaining_tasks)

    return BatchAdvancedAnalysisResponse(
        success=True,
        batch_id=batch_id,
        total_tasks=job["total"],
        status=job["status"],
        completed=job["completed"],
        failed=job.get("failed", 0),
        processing=job.get("processing", 0),
        progress_percentage=job.get("progress_percentage", 0.0),
        concurrency=job.get("concurrency", DEFAULT_ADVANCED_CONCURRENCY),
        estimated_remaining_time=estimated_remaining_time,
        current_processing_docs=job.get("current_processing_docs", []),
        created_at=job["created_at"],
        updated_at=job.get("updated_at", job["created_at"]),
        results=job.get("results") if job["status"] in ["completed", "failed"] else None,
        message=f"批量作业状态: {job['status']}"
    )

@router.get("/batch-advanced/results/{batch_id}")
async def get_batch_advanced_results(batch_id: str):
    """获取批量高级分析结果"""

    if batch_id not in batch_advanced_jobs:
        raise HTTPException(
            status_code=404,
            detail=f"批量作业 {batch_id} 不存在"
        )

    job = batch_advanced_jobs[batch_id]

    if job["status"] not in ["completed", "failed"]:
        raise HTTPException(
            status_code=400,
            detail=f"批量作业 {batch_id} 尚未完成，当前状态: {job['status']}"
        )

    return {
        "batch_id": batch_id,
        "status": job["status"],
        "total_tasks": job["total"],
        "completed": job["completed"],
        "failed": job["failed"],
        "results": job["results"],
        "created_at": job["created_at"],
        "updated_at": job["updated_at"]
    }

async def process_batch_advanced_job(
    batch_id: str,
    request: BatchAdvancedAnalysisRequest,
    document_service: DocumentService,
    analysis_service: AnalysisService
):
    """处理批量高级分析作业（支持并发）"""
    import time

    try:
        logger.info(f"开始处理批量高级分析作业: {batch_id}，并发数: {request.concurrency}")
        batch_advanced_jobs[batch_id]["status"] = "processing"
        batch_advanced_jobs[batch_id]["start_time"] = time.time()
        batch_advanced_jobs[batch_id]["updated_at"] = datetime.now()

        # 创建信号量控制并发数
        concurrency = min(request.concurrency or DEFAULT_ADVANCED_CONCURRENCY, MAX_ADVANCED_CONCURRENCY)
        semaphore = asyncio.Semaphore(concurrency)

        # 创建所有任务
        tasks = []
        for req in request.requests:
            task = process_single_advanced_analysis(
                semaphore, batch_id, req, document_service, analysis_service
            )
            tasks.append(task)

        # 并发执行所有任务
        await asyncio.gather(*tasks, return_exceptions=True)

        # 更新作业状态
        batch_advanced_jobs[batch_id]["status"] = "completed"
        batch_advanced_jobs[batch_id]["updated_at"] = datetime.now()
        batch_advanced_jobs[batch_id]["processing"] = 0
        batch_advanced_jobs[batch_id]["current_processing_docs"] = []
        batch_advanced_jobs[batch_id]["progress_percentage"] = 100.0
        logger.info(f"批量高级分析作业 {batch_id} 已完成")

    except Exception as e:
        logger.error(f"处理批量高级分析作业 {batch_id} 失败: {str(e)}")
        batch_advanced_jobs[batch_id]["status"] = "failed"
        batch_advanced_jobs[batch_id]["updated_at"] = datetime.now()
        batch_advanced_jobs[batch_id]["processing"] = 0
        batch_advanced_jobs[batch_id]["current_processing_docs"] = []
        batch_advanced_jobs[batch_id]["error"] = str(e)

async def process_single_advanced_analysis(
    semaphore: asyncio.Semaphore,
    batch_id: str,
    request: AdvancedAnalysisRequest,
    document_service: DocumentService,
    analysis_service: AnalysisService
):
    """处理单个高级分析任务（并发安全）"""
    async with semaphore:
        try:
            # 更新正在处理的文档列表
            batch_advanced_jobs[batch_id]["processing"] += 1
            batch_advanced_jobs[batch_id]["current_processing_docs"].append(request.doc_id)
            batch_advanced_jobs[batch_id]["updated_at"] = datetime.now()

            logger.info(f"开始处理高级分析文档 {request.doc_id}")

            # 获取文档
            document = await document_service.get_document(request.doc_id)
            if not document:
                logger.warning(f"文档 {request.doc_id} 不存在，跳过")
                batch_advanced_jobs[batch_id]["results"][request.doc_id] = {
                    "success": False,
                    "error": "文档不存在",
                    "doc_id": request.doc_id
                }
                batch_advanced_jobs[batch_id]["failed"] += 1
                return

            # 执行高级分析
            start_time = datetime.now()
            analysis_id = f"adv_analysis_{int(start_time.timestamp())}_{request.doc_id}"

            # 模拟高级分析处理
            await asyncio.sleep(2)  # 模拟分析耗时

            # 这里应该调用实际的高级分析逻辑
            # 暂时返回模拟结果
            results = {
                "analysis_id": analysis_id,
                "doc_id": request.doc_id,
                "tasks": request.tasks,
                "results": {
                    task: f"高级分析结果 - 任务: {task}, 文档: {document.title}"
                    for task in request.tasks
                },
                "performance_metrics": {
                    "processing_time": 2.0,
                    "model_used": "zhipu_glm4_5",
                    "tokens_used": 1500
                }
            }

            # 保存结果到批处理状态
            batch_advanced_jobs[batch_id]["results"][request.doc_id] = {
                "success": True,
                "analysis_id": analysis_id,
                "doc_id": request.doc_id,
                "results": results,
                "timestamp": datetime.now().isoformat()
            }

            # 更新作业状态
            batch_advanced_jobs[batch_id]["completed"] += 1
            batch_advanced_jobs[batch_id]["progress_percentage"] = (
                batch_advanced_jobs[batch_id]["completed"] / batch_advanced_jobs[batch_id]["total"]
            ) * 100

            logger.info(f"高级分析文档 {request.doc_id} 处理完成 "
                       f"({batch_advanced_jobs[batch_id]['completed']}/{batch_advanced_jobs[batch_id]['total']})")

        except Exception as e:
            logger.error(f"处理高级分析文档 {request.doc_id} 失败: {str(e)}")
            batch_advanced_jobs[batch_id]["results"][request.doc_id] = {
                "success": False,
                "error": str(e),
                "doc_id": request.doc_id,
                "timestamp": datetime.now().isoformat()
            }
            batch_advanced_jobs[batch_id]["failed"] += 1

        finally:
            # 从正在处理的文档列表中移除
            batch_advanced_jobs[batch_id]["processing"] -= 1
            if request.doc_id in batch_advanced_jobs[batch_id]["current_processing_docs"]:
                batch_advanced_jobs[batch_id]["current_processing_docs"].remove(request.doc_id)
            batch_advanced_jobs[batch_id]["updated_at"] = datetime.now()

async def _process_with_orchestrator(
    document: DocumentResponse, 
    tasks: List[str], 
    request: AdvancedAnalysisRequest
) -> Dict[str, Any]:
    """使用AI模型编排器处理任务"""
    
    if not MODEL_ORCHESTRATOR_AVAILABLE:
        # 如果编排器不可用，回退到传统分析
        logger.warning("AI模型编排器不可用，回退到传统分析服务")
        return {
            "results": {},
            "orchestrator_info": {
                "error": "AI模型编排器不可用",
                "fallback": "使用传统分析服务"
            }
        }
    
    results = {}
    orchestrator_metrics = []
    
    for task in tasks:
        # 创建编排器请求
        orchestrator_request = OrchestratorRequest(
            task_type=task,
            content=document.text,
            document_id=document.doc_id,
            priority=request.priority,
            complexity=TaskComplexity.MEDIUM,
            requires_multimodal=False
        )
        
        # 通过编排器执行
        orchestrator_result = await model_orchestrator.route_analysis_request(
            orchestrator_request
        )
        
        if orchestrator_result["success"]:
            # 这里需要调用实际的分析逻辑
            # 目前使用模拟结果
            results[task] = {
                "analysis_data": orchestrator_result["result"]["analysis_data"],
                "model_used": orchestrator_result["model_used"],
                "confidence_score": orchestrator_result["result"]["confidence_score"]
            }
        else:
            results[task] = {
                "error": orchestrator_result["error"]
            }
        
        orchestrator_metrics.append({
            "task": task,
            "model_used": orchestrator_result.get("model_used"),
            "execution_time": orchestrator_result.get("execution_time"),
            "cost_estimate": orchestrator_result.get("cost_estimate"),
            "success": orchestrator_result["success"]
        })
    
    return {
        "results": results,
        "orchestrator_info": {
            "metrics": orchestrator_metrics,
            "total_cost": sum(m.get("cost_estimate", 0) for m in orchestrator_metrics),
            "avg_execution_time": sum(m.get("execution_time", 0) for m in orchestrator_metrics) / len(orchestrator_metrics)
        }
    }

async def _process_traditional_tasks(
    document: DocumentResponse,
    tasks: List[str],
    analysis_service: AnalysisService
) -> Dict[str, Any]:
    """处理传统分析任务"""
    
    results = {}
    
    # 构造传统分析请求
    analysis_request = AnalysisRequest(
        doc_id=document.doc_id,
        tasks=tasks
    )
    
    # 使用现有分析服务
    analysis_results = await analysis_service.analyze_document(
        document, analysis_request
    )
    
    # 转换结果格式
    for task in tasks:
        if task in analysis_results.results:
            results[task] = analysis_results.results[task]
    
    return results

async def _process_new_tasks(document: DocumentResponse, tasks: List[str]) -> Dict[str, Any]:
    """处理新分析任务"""
    
    results = {}
    
    for task in tasks:
        try:
            if task == "temporal_framing":
                if TEMPORAL_FRAMING_AVAILABLE:
                    # 调用时间框架分析
                    document_dict = {
                        "doc_id": document.doc_id,
                        "title": document.title,
                        "text": document.text
                    }
                    result = await analyze_temporal_framing(document_dict)
                    results[task] = result
                else:
                    results[task] = {
                        "status": "unavailable",
                        "message": "时间框架分析模块不可用"
                    }
                    
            elif task == "spatial_framing":
                # 空间框架分析 - 开发中
                results[task] = {
                    "status": "developing",
                    "message": "空间框架分析功能正在开发中"
                }
                
            elif task == "discourse_coalition":
                # 话语联盟分析 - 开发中
                results[task] = {
                    "status": "developing", 
                    "message": "话语联盟分析功能正在开发中"
                }
                
        except Exception as e:
            logger.error(f"新任务 {task} 执行失败: {e}")
            results[task] = {
                "error": str(e)
            }
    
    return results

async def _save_advanced_analysis_results(
    analysis_id: str,
    doc_id: str, 
    results: Dict[str, Any],
    performance_metrics: Dict[str, Any]
):
    """保存高级分析结果"""
    
    try:
        # 这里应该保存到数据库或文件系统
        # 目前使用日志记录
        logger.info(f"保存高级分析结果: {analysis_id}")
        logger.debug(f"结果数据: {results}")
        logger.debug(f"性能指标: {performance_metrics}")
        
        # 可以集成到现有的结果保存逻辑中
        # await analysis_service.save_analysis_results(...)
        
    except Exception as e:
        logger.error(f"保存高级分析结果失败: {e}")

async def _save_batch_results(batch_id: str, results: List[Dict]):
    """保存批量分析结果"""
    
    try:
        logger.info(f"保存批量分析结果: {batch_id}, 结果数: {len(results)}")
        # 这里应该保存到数据库
        
    except Exception as e:
        logger.error(f"保存批量结果失败: {e}")

# 测试端点
@router.get("/test/temporal-framing/{doc_id}")
async def test_temporal_framing(
    doc_id: str,
    document_service: DocumentService = Depends(get_document_service)
):
    """测试时间框架分析功能"""
    
    if not TEMPORAL_FRAMING_AVAILABLE:
        raise HTTPException(status_code=503, detail="时间框架分析模块不可用")
    
    try:
        # 获取文档
        document = await document_service.get_document(doc_id)
        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        # 执行时间框架分析
        document_dict = {
            "doc_id": document.doc_id,
            "title": document.title,
            "text": document.text
        }
        
        result = await analyze_temporal_framing(document_dict)
        
        return {
            "success": True,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"时间框架分析测试失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
