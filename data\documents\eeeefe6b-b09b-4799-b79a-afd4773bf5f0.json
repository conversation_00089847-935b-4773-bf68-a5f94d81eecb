{"doc_id": "eeeefe6b-b09b-4799-b79a-afd4773bf5f0", "title": "item075_US_Advancing Cooperative AI Governance at the 2023 G7 Summit", "text": "Advancing Cooperative AI Governance at the 2023 G7 Summit\r\nWorkforce Challenges and the Potential of AI Advanced economies face major workforce challenges, and AI’s ability to increase labor-force productivity can contribute to future economic growth. For the past century, the global economy has been nurtured by a growing workforce and increasing labor productivity. However, this is quickly changing. Advanced economies in particular face a major, multi-decade challenge of declining populations and labor force participation rates. Europe’s working age population is expected to decrease by 4 percent by the end of the decade.3 Japan’s population, the oldest in the world, fell by 0.57 percent from 2021 to 2022, its largest decline since comparable demographic data was made available.4 Data gathered by the United Nations and Pew Research Center projects a declining population in G7 countries through the end of the twenty-first century.5 In addition to the problem of declining birth rates, the Covid-19 pandemic has caused labor shortages to soar to historic heights. As of February 2023, U.S. businesses were seeking to fill over 9.9 million vacancies.6 For policymakers, fostering labor-force productivity has become increasingly important to supporting an aging population while maintaining non-inflationary economic growth. Automation will be critical in meeting this challenge, as various AI tools can be developed to support worker productivity, such as streamlining rote tasks, increasing access to information, and presenting data in a digestible way. AI is a cross-cutting and general-purpose technology that will impact growth, innovation, and productivity in nearly every industry for decades to come. A report published by the World Economic Forum predicts up to a 40 percent increase in labor productivity from AI in developed countries by 2035.7 Firm-level data supports this conclusion. A study of French companies found that automation adoption is associated with a 20 percent 5  |  Gregory C. Allen and Akhil Thadaniincrease in output along with expanded productivity and faster growth.8 A study by economists at Carnegie Mellon University found that filing an AI patent was associated with 25 percent faster employment growth and 40 percent faster revenue growth, as well as increased output per worker.9 The AI opportunity presents an inflection point to fill the gaps in an aging industrial workforce, boost productivity, and drive down inflation.10 Recent advancements in generative AI models that can produce novel text, images, videos, and sounds have been particularly significant in their ability to open new avenues for growth. The eruption of these models has largely occurred in less than a year, demonstrating the rapid pace of such technological innovation. In April 2022, San Francisco–based OpenAI released its text-to-image generating DALL-E 2 model, which was followed by London-based Stability AI’s Stable Diffusion image generator later in the summer. These models were the f irst generative AI technologies to gain broad attention, with millions of users signing up within months of release.11 November 2022 saw the introduction of OpenAI’s text-generating ChatGPT. Not only has ChatGPT become the most widely known generative AI technology, but it has become the fastest-growing consumer application in history, with an estimated 100 million monthly users only two months after launch.12 Generative AI models have begun to demonstrate their ability to increase productivity across sectors. This is especially true for human-AI partnerships, where new technologies are used to complement existing human or traditional software capabilities in ways that offload monotonous or data-intensive work. GitHub’s Copilot, for example, has been used to suggest code for programmers, while the London law firm Allen & Overy has recently introduced a generative AI model to help speed up the process of drafting corporate law documents.13 The newest version of OpenAI’s ChatGPT system, based on the GPT-4 model, is capable of passing a wide variety of professional and academic exams, including the Uniform Bar Exam, a professional exam for lawyers in the United States.14 Overall, research suggests that by 2025 generative AI could be complimenting a much wider variety of work, with the potential for 30 percent of novel drugs and materials being discovered using generative AI techniques.15 Maximizing the opportunity for AI also involves a careful consideration of the risks and potential trade-offs involved. AI can boost global prosperity by augmenting human productivity and creativity. However, the transition to an AI future, if managed poorly, can also displace entire industries and increase socioeconomic disparity.16 If the widespread adoption of AI is to meaningfully tackle labor force considerations, it must be implemented in a human-centric fashion that addresses concerns over talent displacement, harms, and inclusion. Where possible, efforts to integrate AI technologies in different industries should not focus on replacing workers with AI but instead aim to increase worker productivity by giving them more tools. AI and automation are changing the nature of work, and the demand for technology-oriented and emotions-based skills is projected to continue to grow.17 Advantages of Regulating AI Well-designed AI regulation can both speed AI adoption and mitigate AI risks. Public and private organizations are increasingly embedding AI into their processes and products, but legitimate concerns about ethical, safety, and liability risks have restrained adoption. In industry surveys conducted by the Brookings Institution, managers responded that their intent to adopt AI systems was partially offset by concerns over challenges with AI safety and responsible implementation.18 McKinsey’s 2021 state of AI report found that organizations rank explainability, privacy, and equity and fairness among the top f ive risks for AI management.19 6  |  Advancing Cooperative AI GovernancePolicymakers around the world are working toward necessary and inevitable AI regulation and guidance that support responsible innovation while minimizing the potential harmful outcomes of AI systems through governance measures and safeguards. For example, the European Union is drafting its AI Act; the U.S. National Institute of Standards and Technology (NIST) published its highly anticipated AI Risk Management Framework in 2023; the United Kingdom recently released the first draft of its approach to AI regulation; and Japan is governing AI through its vision for “Society 5.0.”20 CSIS has tracked a nearly sixfold increase in the number of AI governance initiatives by G7 governments over the last five years. Well-designed regulatory frameworks should focus on risk-based governance of AI systems, advance good governance, and support voluntary standards. Such standards should outline context-specific best practices, clarify requirements and liabilities, and reduce uncertainty for companies seeking to responsibly adopt AI.21 Therefore, promoting the role of AI as a key method of bolstering labor force productivity and economic growth requires designing regulation to mitigate its risks and promote research, development, and adoption.22 Government-supported standards and regulations, created in cooperation with industry and civil society, would assist organizations seeking to adopt AI by creating clear requirements to maximize for principles of responsible use, such as fairness, transparency, bias, and accountability. This is a pressing need. Among businesses that adopt AI, 84 percent believe responsible AI (RAI) to be a top management priority, but only 24 percent reported that their organization had developed a mature RAI program.23 Mature RAI mechanisms reduce the costs and reputational risks associated with ethical and safety failures of AI. Having an RAI program in place helps firms identify AI governance gaps and address AI system failures before they become compounded through scaling efforts. Work is needed to capture existing AI governance best practices to inform a mature and widely adopted set of standards. This can help reduce the cost and complexity of AI governance. Business leaders are recognizing the economic benefits of implementing robust AI governance, including improved brand differentiation, accelerated innovation, increased customer retention, greater returns on investment, and better preparedness for emerging regulation.24 McKinsey found greater returns on AI investment for organizations that focused on measures to mitigate AI risk, such as ensuring data quality, monitoring pre- and post-deployment model performance, and explaining AI decisionmaking.25 Creating an RAI strategy within organizations takes substantial resources, expertise, and foresight. RAI is also a relatively new term, and its definition and requirements are sometimes inconsistent between, or even within, f irms.26 Regulatory frameworks can help provide the necessary requirements, guidance, and enforcement to speed the ethical development and deployment of AI. This is an area where risk-based, context-specific, and f lexible approaches to regulation can improve safety and accelerate adoption simultaneously. Benefits of Interoperable Regulation The benefits of new technology take time to proliferate, and interoperable regulation is necessary to maintain and grow the pace of beneficial AI development and adoption. Innovation alone is rarely enough to create significant and widespread impact. Firms must be allowed time to adapt to new changes, build technical know-how, and invent new business models, processes, and products. For example, it took two decades for national U.S. statistics to reflect the boost in productivity from the introduction of electricity into factories.27 First, infrastructure needed to be laid, workers needed to be trained, and production lines had to be reinvented. Many general-purpose technologies feature a slow start followed by a boom in productivity, and the pattern appears to be holding true for AI.28 The quality and pace of AI 7  |  Gregory C. Allen and Akhil Thadaniadoption will greatly impact an economy’s ability to maximize its potential benefits. It is up to policymakers to ensure that resulting laws and regulations provide the necessary guidance to mitigate AI’s risks while creating an enabling environment for the technology to grow. One critical component of such an enabling environment is regulatory interoperability. Diverging regulatory requirements make it difficult for AI research and development collaboration to happen. While complete regulatory harmonization—adoption of the same regulations globally—is not feasible and may not be desirable, achieving a sufficient level of interoperability would allow different regulatory systems to work together. Incoherent legal frameworks or regulation can constitute a technical barrier to trade, making it harder for AI products and services to cross regulatory borders. Complying with regulation is expensive. Creating different versions of the same product to conform to regulations that differ not only in degree but in fundamental approach is often prohibitively expensive. Organizations looking to operate in multiple countries must spend additional resources to ensure that their products meet the regulatory requirements of all target markets. Especially for small and medium-sized enterprises that are hungry for market access, the burden to comply with complex foreign regulations can act as a barrier to trade, a reality explicitly acknowledged by the World Trade Organization’s Agreement on Technical Barriers to Trade.29 A study published by the National Bureau of Economic Research estimated the cost to innovation of the European Union’s General Data Protection Regulation (GDPR) by measuring the reduction of new applications listed on the Google Play Store. The study found that “about a third of existing [mobile phone] apps exited the market; and following GDPR’s enactment, the rate of [new] app entry fell by nearly half.”30 Interoperable regulation and standards reduce barriers to market entry. By lowering transaction and compliance costs, interoperable regulation minimizes the use of costly resources due to easier harmonization across countries. Aligned foundational standards and concepts provide a strong basis for mutual recognition mechanisms for AI safety, privacy, and data governance measures. AI research and development is an international activity.31 Cross-border collaborations between researchers, private enterprises, and academic institutions fuel AI innovation. For AI, increasing access to diverse data sets, sharing the costs of computing and AI infrastructure, expanding open-source and basic scientific research collaboration, and exchanging talent strengthens the impact and quality of AI research and development. As AI models have continued to develop at a rapid pace, the resources necessary to run and train them have increased exponentially. More advanced algorithms require more computing power and vast quantities of diverse and high-quality data to function well. Facilitating international AI collaborations can allow for a large number of researchers to take advantage of the same set of fixed investments, which otherwise can be hard to economically justify. Joint AI collaborations can leverage comparative advantages and reduce duplicative investments in AI.32 Not only does a fractured regulatory landscape impact a country’s ability to conduct AI research, but it also impacts the availability of AI products. As AI systems become more resource intensive, the ability to produce cutting-edge and advanced AI products will depend on the computing infrastructure and data sets available. Functioning as a barrier to trade, highly divergent domestic regulations risk blocking access to international markets. Countries without a strong domestic focus on bringing AI applications to market may get left behind. Lastly, the concerted and balanced enforcement of shared ethical principles, grounded in democratic values, can increase trust in AI systems. There remain cultural and ethical differences in how different countries interpret—and therefore legislate—AI principles. While the CSIS AI Council recognizes that different 8  |  Advancing Cooperative AI Governancejurisdictions may want regulation that reflects their own specific values, and that this might be important in some settings, maximizing agreement on core principles will help enable interoperability and ease frictions to cooperation. Without a common understanding of the component parts of, for example, trustworthy, explainable, or privacy-preserving AI, the compliance measures required by regulators to meet such principles and the metrics used to measure success will differ. What may count as sufficiently “safe” or “trustworthy” in one jurisdiction may not count somewhere else. What is evaluated when attempting to determine whether a system is “safe” or “trustworthy” may also differ across regulatory borders. An uneven regulatory landscape featuring incoherent national AI regulatory frameworks will place artificial barriers to AI innovation and adoption and can weaken not only research cooperation and trade but also protections for shared values for AI. Key AI terms and concepts, beyond ethical principles, will inform the scope and implementation of binding AI legislation. The European Union and United States both agree on a risk-based approach to AI governance but differ in how each defines and implements such an approach. International standards can help. The coordinated development and adoption of a set of foundational AI standards that establish a common taxonomy will be critical to create a nurturing and cohesive global regulatory landscape. Avoiding AI Balkanization Government regulators of AI should take appropriate steps to avoid a global landscape in which countries regulate AI under substantially different rules, terminologies, and requirements. Many countries around the world have aligned on the same broad goals for AI, as outlined by multilateral commitments from groups such as the G7, G20, and OECD. As governments seek to implement their shared principles in practice, they need renewed focus to ensure that regulatory frameworks are coherent and interoperable. The national strategies and draft policies released by the European Union, the United States, the United Kingdom, Japan, and others reflect differences in countries’ basic approaches to AI governance. Divergences in foundational concepts—such as the definition of AI, how to categorize AI risks, or the underlying factors that make up what it means to be “trustworthy”—could greatly impact the scope and implications of AI regulations. For areas where country approaches are aligned, at least in principle, different definitions of the same terms can cause additional friction. The U.S. Blueprint for an AI Bill of Rights adopts a broad definition of AI.33 If put into force, its requirements would encompass not just machine-learning systems but a significant share of modern-day software across all industries. The United Kingdom, on the other hand, further clarifies the scope of its AI guidance to “mostly discuss machine learning,” a much narrower subcategory.34 The same piece of technology could be considered AI in one country and traditional software in another, subjecting its developer (and potentially the end user) to different compliance requirements. Aligning on factors that underlie shared policy goals, such as explainable AI, and on a definition for AI can provide a mutually recognized baseline for further cooperation. Global standards development organizations (SDOs) can help create a common “ruler” or measuring stick by which regulators measure AI risks. Depending on varying levels of risk tolerance, governments can choose to prescribe different thresholds along such a ruler for AI’s responsible use. Once the factors that underpin AI transparency, for example, have been agreed to, governments can require different levels of transparency along the same underlying scale. 9  |  Gregory C. Allen and Akhil ThadaniMisaligned foundational concepts have large downstream impacts, influencing sector-specific guidance and more detailed legislation. How countries broadly evaluate, classify, and safeguard against AI risks, for example, has a fundamental impact on the design of the AI regulatory framework, setting the conditions for the adoption and development of AI.  Leveraging AI Standards Development Increasing international cooperation on AI standards development—and on internationally accepted foundational standards in particular—has the potential to provide one of the highest returns on investment for global policymakers. Legislation sets what goals to reach; standards can help outline how to reach those goals. Often considered terms of art with varying bounds based on organization, country, or industry, the International Organization for Standardization (ISO) and the International Electrotechnical Commission (IEC) Joint Technical Committee (JTC) Subcommittee on AI (SC 42) roughly categorizes AI standards into three subgroups: governance standards, foundational standards, and technical standards. AI governance standards provide guidance to corporate leaders and policymakers on organizational and administrative best practices to facilitate industry’s ability to meet the demands of responsible AI.35 The OECD has accomplished much on AI governance frameworks, sometimes referred to as policy standards, and sometimes publishes technical standards, though to date it has not done so on AI. Governance standards that seek to provide guidance on institutional arrangements, organizational structures, and roles and responsibilities further legal interoperability as defined by the EU interoperability framework.36 Foundational standards, shepherded by the ISO/IEC SC 42 Working Group 1, establish a common understanding and basis for the development of technical and vertical, or sector-specific, standards. They can be technical or non-technical and are horizontal in nature. As characterized by the ISO/IEC, foundational standards outline best practices for AI governance and clarify a common language and set of frameworks that can be implemented across all AI use cases.37 AI is a general-purpose technology with a wide variety of stakeholders and uses. Therefore, foundational standards must establish a common language and overarching framework that are useful regardless of the area to which AI is applied. Aligned foundational standards can establish a common approach to AI regulatory norms, advancing the interoperability of regulatory frameworks built on those norms. By providing a common lexicon for AI, foundational standards help ensure that the meaning of exchanged data, information, AI technologies, and services remain consistent. As they contribute to both legal and technical interoperability, international adoption of the same set of foundational standards is critical for open market access. Examples include ISO/IEC 22989, which covers basic AI concepts and terminology, ISO/IEC 23894, which provides guidance on AI risk management, and the OECD’s framework for AI risk classification.38 ISO/IEC 42001 is a particularly noteworthy standard in development. It sets out a management system standard for artificial intelligence and is being considered for adoption by the EU and UK national standards bodies.39 While standards are a critical component of driving interoperability and commonality across different countries and industries, different sectors have varying needs when it comes to compliance and implementation of new technology and the role of standards. The services sector often uses a principle-based approach to demonstrate alignment with specific AI principles, as they are typically not subject to regulatoryenforced conformity testing to gain market entry. Technical standards go into deeper technology verticals, providing voluntary guidance or best practices for 10  |  Advancing Cooperative AI Governancespecific use cases or application areas. Often more detailed and specific in nature, these standards provide technology-oriented guidance for how to build and maintain AI systems in accordance with industry best practices, or in the case of some standards developed by national standards bodies, with binding domestic regulation. Technical standards further interoperability by enabling machine-to-machine communication and are most commonly associated with the term “standard.” Standards are developed at both a national level, at national standards bodies such as the United States’ NIST, the United Kingdom’s AI Standards Hub, Japan’s National Institute of Advanced Industrial Science and Technology, or Europe’s Committee for Standardization (CEN) and the European Committee for Electrotechnical Standardization (CENELEC), often referred to as CEN-CENELEC, and at an international level, at organizations such as the ISO, IEC, and occasionally the OECD. International standards remain wholly voluntary and are developed through public-private cooperation. The ISO/IEC subcommittee on AI is one of the more advanced efforts developing a holistic suite of AI standards and operates under a “one country, one vote” system. Private companies are organized under a national delegation with varying government involvement. For example, the U.S. delegation to the SC 42 is represented by the American National Standards Institute, a private sector–led standards body (as opposed to the NIST, its government-run counterpart). On the other hand, the European Union, in addition to encouraging the participation of EU corporations, has designated a special government delegate from the European Commission for the ISO’s work on AI. Standards developed by national standards bodies are similarly developed through a varied mix of publicprivate cooperation. However, they may have more of a binding impact on the regulatory environment. As part of the European Union’s AI Act, the European Commission issued a draft standardization request to CENCENELEC outlining a specific set workplan for the standards body on AI.40 These standards will form the basis of regulatory impact assessments and will likely provide the most commonly used mechanism of establishing conformity with the AI Act provisions. As stated earlier, the European Commission holds significant power to direct, reject, and alter the standards created at CEN-CENELEC. In the United States, the NIST’s Cybersecurity Framework can be considered a close equivalent of the newly minted AI Risk Management Framework.41 After a period of review and test, NIST’s Cybersecurity Framework was made mandatory for government offices and, later, for many government contracts. Despite remaining voluntary for the rest of industry, its use quickly became the industry standard. Without further action on AI by the U.S. Congress, the same trajectory may hold true for the AI Risk Management Framework.  Many national standards bodies are represented at international standards organizations and aim to achieve some level of alignment between their work. EU standards bodies are subject to the Vienna and Frankfurt Agreements, which heavily incentivize standards alignment with the ISO and IEC.42 Pursuant to these agreements, upwards of 40 percent of CEN-CENELEC standards officially adopted into the EU Journal are based on international standards.43 These agreements are not binding, however, and there is no guarantee of alignment of AI standards. For AI, voluntary standards will form much of the substance of early AI regulation. Emerging AI regulations, still early in their development, have placed a heavy emphasis on the role of standards to achieve key policy goals. The European Union’s draft AI Act confers an outsized role to European standards organizations, who in the legislation’s current form will shape much of the act’s implementation. Specifically, the most developed federal-level effort at AI governance in the United States is the NIST’s AI Risk Management Framework. Shortly after releasing their proposal for an AI regulatory framework, the United Kingdom established the AI Standards Hub as a key pillar of their national AI strategy’s section on “Governing AI Effectively.”44 And Japan 11  |  Gregory C. Allen and Akhil Thadanihas, at least for now, deferred binding AI legislation altogether in favor of a soft, standards-driven approach. The development of international governance, foundational, and technical standards at SDOs such as the ISO, IEC, and OECD play an important role in ensuring a level economic playing field for organizations worldwide. When adopted by national standards bodies, internationally created standards can inform domestic governance while facilitating international trade, investment, and research collaborations. Much of the standardization activity on AI seeks to provide a level of conformity and interoperability between different regulatory approaches.45 However, despite the work of international SDOs, there is an absence of political agreement among G7 countries on both cooperatively developing AI standards in an international forum and aligning the results with standards adopted by national standards bodies. For the European Union, national standards (requested by the European Commission and crafted by CEN-CENELEC) will likely detail regulator-prescribed impact assessments and create measures and tools to demonstrate compliance with regulatory requirements, such as meeting the “acceptable level of trust,” conformity procedures, and algorithmic audits.46 They function as companions to national legislation and provide guidelines to ensure that AI technologies meet the goals and performance requirements outlined by regulators. International standards alone are not a panacea. They must be well crafted through multistakeholder input, they must be meaningfully consulted, and they ideally would be aligned by national governments and national standards bodies to make a positive difference. In the case of AI, creating standards is a multi-stage effort that requires input from a range of stakeholders and takes years to finish. Given the political pressure facing regional legislators, there is a risk that the creation of authoritative rules on AI will outpace the work being done by SDOs. Without a completed and internationally devised blueprint, governments and national standards bodies are left to draft standards (foundational and otherwise) on their own and to decide who gets to participate in the process. While this is not uncommon outside of AI regulation, creating isolated standards—built off fundamentally different definitions of key terminology and concepts—will compound the frictions between different country approaches to AI governance. The European Union’s AI Act The European Union’s work on AI may mature faster than international SDOs, raising the possibility for EU standards that are not fully harmonized with international bodies. The European Union represents one of the world’s largest markets and is the first to propose and likely adopt a comprehensive law governing the entirety of AI. European lawmakers have been honing their approach to AI regulation since the European Commission’s initial probe in 2018. In order to ensure that standards are driven by European values, and are without undue influence from incompatible value systems, Europe has taken measures that currently limit non-European firms’ participation in domestic standards creation. The European Commission introduced the draft AI Act in April 2021. With the potential for up to a 6 percent f ine of revenue for non-compliance, the final version of the act will likely have a large impact on AI adoption in the European Union. Internationally, as the first comprehensive and binding legislative AI proposal, the draft act could be incredibly influential in setting the global precedent for AI regulation. The AI Act text has since moved to the European Parliament and European Council, where each body will make their own amendments before entering three-party negotiations, or “trilogues,” to create a consolidated version between the three bodies. The draft is projected to enter trilogues during the latter half of 2023. 12  |  Advancing Cooperative AI GovernanceThe European Union adopted a new standardization strategy during the first half of 2022. The strategy lays down amended rules for EU standards creation, including the stated goal of avoiding “undue influence of actors from outside the EU and EEA [European Economic Area] in the decision-making processes.”47 The new strategy also called on European standards organizations to update their governance structures and restrict voting rights to EU-based organizations. With reduced avenues for international input at a domestic level at this time, cooperation on standards creation with the European Union must go through either international standards organizations or an alternative multilateral mechanism. The EU-U.S. Trade and Technology Council (TTC), launched in 2021, was set up to promote transatlantic cooperation on technology governance and digital trade. During its December 2022 ministerial meeting, the TTC’s Working Group on AI Standards released the Joint Roadmap on Evaluation and Measurement Tools for Trustworthy AI and Risk Management, which calls for greater international cooperation on AI standards.48 Nonbinding in nature, the joint roadmap emphasized the importance of transatlantic cooperation in international standards bodies, on shared definitions for AI terms, and on the development of tools for AI risk management and evaluation. The implementation and reach of this roadmap is not yet clear. However, immediately following its release, the European Commission issued an updated draft standardization request for the development of AI standards to the relevant European standards organizations, CEN-CENELEC.49 First issued in March 2022, the standards request was updated to emphasize coordination and cooperation with the ISO and IEC. Evidently a result of the TTC negotiations, a footnote in Annex II specifies the adoption of an internationally created common set of AI terms as especially important.50 Also following the TTC meeting came an EU-issued regulation that softened the language on non-EU participation in European standards creation.51 Although there have been recent encouraging signs, the European Commission holds the ability to diverge away from standards created in international bodies, should they see it necessary. The European Union’s AI Act, originally published in April 2021 by the European Commission, will be the most significant horizontal AI regulation anywhere to date. The regulation will apply to “high-risk” products released in the European Union or affecting the interests of people in the European Union. The legislative process is ongoing and is projected to enter final interinstitutional “trilogue” negotiations during the latter half of 2023. In the meantime, CEN-CENELEC has begun work on the draft request in order to keep to the European Commission’s timeline for standards creation. A variety of different potential outcomes are still possible. As the AI Act approaches a final text, the European Commission may decide to alter the standards request to be more or less in line with the work of the ISO and IEC. Moreover, if the European Commission f inds that the created standards are not in line with European values or insufficiently implement the goals of the AI Act, it may deny or request edits to those standards. It also may request targeted amendments to those standards, as well as submit its own technical common specifications, which essentially function as a replacement for standards. Even without drastically altering the standardization request, the European Commission can reject ISO/IEC-inspired or -aligned standards. Which standards are candidates for alignment, and the extent to which those standards will be aligned with those created in the ISO/IEC, was not specified by the standards request. Those questions will be answered by the work of CEN-CENELEC, the European Commission’s evaluation of ISO/IEC standards, and the European Union’s evolving AI Act. While the updated draft standards request and the European Commission’s engagement with the ISO and IEC are encouraging, it is important to build on this to ensure that the standards eventually adopted by CEN-CENELEC will be internationally compatible. 13  |  Gregory C. Allen and Akhil ThadaniOnce a fragmented set of standards is widely adopted, it can be extremely costly and difficult to go back and reconfigure recommended practices under a unified framework. Companies and consumers adapt to standardization.52 They create infrastructure and an ecosystem of compliant technology and policy to meet standards requirements. Electrical plugs and sockets, for example, do not conform to a uniform and internationally recognized standard. Different voltage requirements and plug shapes place additional costs on exporters and travelers using electrical equipment across regulatory borders. It would take significant time and resources to go back and replace all the manufacturing equipment, infrastructure, and consumer electronics to comply with a new and unified standard. This outcome should be avoided for AI. In addition to developing and aligning to international standards, it is important that G7 countries drive interoperability around core norms and concepts—what the ISO refers to as foundational standards—in their regulatory frameworks. For example, developing regulatory frameworks that are risk based and focused on correctly identifying and addressing the potential risks of different AI uses is an important norm that ensures that resources are focused on the use cases posing the potential risk of harm. Creating a common definition for a “risk-based approach” in the form of shared foundational standards will help create the necessary conditions for further interoperability. An uneven regulatory environment hurts countries’ ability to put AI to work. Through the OECD Council Recommendation on AI, the G7’s Charlevoix Common Vision for the Future of AI, and the G20 AI principles, multilateral organizations provided helpful mechanisms to converge on AI principles.53 The same tools for alignment can be used again. The most impactful step is to develop and agree to a multilateral understanding of the foundational concepts that make up horizontal AI standards. They form the basis and scope for deeper vertical standards and greatly impact the implementation of regulatory requirements. Complete coherence between country regulations is unlikely. Different legal traditions, values, constituents, and AI capabilities and comparative advantages all make it difficult to wholly align country approaches. However, aligning on the factors that underlie shared policy goals, such as explainable AI, and on a definition for AI can provide a mutually recognized baseline across jurisdictions. This creates a “common ruler” by which countries can similarly measure and understand the component parts of key concepts such as AI fairness, explainability, risk, and trust. Independent jurisdictions can then determine where on that scale the use of AI should fall.  ", "metadata": {"original_filename": "item075_US_Advancing Cooperative AI Governance at the 2023 G7 Summit.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:51:54.253025", "updated_at": "2025-08-28T21:51:54.253025", "word_count": 37541}