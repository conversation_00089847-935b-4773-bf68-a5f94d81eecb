{"doc_id": "3f0933a7-d619-4af8-bd78-e1dea47adeb3", "title": "item067_US_The case for targeted regulation", "text": "The case for targeted regulation\r\n\r\n13 min read\r\nA building with columns\r\nIncreasingly powerful AI systems have the potential to accelerate scientific progress, unlock new medical treatments, and grow the economy. But along with the remarkable new capabilities of these AIs come significant risks. Governments should urgently take action on AI policy in the next eighteen months. The window for proactive risk prevention is closing fast.\r\n\r\nJudicious, narrowly-targeted regulation can allow us to get the best of both worlds: realizing the benefits of AI while mitigating the risks. Dragging our feet might lead to the worst of both worlds: poorly-designed, knee-jerk regulation that hampers progress while also failing to be effective at preventing risks.\r\n\r\nIn this post, we suggest some principles for how governments can meaningfully reduce catastrophic risks while supporting innovation in AI’s thriving scientific and commercial sectors.\r\n\r\nResponsible Scaling Policies and AI regulation\r\nRSPs are not intended as a substitute for regulation, but as a prototype for it. Based on our experience with RSPs, we believe there are three elements that are key for effective AI regulation:\r\n\r\nTransparency. Currently, the public and lawmakers have no way to verify any AI company’s adherence to its RSP (or similar plan), nor the outcome of any tests run as part of it. A simple, sensible step is to require companies to have and publish RSP-like policies, describing at a high level their capability thresholds and the related safeguards that are triggered when a model reaches them. Companies should also be required to publish a set of risk evaluations of each new generation of AI systems, so as to create a public record of the risks of AI systems and best practice for mitigating those risks. Finally, there should be some mechanism to verify the accuracy of these statements.\r\nIncentivizing better safety and security practices. Transparency alone does not guarantee robust policies: companies could simply declare very weak safety and security practices. AI regulation should also incentivize companies to develop RSPs that are effective at preventing catastrophes. There are a number of possible mechanisms for this, with different pros and cons. For instance, regulators could identify the threat models that RSPs must address, under some standard of reasonableness, while leaving the details to companies. Or they could simply specify the standards an RSP must meet. The government can also encourage an RSP “race to the top” by inquiring about and comparing RSPs, learning from the emerging best practices, and holding companies to account if their RSPs are obviously operating beneath the bar set by those practices. Finally, there are a number of possible mechanisms for indirectly incentivizing safe practices. We are uncertain about the exact mechanism, but we feel strongly that any mechanism should be flexible: the technology is developing rapidly, so it is important for regulatory processes to learn from the best practices as they evolve, rather than being static.\r\nSimplicity and focus. Whatever regulations we arrive at should be as surgical as possible. They must not impose burdens that are unnecessary or unrelated to the issues at hand. One of the worst things that could happen to the cause of catastrophic risk prevention is a link forming between regulation that’s needed to prevent risks and burdensome or illogical rules. Any bill or law should also be simple to understand and implement: complexity creates confusion and makes it harder to predict what will happen in practice.\r\nThere are many different approaches that could meet these three criteria; we’re not wedded to any one in particular. Instead, we’re pragmatically interested in finding a reasonable proposal that a critical mass of stakeholders can get behind.", "metadata": {"original_filename": "item067_US_The case for targeted regulation.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T22:21:32.416578", "updated_at": "2025-08-28T22:21:32.416578", "word_count": 3854}