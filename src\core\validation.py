"""
输入验证和清理模块
"""
import re
import html
from typing import Optional, List, Dict, Any
from pathlib import Path
from fastapi import HTTPException, status, UploadFile
from pydantic import BaseModel, field_validator
from src.core.config import settings
import logging

logger = logging.getLogger(__name__)

class InputValidator:
    """输入验证器类"""
    
    # 允许的文件类型
    ALLOWED_FILE_TYPES = {
        'text/plain': '.txt',
        'text/markdown': '.md',
        'application/pdf': '.pdf',
        'application/msword': '.doc',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx'
    }
    
    # 危险字符模式
    DANGEROUS_PATTERNS = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'vbscript:',
        r'on\w+\s*=',
        r'eval\s*\(',
        r'document\.',
        r'window\.',
        r'alert\s*\(',
        r'prompt\s*\(',
        r'confirm\s*\(',
        r'<iframe[^>]*>',
        r'<object[^>]*>',
        r'<embed[^>]*>',
    ]
    
    @classmethod
    def sanitize_text(cls, text: str) -> str:
        """
        清理文本输入，防止XSS攻击
        
        Args:
            text: 输入文本
            
        Returns:
            清理后的文本
        """
        if not text:
            return ""
        
        # HTML实体编码
        sanitized = html.escape(text)
        
        # 移除危险的JavaScript代码
        for pattern in cls.DANGEROUS_PATTERNS:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE | re.DOTALL)
        
        # 移除控制字符
        sanitized = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', sanitized)
        
        return sanitized.strip()
    
    @classmethod
    def validate_filename(cls, filename: str) -> str:
        """
        验证文件名安全性
        
        Args:
            filename: 文件名
            
        Returns:
            验证后的文件名
            
        Raises:
            HTTPException: 文件名不安全
        """
        if not filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文件名不能为空"
            )
        
        # 检查文件名长度
        if len(filename) > 255:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文件名过长"
            )
        
        # 检查危险字符
        dangerous_chars = ['..', '/', '\\', ':', '*', '?', '"', '<', '>', '|']
        for char in dangerous_chars:
            if char in filename:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"文件名包含危险字符: {char}"
                )
        
        # 检查文件扩展名
        allowed_extensions = ['.txt', '.md', '.pdf', '.doc', '.docx']
        file_ext = Path(filename).suffix.lower()
        if file_ext not in allowed_extensions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的文件类型: {file_ext}"
            )
        
        return filename
    
    @classmethod
    async def validate_file_upload(cls, file: UploadFile) -> Dict[str, Any]:
        """
        验证文件上传
        
        Args:
            file: 上传的文件
            
        Returns:
            验证结果
            
        Raises:
            HTTPException: 文件验证失败
        """
        # 验证文件名
        cls.validate_filename(file.filename)
        
        # 验证文件大小
        max_size = cls._parse_size(settings.MAX_FILE_SIZE)
        if file.size and file.size > max_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"文件大小超过限制: {settings.MAX_FILE_SIZE}"
            )
        
        # 验证文件类型
        if file.content_type not in cls.ALLOWED_FILE_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的文件类型: {file.content_type}"
            )
        
        # 读取文件内容进行安全检查
        try:
            content = await file.read()
            await file.seek(0)  # 重置文件指针
            
            # 检查文件内容是否包含恶意代码
            if file.content_type.startswith('text/'):
                text_content = content.decode('utf-8', errors='ignore')
                if cls._contains_malicious_content(text_content):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="文件内容包含恶意代码"
                    )
            
            return {
                "filename": file.filename,
                "content_type": file.content_type,
                "size": len(content),
                "is_valid": True
            }
            
        except Exception as e:
            logger.error(f"文件验证失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文件验证失败"
            )
    
    @classmethod
    def validate_document_text(cls, text: str) -> str:
        """
        验证文档文本
        
        Args:
            text: 文档文本
            
        Returns:
            验证后的文本
            
        Raises:
            HTTPException: 文本验证失败
        """
        if not text or not text.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文档内容不能为空"
            )
        
        # 清理文本
        sanitized_text = cls.sanitize_text(text)
        
        # 检查文本长度
        if len(sanitized_text) > 1000000:  # 1MB
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="文档内容过长"
            )
        
        if len(sanitized_text) < 10:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文档内容过短"
            )
        
        return sanitized_text
    
    @classmethod
    def validate_task_types(cls, task_types: List[str]) -> List[str]:
        """
        验证分析任务类型
        
        Args:
            task_types: 任务类型列表
            
        Returns:
            验证后的任务类型列表
            
        Raises:
            HTTPException: 任务类型验证失败
        """
        if not task_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="至少需要选择一个分析任务"
            )
        
        valid_tasks = {
            'actor_relation', 'role_framing', 
            'problem_scope', 'causal_mechanism'
        }
        
        invalid_tasks = []
        valid_task_list = []
        
        for task in task_types:
            if task not in valid_tasks:
                invalid_tasks.append(task)
            else:
                valid_task_list.append(task)
        
        if invalid_tasks:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的分析任务: {', '.join(invalid_tasks)}"
            )
        
        return valid_task_list
    
    @classmethod
    def validate_api_key(cls, api_key: str) -> str:
        """
        验证API密钥格式
        
        Args:
            api_key: API密钥
            
        Returns:
            验证后的API密钥
            
        Raises:
            HTTPException: API密钥格式无效
        """
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API密钥不能为空"
            )
        
        # 检查API密钥格式（基本验证）
        if len(api_key) < 32:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API密钥格式无效"
            )
        
        # 检查API密钥是否包含危险字符
        if cls._contains_malicious_content(api_key):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API密钥包含危险内容"
            )
        
        return api_key.strip()
    
    @classmethod
    def _parse_size(cls, size_str: str) -> int:
        """
        解析大小字符串（如 "10MB"）为字节数
        
        Args:
            size_str: 大小字符串
            
        Returns:
            字节数
        """
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    @classmethod
    def _contains_malicious_content(cls, content: str) -> bool:
        """
        检查内容是否包含恶意代码
        
        Args:
            content: 要检查的内容
            
        Returns:
            是否包含恶意内容
        """
        for pattern in cls.DANGEROUS_PATTERNS:
            if re.search(pattern, content, re.IGNORECASE | re.DOTALL):
                return True
        return False

class ValidatedDocumentCreate(BaseModel):
    """验证后的文档创建模型"""
    title: str
    text: str
    doc_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    @field_validator('title')
    @classmethod
    def validate_title(cls, v):
        """验证标题"""
        if not v or not v.strip():
            raise ValueError('标题不能为空')
        if len(v) > 200:
            raise ValueError('标题长度不能超过200字符')
        return InputValidator.sanitize_text(v.strip())
    
    @field_validator('text')
    @classmethod
    def validate_text(cls, v):
        """验证文本"""
        return InputValidator.validate_document_text(v)
    
    @field_validator('metadata')
    @classmethod
    def validate_metadata(cls, v):
        """验证元数据"""
        if v is None:
            return {}
        if not isinstance(v, dict):
            raise ValueError('元数据必须是字典')
        return v

# 创建输入验证器实例
input_validator = InputValidator()