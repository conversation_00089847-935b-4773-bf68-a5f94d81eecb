<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Intelligent Document Analysis System</title>
    <link rel="stylesheet" href="/static/style.css">
    <script src="/static/js/i18n.js"></script>
    <meta name="description" content="Intelligent document analysis system powered by Zhipu AI, providing in-depth policy document analysis, actor relationship identification, causal mechanism analysis and more">
    <meta name="keywords" content="document analysis, policy analysis, AI analysis, Zhipu AI, text mining">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        // Theme toggle functionality
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            const icon = document.querySelector('.theme-toggle i');
            if (newTheme === 'dark') {
                icon.textContent = '☀️';
            } else {
                icon.textContent = '🌙';
            }
        }
        
        // Initialize theme
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            
            const icon = document.querySelector('.theme-toggle i');
            if (icon && savedTheme === 'dark') {
                icon.textContent = '☀️';
            }
            
            // Set language to English by default
            switchLanguage('en');
        });
    </script>
</head>
<body>
    <div class="container">
        <header>
            <h1>📄 Intelligent Document Analysis System</h1>
            <p class="subtitle">Advanced Policy Document Analysis Tool Powered by Zhipu AI</p>
            <nav class="main-nav">
                <a href="/" class="nav-link active">🏠 Document Analysis</a>
                <a href="/static/visualization.html" class="nav-link">📊 Data Visualization</a>
                <a href="/static/data-table.html" class="nav-link">📋 Table View</a>
                <a href="/static/prompts.html" class="nav-link">🔧 Prompt Editor</a>
                <a href="/docs" class="nav-link">📚 API Documentation</a>
                <div class="language-selector">
                    <button data-lang="zh" onclick="switchLanguage('zh')">中文</button>
                    <button data-lang="en" onclick="switchLanguage('en')" class="active">English</button>
                </div>
                <button class="theme-toggle" onclick="toggleTheme()" title="Switch Theme">
                    <i>🌙</i>
                </button>
            </nav>
        </header>

        <main>
            <!-- API Configuration Area -->
            <div class="config-section">
                <h2>🔧 API Configuration</h2>
                <div class="config-grid">
                    <div class="config-item">
                        <label for="api-key">API Key:</label>
                        <input type="password" id="api-key" placeholder="Enter your Zhipu AI API Key">
                        <button onclick="saveApiKey()" class="btn btn-primary">Save</button>
                    </div>
                    <div class="config-item">
                        <label for="model-select">Model:</label>
                        <select id="model-select">
                            <option value="glm-4.5" selected>GLM-4.5</option>
                            <option value="glm-4-plus">GLM-4-Plus</option>
                            <option value="glm-4-0520">GLM-4-0520</option>
                            <option value="glm-4">GLM-4</option>
                            <option value="glm-4-air">GLM-4-Air</option>
                            <option value="glm-4-airx">GLM-4-AirX</option>
                            <option value="glm-4-flash">GLM-4-Flash</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Document Upload Area -->
            <div class="upload-section">
                <h2>📄 Document Upload</h2>
                <div class="upload-area" id="upload-area">
                    <div class="upload-content">
                        <i class="upload-icon">📁</i>
                        <p>Click or drag files to this area to upload</p>
                        <p class="upload-hint">Supports PDF, Word, TXT formats, file size should not exceed 10MB</p>
                        <input type="file" id="file-input" accept=".pdf,.doc,.docx,.txt" style="display: none;">
                        <button onclick="document.getElementById('file-input').click()" class="btn btn-primary">Select File</button>
                    </div>
                </div>
                <div class="upload-progress" id="upload-progress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <p id="progress-text">Uploading...</p>
                </div>
            </div>

            <!-- Analysis Configuration -->
            <div class="analysis-section">
                <h2>🧠 Analysis Configuration</h2>
                <div class="analysis-grid">
                    <div class="analysis-item">
                        <label for="doc-id">Document ID:</label>
                        <input type="text" id="doc-id" placeholder="Enter document ID or upload a new document">
                    </div>
                    <div class="analysis-item">
                        <label for="analysis-tasks">Analysis Tasks:</label>
                        <div class="checkbox-group">
                            <label><input type="checkbox" value="actor_relation" checked> Actor Relations</label>
                            <label><input type="checkbox" value="role_framing" checked> Role Framing</label>
                            <label><input type="checkbox" value="problem_scope" checked> Problem Scope</label>
                            <label><input type="checkbox" value="causal_mechanism" checked> Causal Mechanism</label>
                        </div>
                    </div>
                </div>
                <div class="analysis-actions">
                    <button onclick="startAnalysis()" class="btn btn-success">🚀 Start Analysis</button>
                    <button onclick="viewResults()" class="btn btn-info">📊 View Results</button>
                </div>
            </div>

            <!-- Analysis Progress -->
            <div class="progress-section" id="progress-section" style="display: none;">
                <h2>📈 Analysis Progress</h2>
                <div class="progress-container">
                    <div class="progress-item">
                        <span>Actor Relations</span>
                        <div class="progress-bar"><div class="progress-fill" id="actor-progress"></div></div>
                    </div>
                    <div class="progress-item">
                        <span>Role Framing</span>
                        <div class="progress-bar"><div class="progress-fill" id="role-progress"></div></div>
                    </div>
                    <div class="progress-item">
                        <span>Problem Scope</span>
                        <div class="progress-bar"><div class="progress-fill" id="scope-progress"></div></div>
                    </div>
                    <div class="progress-item">
                        <span>Causal Mechanism</span>
                        <div class="progress-bar"><div class="progress-fill" id="causal-progress"></div></div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h2>⚡ Quick Actions</h2>
                <div class="actions-grid">
                    <a href="/static/visualization.html" class="action-card">
                        <i>📊</i>
                        <h3>Data Visualization</h3>
                        <p>View analysis results in interactive charts and graphs</p>
                    </a>
                    <a href="/static/data-table.html" class="action-card">
                        <i>📋</i>
                        <h3>Table View</h3>
                        <p>Browse detailed analysis data in structured tables</p>
                    </a>
                    <a href="/static/prompts.html" class="action-card">
                        <i>🔧</i>
                        <h3>Prompt Editor</h3>
                        <p>Customize analysis prompts and parameters</p>
                    </a>
                    <a href="/docs" class="action-card">
                        <i>📚</i>
                        <h3>API Documentation</h3>
                        <p>Explore comprehensive API documentation and examples</p>
                    </a>
                </div>
            </div>
        </main>
    </div>

    <script src="/static/app.js"></script>
</body>
</html>
