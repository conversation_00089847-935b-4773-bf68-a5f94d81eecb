{"id": "c0b58815-761f-43a3-8451-87a98c421fd5", "task_type": "problem_scope", "name": "默认问题范围分析", "description": "分析政策文档中的问题定义和范围界定", "template": "请分析以下政策文档中的问题定义和范围界定。\n\n文档内容：\n{document}\n\n请执行以下分析任务：\n1. 识别文档中定义的主要问题\n2. 分析问题的范围和边界\n3. 分析问题的严重程度和紧迫性\n4. 总结关键发现\n\n请以严格的JSON格式返回分析结果，结构如下（不要包含注释）：\n{\n    \"problems\": [\n        {\n            \"name\": \"问题名称\",\n            \"definition\": \"问题定义\",\n            \"scope\": \"问题范围\",\n            \"severity\": \"严重程度\",\n            \"urgency\": \"紧迫性\",\n            \"affected_groups\": [\"受影响群体1\", \"受影响群体2\"]\n        }\n    ],\n    \"scope_boundaries\": [\n        {\n            \"dimension\": \"界定维度\",\n            \"included\": [\"包含内容1\", \"包含内容2\"],\n            \"excluded\": [\"排除内容1\", \"排除内容2\"],\n            \"rationale\": \"界定理由\"\n        }\n    ],\n    \"problem_hierarchy\": [\n        {\n            \"main_problem\": \"主要问题\",\n            \"sub_problems\": [\"子问题1\", \"子问题2\"],\n            \"relationships\": [\"关系描述1\", \"关系描述2\"]\n        }\n    ],\n    \"key_findings\": [\"关键发现\"]\n}\n\n重要：只返回纯JSON格式的分析结果，不要包含任何额外的解释、注释或说明文字。", "version": "1.0.0", "is_active": true, "is_default": true, "created_at": "2025-08-28 14:02:00.950468", "updated_at": "2025-08-28 14:02:00.950468", "created_by": "user", "tags": ["默认", "问题", "范围", "系统"], "performance_score": null}