import asyncio
import json
from src.services.zhipu_client import ZhipuAIClient
from src.services.prompt_service import PromptManagementService
from src.models.schemas import TaskType

async def test_prompt_management():
    """测试提示词管理功能"""
    
    print("=== 测试提示词管理功能 ===")
    
    # 创建服务
    zhipu_client = ZhipuAIClient()
    prompt_service = PromptManagementService(zhipu_client)
    
    # 测试创建提示词
    print("\n1. 创建新提示词...")
    from src.models.schemas import PromptTemplateCreate
    
    new_prompt = PromptTemplateCreate(
        task_type=TaskType.ACTOR_RELATION,
        name="测试提示词",
        description="用于测试的提示词",
        template="请分析以下文档中的行为者关系：\n{document}\n请返回JSON格式的结果。",
        tags=["测试", "优化"]
    )
    
    created_prompt = prompt_service.create_prompt(new_prompt)
    print(f"创建成功: {created_prompt.id}")
    
    # 测试获取提示词
    print("\n2. 获取提示词...")
    retrieved_prompt = prompt_service.get_prompt(created_prompt.id)
    print(f"获取成功: {retrieved_prompt.name}")
    
    # 测试更新提示词
    print("\n3. 更新提示词...")
    from src.models.schemas import PromptTemplateUpdate
    
    update_data = PromptTemplateUpdate(
        description="更新后的描述",
        tags=["测试", "优化", "更新"]
    )
    
    updated_prompt = prompt_service.update_prompt(created_prompt.id, update_data)
    print(f"更新成功: {updated_prompt.version}")
    
    # 测试获取所有提示词
    print("\n4. 获取所有提示词...")
    all_prompts = prompt_service.get_active_prompts()
    print(f"共有 {len(all_prompts)} 个激活的提示词")
    
    # 测试按任务类型获取
    print("\n5. 按任务类型获取...")
    actor_prompts = prompt_service.get_prompts_by_task_type(TaskType.ACTOR_RELATION)
    print(f"行为者关系提示词: {len(actor_prompts)} 个")
    
    print("\n=== 提示词管理功能测试完成 ===")

if __name__ == "__main__":
    asyncio.run(test_prompt_management())