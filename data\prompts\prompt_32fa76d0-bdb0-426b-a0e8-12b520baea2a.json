{"id": "32fa76d0-bdb0-426b-a0e8-12b520baea2a", "task_type": "role_framing", "name": "默认角色塑造分析", "description": "分析政策文档中的角色塑造和身份建构", "template": "请分析以下政策文档中的角色塑造和身份建构。\n\n文档内容：\n{document}\n\n请执行以下分析任务：\n1. 识别文档中被塑造的角色和身份\n2. 分析角色的特征和属性描述\n3. 分析角色之间的对比和关系\n4. 总结关键发现\n\n请以严格的JSON格式返回分析结果，结构如下（不要包含注释）：\n{\n    \"roles\": [\n        {\n            \"name\": \"角色名称\",\n            \"type\": \"角色类型\",\n            \"characteristics\": [\"特征1\", \"特征2\"],\n            \"framing_strategy\": \"塑造策略\",\n            \"evidence\": [\"支持证据1\", \"支持证据2\"]\n        }\n    ],\n    \"identity_construction\": [\n        {\n            \"identity\": \"身份类型\",\n            \"construction_method\": \"建构方法\",\n            \"purpose\": \"建构目的\",\n            \"examples\": [\"具体例子1\", \"具体例子2\"]\n        }\n    ],\n    \"role_contrasts\": [\n        {\n            \"positive_role\": \"正面角色\",\n            \"negative_role\": \"负面角色\",\n            \"contrast_dimension\": \"对比维度\",\n            \"description\": \"对比描述\"\n        }\n    ],\n    \"key_findings\": [\"关键发现\"]\n}\n\n重要：只返回纯JSON格式的分析结果，不要包含任何额外的解释、注释或说明文字。", "version": "1.0.0", "is_active": true, "is_default": true, "created_at": "2025-08-28 14:02:00.948377", "updated_at": "2025-08-28 14:02:00.948377", "created_by": "user", "tags": ["默认", "角色", "身份", "系统"], "performance_score": null}