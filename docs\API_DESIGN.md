# API接口设计文档

## API概览

### 基础信息
- **基础URL**: `http://localhost:8000/api/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`
- **响应格式**: JSON

### 通用响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "req_123456789"
}
```

### 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {
      "field": "email",
      "message": "邮箱格式不正确"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "request_id": "req_123456789"
}
```

## 认证相关接口

### 1. 用户注册
- **URL**: `POST /auth/register`
- **描述**: 注册新用户

**请求体**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "confirm_password": "string"
}
```

**响应体**:
```json
{
  "success": true,
  "data": {
    "user_id": "user_123456",
    "username": "testuser",
    "email": "<EMAIL>",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z"
  },
  "message": "用户注册成功"
}
```

### 2. 用户登录
- **URL**: `POST /auth/login`
- **描述**: 用户登录

**请求体**:
```json
{
  "username": "string",
  "password": "string"
}
```

**响应体**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_in": 3600,
    "user": {
      "id": "user_123456",
      "username": "testuser",
      "email": "<EMAIL>",
      "is_admin": false
    }
  },
  "message": "登录成功"
}
```

### 3. 用户登出
- **URL**: `POST /auth/logout`
- **描述**: 用户登出

**请求头**:
```
Authorization: Bearer <token>
```

**响应体**:
```json
{
  "success": true,
  "message": "登出成功"
}
```

### 4. 刷新Token
- **URL**: `POST /auth/refresh`
- **描述**: 刷新访问令牌

**请求头**:
```
Authorization: Bearer <token>
```

**响应体**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "Bearer",
    "expires_in": 3600
  },
  "message": "Token刷新成功"
}
```

## 文档管理接口

### 5. 上传文档
- **URL**: `POST /documents/upload`
- **描述**: 上传文档进行分析

**请求头**:
```
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

**请求体**:
```json
{
  "file": "file", // 文件对象
  "title": "string", // 可选，文档标题
  "language": "string" // 可选，语言标识，默认为zh
}
```

**响应体**:
```json
{
  "success": true,
  "data": {
    "document_id": "doc_123456",
    "title": "AI政策分析报告",
    "file_size": 1024,
    "content_type": "text/plain",
    "word_count": 500,
    "created_at": "2024-01-01T00:00:00Z"
  },
  "message": "文档上传成功"
}
```

### 6. 创建文本文档
- **URL**: `POST /documents/text`
- **描述**: 创建文本文档

**请求头**:
```
Authorization: Bearer <token>
Content-Type: application/json
```

**请求体**:
```json
{
  "title": "string",
  "content": "string",
  "language": "string" // 可选，默认为zh
}
```

**响应体**:
```json
{
  "success": true,
  "data": {
    "document_id": "doc_123456",
    "title": "AI政策分析报告",
    "word_count": 500,
    "created_at": "2024-01-01T00:00:00Z"
  },
  "message": "文档创建成功"
}
```

### 7. 获取文档信息
- **URL**: `GET /documents/{document_id}`
- **描述**: 获取文档详细信息

**请求头**:
```
Authorization: Bearer <token>
```

**响应体**:
```json
{
  "success": true,
  "data": {
    "id": "doc_123456",
    "title": "AI政策分析报告",
    "content": "文档内容预览...",
    "content_type": "text/plain",
    "file_size": 1024,
    "word_count": 500,
    "language": "zh",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "created_by": "user_123456",
    "analysis_status": {
      "task1": "completed",
      "task2": "completed",
      "task3": "pending",
      "task4": "pending"
    }
  },
  "message": "获取成功"
}
```

### 8. 列出文档
- **URL**: `GET /documents`
- **描述**: 获取文档列表

**请求头**:
```
Authorization: Bearer <token>
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `size`: 每页大小 (默认: 20, 最大: 100)
- `language`: 语言过滤
- `search`: 搜索关键词
- `created_by`: 创建者ID
- `start_date`: 开始日期
- `end_date`: 结束日期

**响应体**:
```json
{
  "success": true,
  "data": {
    "total": 100,
    "page": 1,
    "size": 20,
    "items": [
      {
        "id": "doc_123456",
        "title": "AI政策分析报告",
        "word_count": 500,
        "language": "zh",
        "created_at": "2024-01-01T00:00:00Z",
        "analysis_status": {
          "completed": 2,
          "pending": 2
        }
      }
    ]
  },
  "message": "获取成功"
}
```

### 9. 删除文档
- **URL**: `DELETE /documents/{document_id}`
- **描述**: 删除文档

**请求头**:
```
Authorization: Bearer <token>
```

**响应体**:
```json
{
  "success": true,
  "message": "文档删除成功"
}
```

## 分析任务接口

### 10. 提交分析任务
- **URL**: `POST /analysis/submit`
- **描述**: 提交文档分析任务

**请求头**:
```
Authorization: Bearer <token>
Content-Type: application/json
```

**请求体**:
```json
{
  "document_id": "doc_123456",
  "tasks": ["task1", "task2", "task3", "task4"],
  "priority": 0, // 可选，优先级，默认为0
  "callback_url": "string" // 可选，回调URL
}
```

**响应体**:
```json
{
  "success": true,
  "data": {
    "task_id": "task_123456",
    "document_id": "doc_123456",
    "tasks": ["task1", "task2", "task3", "task4"],
    "status": "pending",
    "priority": 0,
    "created_at": "2024-01-01T00:00:00Z"
  },
  "message": "任务提交成功"
}
```

### 11. 获取任务状态
- **URL**: `GET /analysis/tasks/{task_id}`
- **描述**: 获取任务状态

**请求头**:
```
Authorization: Bearer <token>
```

**响应体**:
```json
{
  "success": true,
  "data": {
    "id": "task_123456",
    "document_id": "doc_123456",
    "task_type": "task1",
    "status": "completed",
    "priority": 0,
    "progress": 100,
    "retry_count": 0,
    "created_at": "2024-01-01T00:00:00Z",
    "started_at": "2024-01-01T00:00:05Z",
    "completed_at": "2024-01-01T00:00:15Z",
    "execution_time": 10.5,
    "error_message": null
  },
  "message": "获取成功"
}
```

### 12. 获取分析结果
- **URL**: `GET /analysis/results/{document_id}`
- **描述**: 获取文档分析结果

**请求头**:
```
Authorization: Bearer <token>
```

**查询参数**:
- `tasks`: 任务列表，如 "task1,task2,task3,task4"
- `format`: 输出格式 (json, csv)

**响应体**:
```json
{
  "success": true,
  "data": {
    "document_id": "doc_123456",
    "title": "AI政策分析报告",
    "analysis_time": "2024-01-01T00:00:00Z",
    "results": {
      "task1": {
        "actors": [...],
        "relationships": {...}
      },
      "task2": {
        "portrayals": {...}
      },
      "task3": {
        "issue_scope": {...}
      },
      "task4": {
        "causal_mechanisms": {...}
      }
    },
    "summary": {
      "total_actors": 15,
      "total_relationships": 25,
      "total_portrayals": 8,
      "execution_time": 45.2
    }
  },
  "message": "获取成功"
}
```

### 13. 重新运行分析
- **URL**: `POST /analysis/rerun/{document_id}`
- **描述**: 重新运行文档分析

**请求头**:
```
Authorization: Bearer <token>
Content-Type: application/json
```

**请求体**:
```json
{
  "tasks": ["task1", "task2", "task3", "task4"],
  "priority": 0
}
```

**响应体**:
```json
{
  "success": true,
  "data": {
    "task_id": "task_789012",
    "message": "分析任务已重新提交"
  },
  "message": "重新运行成功"
}
```

## 批量处理接口

### 14. 批量上传文档
- **URL**: `POST /batch/upload`
- **描述**: 批量上传文档

**请求头**:
```
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

**请求体**:
```json
{
  "files": ["file1", "file2", "file3"],
  "batch_name": "string",
  "description": "string",
  "auto_analyze": true,
  "tasks": ["task1", "task2", "task3", "task4"]
}
```

**响应体**:
```json
{
  "success": true,
  "data": {
    "batch_id": "batch_123456",
    "name": "批量分析任务",
    "total_files": 3,
    "uploaded_files": 3,
    "failed_files": 0,
    "created_at": "2024-01-01T00:00:00Z"
  },
  "message": "批量上传成功"
}
```

### 15. 获取批量任务状态
- **URL**: `GET /batch/{batch_id}`
- **描述**: 获取批量任务状态

**请求头**:
```
Authorization: Bearer <token>
```

**响应体**:
```json
{
  "success": true,
  "data": {
    "id": "batch_123456",
    "name": "批量分析任务",
    "description": "批量分析政策文档",
    "status": "completed",
    "total_tasks": 12,
    "completed_tasks": 12,
    "failed_tasks": 0,
    "success_tasks": 12,
    "created_at": "2024-01-01T00:00:00Z",
    "started_at": "2024-01-01T00:00:05Z",
    "completed_at": "2024-01-01T00:05:00Z",
    "execution_time": 295.0,
    "files": [
      {
        "filename": "doc1.txt",
        "document_id": "doc_123456",
        "status": "completed",
        "error_message": null
      }
    ]
  },
  "message": "获取成功"
}
```

### 16. 下载批量结果
- **URL**: `GET /batch/{batch_id}/download`
- **描述**: 下载批量分析结果

**请求头**:
```
Authorization: Bearer <token>
```

**查询参数**:
- `format`: 下载格式 (json, csv, zip)

**响应**: 文件下载

## 系统管理接口

### 17. 系统状态
- **URL**: `GET /system/health`
- **描述**: 系统健康检查

**响应体**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00Z",
    "version": "1.0.0",
    "uptime": 86400,
    "components": {
      "database": "healthy",
      "redis": "healthy",
      "zhipuai_api": "healthy"
    },
    "metrics": {
      "active_tasks": 5,
      "queued_tasks": 10,
      "completed_tasks": 1000,
      "failed_tasks": 5
    }
  },
  "message": "系统正常"
}
```

### 18. 系统统计
- **URL**: `GET /system/stats`
- **描述**: 获取系统统计信息

**请求头**:
```
Authorization: Bearer <token>
```

**查询参数**:
- `start_date`: 开始日期
- `end_date`: 结束日期

**响应体**:
```json
{
  "success": true,
  "data": {
    "documents": {
      "total": 1000,
      "today": 10,
      "this_week": 50,
      "this_month": 200
    },
    "tasks": {
      "total": 4000,
      "completed": 3800,
      "failed": 200,
      "running": 5,
      "pending": 15
    },
    "users": {
      "total": 100,
      "active": 80,
      "new_today": 2
    },
    "performance": {
      "avg_execution_time": 30.5,
      "success_rate": 95.0,
      "api_calls_today": 5000
    }
  },
  "message": "获取成功"
}
```

### 19. 系统日志
- **URL**: `GET /system/logs`
- **描述**: 获取系统日志

**请求头**:
```
Authorization: Bearer <token>
```

**查询参数**:
- `level`: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `start_date`: 开始日期
- `end_date`: 结束日期
- `page`: 页码
- `size`: 每页大小

**响应体**:
```json
{
  "success": true,
  "data": {
    "total": 1000,
    "page": 1,
    "size": 20,
    "items": [
      {
        "id": 1,
        "level": "ERROR",
        "message": "API调用失败",
        "timestamp": "2024-01-01T00:00:00Z",
        "source": "api",
        "user_id": "user_123456"
      }
    ]
  },
  "message": "获取成功"
}
```

## Pydantic 模型定义

### 基础响应模型

```python
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List, Generic, TypeVar
from datetime import datetime
from enum import Enum

class ResponseBase(BaseModel):
    """基础响应模型"""
    success: bool = True
    message: str = "操作成功"
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[str] = None

class ResponseData(ResponseBase, Generic[TypeVar]):
    """数据响应模型"""
    data: Any

class ErrorResponse(ResponseBase):
    """错误响应模型"""
    success: bool = False
    error: Dict[str, Any]

class ErrorCode(str, Enum):
    """错误代码枚举"""
    VALIDATION_ERROR = "VALIDATION_ERROR"
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR"
    AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR"
    NOT_FOUND = "NOT_FOUND"
    RATE_LIMIT_ERROR = "RATE_LIMIT_ERROR"
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"
    INTERNAL_ERROR = "INTERNAL_ERROR"
```

### 用户相关模型

```python
from pydantic import EmailStr, constr, validator

class UserRegisterRequest(BaseModel):
    """用户注册请求"""
    username: constr(min_length=3, max_length=50)
    email: EmailStr
    password: constr(min_length=6, max_length=100)
    confirm_password: str
    
    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'password' in values and v != values['password']:
            raise ValueError('两次输入的密码不匹配')
        return v

class UserLoginRequest(BaseModel):
    """用户登录请求"""
    username: constr(min_length=3, max_length=50)
    password: str

class UserResponse(BaseModel):
    """用户响应"""
    id: str
    username: str
    email: str
    is_active: bool
    is_admin: bool
    created_at: datetime
    last_login: Optional[datetime] = None

class TokenResponse(BaseModel):
    """令牌响应"""
    access_token: str
    token_type: str = "Bearer"
    expires_in: int
    user: UserResponse
```

### 文档相关模型

```python
class DocumentCreateRequest(BaseModel):
    """文档创建请求"""
    title: Optional[str] = None
    content: str
    language: str = "zh"

class DocumentUploadRequest(BaseModel):
    """文档上传请求"""
    title: Optional[str] = None
    language: str = "zh"

class DocumentResponse(BaseModel):
    """文档响应"""
    id: str
    title: Optional[str] = None
    content_type: str
    file_size: Optional[int] = None
    word_count: int
    language: str
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    analysis_status: Dict[str, str] = Field(default_factory=dict)

class DocumentListResponse(BaseModel):
    """文档列表响应"""
    total: int
    page: int
    size: int
    items: List[DocumentResponse]
```

### 分析任务相关模型

```python
class TaskType(str, Enum):
    """任务类型枚举"""
    TASK1 = "task1"
    TASK2 = "task2"
    TASK3 = "task3"
    TASK4 = "task4"

class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class AnalysisSubmitRequest(BaseModel):
    """分析提交请求"""
    document_id: str
    tasks: List[TaskType]
    priority: int = 0
    callback_url: Optional[str] = None

class TaskResponse(BaseModel):
    """任务响应"""
    id: str
    document_id: str
    task_type: TaskType
    status: TaskStatus
    priority: int
    progress: int = 0
    retry_count: int = 0
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    execution_time: Optional[float] = None
    error_message: Optional[str] = None

class AnalysisResultResponse(BaseModel):
    """分析结果响应"""
    document_id: str
    title: Optional[str] = None
    analysis_time: datetime
    results: Dict[str, Any]
    summary: Dict[str, Any]
```

### 批处理相关模型

```python
class BatchUploadRequest(BaseModel):
    """批量上传请求"""
    files: List[str]
    batch_name: str
    description: Optional[str] = None
    auto_analyze: bool = True
    tasks: List[TaskType] = [TaskType.TASK1, TaskType.TASK2, TaskType.TASK3, TaskType.TASK4]

class BatchStatusResponse(BaseModel):
    """批量状态响应"""
    id: str
    name: str
    description: Optional[str] = None
    status: TaskStatus
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    success_tasks: int
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    execution_time: Optional[float] = None
    files: List[Dict[str, Any]] = Field(default_factory=list)
```

### 系统相关模型

```python
class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str
    timestamp: datetime
    version: str
    uptime: int
    components: Dict[str, str]
    metrics: Dict[str, Any]

class SystemStatsResponse(BaseModel):
    """系统统计响应"""
    documents: Dict[str, int]
    tasks: Dict[str, int]
    users: Dict[str, int]
    performance: Dict[str, float]

class LogEntryResponse(BaseModel):
    """日志条目响应"""
    id: int
    level: str
    message: str
    timestamp: datetime
    source: Optional[str] = None
    user_id: Optional[str] = None
    request_id: Optional[str] = None
    ip_address: Optional[str] = None
    method: Optional[str] = None
    endpoint: Optional[str] = None
    status_code: Optional[int] = None
    response_time: Optional[float] = None

class LogListResponse(BaseModel):
    """日志列表响应"""
    total: int
    page: int
    size: int
    items: List[LogEntryResponse]
```

## API限流策略

### 限流配置
```python
from fastapi import Request, HTTPException
from slowapi import Limiter
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

# 限流规则
RATE_LIMITS = {
    "auth": "5/minute",  # 认证相关接口
    "document_upload": "10/minute",  # 文档上传
    "analysis_submit": "20/minute",  # 分析提交
    "api_read": "100/minute",  # 读取接口
    "api_write": "50/minute",  # 写入接口
}
```

这个API设计文档提供了：

1. **完整的接口定义** - 包括所有必要的RESTful API
2. **详细的请求/响应模型** - 使用Pydantic进行数据验证
3. **统一的响应格式** - 确保API的一致性
4. **错误处理机制** - 完整的错误代码和消息
5. **限流策略** - 保护系统免受滥用

每个接口都经过精心设计，考虑了安全性、可用性和扩展性。