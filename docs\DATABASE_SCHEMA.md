# 数据库架构设计

## 数据库关系图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Users       │    │    Documents    │    │  AnalysisTasks  │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ id (PK)         │──┐ │ id (PK)         │──┐ │ id (PK)         │
│ username        │   │ title           │   │ document_id (FK)│
│ email           │   │ content         │   │ task_type       │
│ hashed_password │   │ content_type    │   │ status          │
│ is_active       │   │ file_path       │   │ input_data      │
│ is_admin        │   │ file_size       │   │ result_data     │
│ created_at      │   │ created_at      │   │ error_message   │
│ last_login      │   │ updated_at      │   │ created_at      │
└─────────────────┘   └─────────────────┘   │ started_at      │
       │                    │                 │ completed_at    │
       │                    │                 └─────────────────┘
       │                    │                        │
       │                    └────────────────────────┘
       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UserSessions  │    │   BatchJobs     │    │   SystemLogs    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ id (PK)         │    │ id (PK)         │    │ id (PK)         │
│ user_id (FK)    │    │ name            │    │ level           │
│ session_token   │    │ status          │    │ message         │
│ created_at      │    │ created_at      │    │ timestamp       │
│ expires_at      │    │ completed_at    │    │ source          │
│ last_accessed   │    │ total_tasks     │    │ metadata        │
└─────────────────┘    │ success_count   │    └─────────────────┘
                       │ error_count     │
                       └─────────────────┘
```

## 详细表结构设计

### 1. 用户表 (users)

```sql
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    
    INDEX idx_users_username (username),
    INDEX idx_users_email (email),
    INDEX idx_users_is_active (is_active)
);
```

### 2. 用户会话表 (user_sessions)

```sql
CREATE TABLE user_sessions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_sessions_user_id (user_id),
    INDEX idx_sessions_token (session_token),
    INDEX idx_sessions_expires (expires_at)
);
```

### 3. 文档表 (documents)

```sql
CREATE TABLE documents (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(500),
    content LONGTEXT NOT NULL,
    content_type VARCHAR(100) DEFAULT 'text/plain',
    file_path VARCHAR(1000),
    file_size BIGINT,
    checksum VARCHAR(64),
    language VARCHAR(10) DEFAULT 'zh',
    word_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(36),
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_documents_created_at (created_at),
    INDEX idx_documents_language (language),
    INDEX idx_documents_created_by (created_by),
    FULLTEXT idx_documents_content (content, title)
);
```

### 4. 分析任务表 (analysis_tasks)

```sql
CREATE TABLE analysis_tasks (
    id VARCHAR(36) PRIMARY KEY,
    document_id VARCHAR(36) NOT NULL,
    task_type ENUM('task1', 'task2', 'task3', 'task4') NOT NULL,
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    priority INT DEFAULT 0,
    input_data JSON,
    result_data JSON,
    error_message TEXT,
    error_details JSON,
    retry_count INT DEFAULT 0,
    max_retries INT DEFAULT 3,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    execution_time FLOAT NULL,
    
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    INDEX idx_tasks_document_id (document_id),
    INDEX idx_tasks_status (status),
    INDEX idx_tasks_task_type (task_type),
    INDEX idx_tasks_created_at (created_at),
    INDEX idx_tasks_priority (priority)
);
```

### 5. 批处理作业表 (batch_jobs)

```sql
CREATE TABLE batch_jobs (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    status ENUM('pending', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    created_by VARCHAR(36) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    total_tasks INT DEFAULT 0,
    completed_tasks INT DEFAULT 0,
    failed_tasks INT DEFAULT 0,
    success_tasks INT DEFAULT 0,
    input_files JSON,
    configuration JSON,
    result_summary JSON,
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_batch_status (status),
    INDEX idx_batch_created_at (created_at),
    INDEX idx_batch_created_by (created_by)
);
```

### 6. 系统日志表 (system_logs)

```sql
CREATE TABLE system_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL,
    message TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    source VARCHAR(100),
    user_id VARCHAR(36),
    request_id VARCHAR(36),
    session_id VARCHAR(36),
    ip_address VARCHAR(45),
    user_agent TEXT,
    method VARCHAR(10),
    endpoint VARCHAR(200),
    status_code INT,
    response_time FLOAT,
    error_details JSON,
    metadata JSON,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_logs_level (level),
    INDEX idx_logs_timestamp (timestamp),
    INDEX idx_logs_source (source),
    INDEX idx_logs_user_id (user_id),
    INDEX idx_logs_request_id (request_id)
);
```

### 7. 性能指标表 (performance_metrics)

```sql
CREATE TABLE performance_metrics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DOUBLE NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tags JSON,
    dimensions JSON,
    
    INDEX idx_metrics_name (metric_name),
    INDEX idx_metrics_timestamp (timestamp)
);
```

### 8. 缓存表 (cache_entries)

```sql
CREATE TABLE cache_entries (
    cache_key VARCHAR(255) PRIMARY KEY,
    cache_value LONGTEXT NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    access_count INT DEFAULT 0,
    
    INDEX idx_cache_expires (expires_at),
    INDEX idx_cache_accessed (accessed_at)
);
```

## SQLAlchemy 模型定义

### 基础模型类

```python
from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, Boolean, ForeignKey, Enum, Float, BigInteger
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, Dict, Any
import uuid

Base = declarative_base()

class BaseModel(Base):
    """基础模型类"""
    __abstract__ = True
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class TimestampMixin:
    """时间戳混入类"""
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

### 用户相关模型

```python
class User(BaseModel):
    """用户模型"""
    __tablename__ = "users"
    
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    last_login = Column(DateTime, nullable=True)
    
    # 关系
    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    documents = relationship("Document", back_populates="created_by_user")
    batch_jobs = relationship("BatchJob", back_populates="created_by_user")
    
    def __repr__(self):
        return f"<User(id='{self.id}', username='{self.username}')>"

class UserSession(BaseModel):
    """用户会话模型"""
    __tablename__ = "user_sessions"
    
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False)
    session_token = Column(String(255), unique=True, nullable=False)
    expires_at = Column(DateTime, nullable=False)
    last_accessed = Column(DateTime, default=datetime.utcnow)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    
    # 关系
    user = relationship("User", back_populates="sessions")
    
    def __repr__(self):
        return f"<UserSession(id='{self.id}', user_id='{self.user_id}')>"
```

### 文档相关模型

```python
class Document(BaseModel):
    """文档模型"""
    __tablename__ = "documents"
    
    title = Column(String(500), nullable=True)
    content = Column(Text, nullable=False)
    content_type = Column(String(100), default="text/plain")
    file_path = Column(String(1000), nullable=True)
    file_size = Column(BigInteger, nullable=True)
    checksum = Column(String(64), nullable=True)
    language = Column(String(10), default="zh")
    word_count = Column(Integer, default=0)
    created_by = Column(String(36), ForeignKey("users.id"), nullable=True)
    
    # 关系
    created_by_user = relationship("User", back_populates="documents")
    analysis_tasks = relationship("AnalysisTask", back_populates="document", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Document(id='{self.id}', title='{self.title[:50]}...')>"

class AnalysisTask(BaseModel):
    """分析任务模型"""
    __tablename__ = "analysis_tasks"
    
    document_id = Column(String(36), ForeignKey("documents.id"), nullable=False)
    task_type = Column(Enum('task1', 'task2', 'task3', 'task4', name='task_type_enum'), nullable=False)
    status = Column(Enum('pending', 'running', 'completed', 'failed', 'cancelled', name='task_status_enum'), default='pending')
    priority = Column(Integer, default=0)
    input_data = Column(JSON, nullable=True)
    result_data = Column(JSON, nullable=True)
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    execution_time = Column(Float, nullable=True)
    
    # 关系
    document = relationship("Document", back_populates="analysis_tasks")
    
    def __repr__(self):
        return f"<AnalysisTask(id='{self.id}', task_type='{self.task_type}', status='{self.status}')>"
```

### 批处理相关模型

```python
class BatchJob(BaseModel):
    """批处理作业模型"""
    __tablename__ = "batch_jobs"
    
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    status = Column(Enum('pending', 'running', 'completed', 'failed', 'cancelled', name='batch_status_enum'), default='pending')
    created_by = Column(String(36), ForeignKey("users.id"), nullable=False)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    total_tasks = Column(Integer, default=0)
    completed_tasks = Column(Integer, default=0)
    failed_tasks = Column(Integer, default=0)
    success_tasks = Column(Integer, default=0)
    input_files = Column(JSON, nullable=True)
    configuration = Column(JSON, nullable=True)
    result_summary = Column(JSON, nullable=True)
    
    # 关系
    created_by_user = relationship("User", back_populates="batch_jobs")
    
    def __repr__(self):
        return f"<BatchJob(id='{self.id}', name='{self.name}', status='{self.status}')>"
```

### 系统相关模型

```python
class SystemLog(Base):
    """系统日志模型"""
    __tablename__ = "system_logs"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    level = Column(Enum('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL', name='log_level_enum'), nullable=False)
    message = Column(Text, nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow)
    source = Column(String(100), nullable=True)
    user_id = Column(String(36), ForeignKey("users.id"), nullable=True)
    request_id = Column(String(36), nullable=True)
    session_id = Column(String(36), nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    method = Column(String(10), nullable=True)
    endpoint = Column(String(200), nullable=True)
    status_code = Column(Integer, nullable=True)
    response_time = Column(Float, nullable=True)
    error_details = Column(JSON, nullable=True)
    metadata = Column(JSON, nullable=True)
    
    # 关系
    user = relationship("User")
    
    def __repr__(self):
        return f"<SystemLog(id='{self.id}', level='{self.level}')>"

class PerformanceMetric(Base):
    """性能指标模型"""
    __tablename__ = "performance_metrics"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    metric_name = Column(String(100), nullable=False)
    metric_value = Column(Float, nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow)
    tags = Column(JSON, nullable=True)
    dimensions = Column(JSON, nullable=True)
    
    def __repr__(self):
        return f"<PerformanceMetric(id='{self.id}', name='{self.metric_name}', value={self.metric_value})>"

class CacheEntry(Base):
    """缓存条目模型"""
    __tablename__ = "cache_entries"
    
    cache_key = Column(String(255), primary_key=True)
    cache_value = Column(Text, nullable=False)
    expires_at = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    accessed_at = Column(DateTime, default=datetime.utcnow)
    access_count = Column(Integer, default=0)
    
    def __repr__(self):
        return f"<CacheEntry(key='{self.cache_key}')>"
```

## 数据库索引优化

### 主要索引策略

```sql
-- 用户相关索引
CREATE INDEX idx_users_active_created ON users(is_active, created_at) WHERE is_active = TRUE;
CREATE INDEX idx_users_admin ON users(is_admin) WHERE is_admin = TRUE;

-- 文档相关索引
CREATE INDEX idx_documents_search ON documents(language, created_at) WHERE word_count > 0;
CREATE INDEX idx_documents_files ON documents(file_path) WHERE file_path IS NOT NULL;

-- 任务相关索引
CREATE INDEX idx_tasks_active ON analysis_tasks(status, task_type, priority) 
WHERE status IN ('pending', 'running');
CREATE INDEX idx_tasks_performance ON analysis_tasks(status, execution_time) 
WHERE status = 'completed';

-- 批处理索引
CREATE INDEX idx_batch_active ON batch_jobs(status, created_at) 
WHERE status IN ('pending', 'running');

-- 日志索引
CREATE INDEX idx_logs_recent ON system_logs(timestamp DESC) WHERE timestamp > DATE_SUB(NOW(), INTERVAL 7 DAY);
CREATE INDEX idx_logs_errors ON system_logs(level, timestamp) WHERE level IN ('ERROR', 'CRITICAL');

-- 性能指标索引
CREATE INDEX idx_metrics_recent ON performance_metrics(metric_name, timestamp DESC) 
WHERE timestamp > DATE_SUB(NOW(), INTERVAL 1 DAY);
```

## 数据库视图

### 常用查询视图

```sql
-- 用户活动视图
CREATE VIEW user_activity_view AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.last_login,
    COUNT(DISTINCT d.id) as document_count,
    COUNT(DISTINCT t.id) as task_count,
    COUNT(DISTINCT b.id) as batch_count,
    MAX(d.created_at) as last_document_created,
    MAX(t.created_at) as last_task_created
FROM users u
LEFT JOIN documents d ON u.id = d.created_by
LEFT JOIN analysis_tasks t ON d.id = t.document_id
LEFT JOIN batch_jobs b ON u.id = b.created_by
GROUP BY u.id, u.username, u.email, u.last_login;

-- 任务统计视图
CREATE VIEW task_statistics_view AS
SELECT 
    DATE(created_at) as date,
    task_type,
    status,
    COUNT(*) as count,
    AVG(execution_time) as avg_execution_time,
    MAX(execution_time) as max_execution_time,
    MIN(execution_time) as min_execution_time
FROM analysis_tasks
GROUP BY DATE(created_at), task_type, status;

-- 系统性能视图
CREATE VIEW system_performance_view AS
SELECT 
    DATE(timestamp) as date,
    source,
    AVG(response_time) as avg_response_time,
    COUNT(*) as request_count,
    SUM(CASE WHEN status_code >= 400 THEN 1 ELSE 0 END) as error_count,
    SUM(CASE WHEN level = 'ERROR' THEN 1 ELSE 0 END) as log_error_count
FROM system_logs
WHERE method IS NOT NULL
GROUP BY DATE(timestamp), source;
```

## 数据迁移策略

### Alembic 配置

```python
# alembic/env.py
from logging.config import fileConfig
from sqlalchemy import engine_from_config
from sqlalchemy import pool
from alembic import context
import os
from dotenv import load_dotenv

load_dotenv()

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
from src.models import Base
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.

def get_url():
    return os.getenv("DATABASE_URL")

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode."""
    url = get_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    configuration = config.get_section(config.config_ini_section)
    configuration["sqlalchemy.url"] = get_url()
    connectable = engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
```

这个数据库架构设计提供了：

1. **完整的表结构设计** - 包括所有必要的表和字段
2. **详细的SQLAlchemy模型** - 用于ORM操作
3. **索引优化策略** - 提高查询性能
4. **数据库视图** - 简化常用查询
5. **迁移策略** - 使用Alembic进行版本控制

每个表都经过精心设计，考虑了性能、扩展性和维护性。