"""
API密钥管理模块
"""
import os
import json
import hashlib
import secrets
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from pathlib import Path
from fastapi import HTTPException, status
from src.core.config import settings
from src.core.security import security_service
import logging

logger = logging.getLogger(__name__)

class APIKeyManager:
    """API密钥管理器"""
    
    def __init__(self):
        self.keys_file = settings.BASE_DIR / "data" / "api_keys.json"
        self.keys_file.parent.mkdir(parents=True, exist_ok=True)
        self._load_keys()
    
    def _load_keys(self):
        """加载API密钥"""
        try:
            if self.keys_file.exists():
                with open(self.keys_file, 'r', encoding='utf-8') as f:
                    self.keys = json.load(f)
            else:
                self.keys = {}
        except Exception as e:
            logger.error(f"加载API密钥失败: {str(e)}")
            self.keys = {}
    
    def _save_keys(self):
        """保存API密钥"""
        try:
            with open(self.keys_file, 'w', encoding='utf-8') as f:
                json.dump(self.keys, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存API密钥失败: {str(e)}")
            raise
    
    def create_api_key(
        self, 
        name: str, 
        description: str = "", 
        permissions: List[str] = None,
        expires_in_days: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        创建新的API密钥
        
        Args:
            name: 密钥名称
            description: 密钥描述
            permissions: 权限列表
            expires_in_days: 过期天数
            
        Returns:
            API密钥信息
        """
        if not name or not name.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="API密钥名称不能为空"
            )
        
        # 生成API密钥
        api_key = security_service.generate_api_key()
        key_id = security_service.hash_sensitive_data(api_key)
        
        # 计算过期时间
        expires_at = None
        if expires_in_days:
            expires_at = (datetime.now() + timedelta(days=expires_in_days)).isoformat()
        
        # 创建密钥信息
        key_info = {
            "id": key_id,
            "name": name,
            "description": description,
            "permissions": permissions or ["read"],
            "created_at": datetime.now().isoformat(),
            "expires_at": expires_at,
            "last_used": None,
            "usage_count": 0,
            "is_active": True,
            "rate_limit": {
                "requests_per_minute": 60,
                "requests_per_hour": 1000,
                "requests_per_day": 10000
            }
        }
        
        # 保存密钥（只保存哈希后的信息）
        self.keys[key_id] = key_info
        self._save_keys()
        
        logger.info(f"创建新的API密钥: {name}")
        
        return {
            "api_key": api_key,
            "key_info": {
                "id": key_id,
                "name": key_info["name"],
                "description": key_info["description"],
                "permissions": key_info["permissions"],
                "created_at": key_info["created_at"],
                "expires_at": key_info["expires_at"]
            }
        }
    
    def validate_api_key(self, api_key: str) -> Dict[str, Any]:
        """
        验证API密钥
        
        Args:
            api_key: API密钥
            
        Returns:
            密钥信息
            
        Raises:
            HTTPException: 密钥无效
        """
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API密钥不能为空"
            )
        
        # 计算密钥哈希
        key_hash = security_service.hash_sensitive_data(api_key)
        
        # 查找密钥
        key_info = self.keys.get(key_hash)
        if not key_info:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的API密钥"
            )
        
        # 检查密钥是否激活
        if not key_info.get("is_active", True):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="API密钥已被禁用"
            )
        
        # 检查是否过期
        if key_info.get("expires_at"):
            expires_at = datetime.fromisoformat(key_info["expires_at"])
            if datetime.now() > expires_at:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="API密钥已过期"
                )
        
        # 更新使用信息
        key_info["last_used"] = datetime.now().isoformat()
        key_info["usage_count"] = key_info.get("usage_count", 0) + 1
        self._save_keys()
        
        return key_info
    
    def revoke_api_key(self, key_id: str) -> bool:
        """
        撤销API密钥
        
        Args:
            key_id: 密钥ID
            
        Returns:
            是否成功
        """
        if key_id not in self.keys:
            return False
        
        self.keys[key_id]["is_active"] = False
        self.keys[key_id]["revoked_at"] = datetime.now().isoformat()
        self._save_keys()
        
        logger.info(f"撤销API密钥: {key_id}")
        return True
    
    def list_api_keys(self) -> List[Dict[str, Any]]:
        """
        列出所有API密钥
        
        Returns:
            API密钥列表
        """
        keys_list = []
        for key_id, key_info in self.keys.items():
            # 返回安全的信息（不包含实际密钥）
            safe_info = {
                "id": key_id,
                "name": key_info["name"],
                "description": key_info["description"],
                "permissions": key_info["permissions"],
                "created_at": key_info["created_at"],
                "expires_at": key_info.get("expires_at"),
                "last_used": key_info.get("last_used"),
                "usage_count": key_info.get("usage_count", 0),
                "is_active": key_info.get("is_active", True)
            }
            keys_list.append(safe_info)
        
        return keys_list
    
    def update_api_key(
        self, 
        key_id: str, 
        name: Optional[str] = None,
        description: Optional[str] = None,
        permissions: Optional[List[str]] = None,
        is_active: Optional[bool] = None
    ) -> bool:
        """
        更新API密钥信息
        
        Args:
            key_id: 密钥ID
            name: 新名称
            description: 新描述
            permissions: 新权限
            is_active: 是否激活
            
        Returns:
            是否成功
        """
        if key_id not in self.keys:
            return False
        
        key_info = self.keys[key_id]
        
        if name is not None:
            key_info["name"] = name
        if description is not None:
            key_info["description"] = description
        if permissions is not None:
            key_info["permissions"] = permissions
        if is_active is not None:
            key_info["is_active"] = is_active
        
        key_info["updated_at"] = datetime.now().isoformat()
        self._save_keys()
        
        logger.info(f"更新API密钥: {key_id}")
        return True
    
    def check_rate_limit(self, api_key: str, endpoint: str) -> bool:
        """
        检查API密钥的速率限制
        
        Args:
            api_key: API密钥
            endpoint: 端点路径
            
        Returns:
            是否允许请求
        """
        try:
            key_info = self.validate_api_key(api_key)
            
            # 这里可以实现更复杂的速率限制逻辑
            # 暂时返回True，实际应该记录请求时间并检查限制
            return True
            
        except Exception as e:
            logger.error(f"检查速率限制失败: {str(e)}")
            return False
    
    def rotate_api_key(self, key_id: str) -> Dict[str, Any]:
        """
        轮换API密钥
        
        Args:
            key_id: 密钥ID
            
        Returns:
            新的API密钥信息
        """
        if key_id not in self.keys:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API密钥不存在"
            )
        
        old_key_info = self.keys[key_id]
        
        # 创建新密钥
        new_key_data = self.create_api_key(
            name=old_key_info["name"],
            description=f"{old_key_info.get('description', '')} (轮换)",
            permissions=old_key_info["permissions"],
            expires_in_days=self._get_days_until_expiry(old_key_info.get("expires_at"))
        )
        
        # 禁用旧密钥
        self.revoke_api_key(key_id)
        
        logger.info(f"轮换API密钥: {key_id}")
        return new_key_data
    
    def _get_days_until_expiry(self, expires_at: Optional[str]) -> Optional[int]:
        """计算到过期的天数"""
        if not expires_at:
            return None
        
        expiry_date = datetime.fromisoformat(expires_at)
        now = datetime.now()
        delta = expiry_date - now
        
        return max(1, delta.days)

# 创建API密钥管理器实例
api_key_manager = APIKeyManager()