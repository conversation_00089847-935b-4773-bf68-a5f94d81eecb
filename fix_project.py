#!/usr/bin/env python3
"""
项目修复脚本
修复文档分析系统中发现的问题
"""
import os
import json
import shutil
from pathlib import Path

def create_directories():
    """创建必要的目录结构"""
    print("🔧 创建必要的目录结构...")
    
    directories = [
        "logs",
        "uploads", 
        "data/documents",
        "data/results",
        "data/prompts",
        "data/prompts/history",
        "data/prompts/test_results",
        "output",
        "config"
    ]
    
    for dir_path in directories:
        path = Path(dir_path)
        path.mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {dir_path}")

def create_environment_file():
    """创建环境变量文件"""
    print("\n🔧 创建环境配置文件...")
    
    env_content = """# 智谱AI API配置 - 请设置您的真实API密钥
ZHIPUAI_API_KEY=your_zhipuai_api_key_here

# 安全配置 - 请在生产环境中更改
SECRET_KEY=development_secret_key_please_change_in_production_12345

# 应用配置
DEBUG=true
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=sqlite:///./document_analysis.db

# API配置
MAX_WORKERS=4
REQUEST_TIMEOUT=30
MAX_RETRIES=3

# 文件上传配置
MAX_FILE_SIZE=10MB
UPLOAD_DIR=uploads

# 缓存配置
CACHE_TTL=3600

# 速率限制配置
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_REQUESTS_PER_HOUR=1000
RATE_LIMIT_REQUESTS_PER_DAY=10000
"""
    
    env_file = Path(".env")
    if not env_file.exists():
        with open(env_file, "w", encoding="utf-8") as f:
            f.write(env_content)
        print("✅ 创建 .env 文件")
    else:
        print("ℹ️  .env 文件已存在")

def fix_missing_imports():
    """修复缺失的导入"""
    print("\n🔧 修复缺失的导入和依赖...")
    
    # 检查并创建缺失的__init__.py文件
    init_files = [
        "src/__init__.py",
        "src/api/__init__.py", 
        "src/api/routes/__init__.py",
        "src/core/__init__.py",
        "src/models/__init__.py",
        "src/services/__init__.py",
        "src/utils/__init__.py",
        "src/db/__init__.py"
    ]
    
    for init_file in init_files:
        path = Path(init_file)
        if not path.exists():
            path.parent.mkdir(parents=True, exist_ok=True)
            path.touch()
            print(f"✅ 创建 {init_file}")

def create_sample_documents():
    """创建示例文档"""
    print("\n🔧 创建示例文档...")
    
    sample_docs = [
        {
            "doc_id": "sample_001",
            "title": "AI政策示例文档",
            "text": """
政府将加强对人工智能产业的监管，确保AI技术的安全发展。
同时，政府也将为AI创新企业提供更多的政策支持和资金扶持。
科技企业应当承担更多的社会责任，推动AI技术的健康发展。
监管部门将建立完善的AI安全评估体系，对关键AI应用进行严格审查。
研究机构需要加强AI伦理研究，为产业发展提供理论指导。
""",
            "metadata": {
                "source": "sample",
                "category": "policy",
                "created_by": "system"
            },
            "created_at": "2024-01-01T00:00:00",
            "updated_at": "2024-01-01T00:00:00",
            "word_count": 150
        },
        {
            "doc_id": "sample_002", 
            "title": "环保政策分析文档",
            "text": """
环保部门宣布将实施更严格的环境保护措施。
企业必须遵守新的排放标准，违法者将面临严厉处罚。
政府承诺投入更多资金用于环境治理和生态修复。
公众应当提高环保意识，积极参与环境保护行动。
环保组织将发挥监督作用，推动环保政策的有效实施。
""",
            "metadata": {
                "source": "sample",
                "category": "environment",
                "created_by": "system"
            },
            "created_at": "2024-01-01T00:00:00",
            "updated_at": "2024-01-01T00:00:00",
            "word_count": 120
        }
    ]
    
    documents_dir = Path("data/documents")
    for doc in sample_docs:
        doc_file = documents_dir / f"{doc['doc_id']}.json"
        if not doc_file.exists():
            with open(doc_file, "w", encoding="utf-8") as f:
                json.dump(doc, f, ensure_ascii=False, indent=2)
            print(f"✅ 创建示例文档: {doc['doc_id']}")

def fix_json_parsing_issues():
    """修复JSON解析问题"""
    print("\n🔧 修复JSON解析问题...")
    
    # 这个问题主要在智谱AI返回的JSON格式中，已经在zhipu_client.py中处理了
    print("✅ JSON解析修复已在zhipu_client.py中实现")

def create_test_script():
    """创建测试脚本"""
    print("\n🔧 创建测试脚本...")
    
    test_script = '''#!/usr/bin/env python3
"""
系统测试脚本
"""
import sys
import asyncio
import requests
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_api_server():
    """测试API服务器是否启动"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ API服务器运行正常")
            return True
        else:
            print(f"❌ API服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到API服务器: {e}")
        return False

def test_document_creation():
    """测试文档创建"""
    try:
        doc_data = {
            "title": "测试文档",
            "text": "这是一个测试文档，用于验证系统功能。政府将推进科技创新，企业应承担社会责任。",
            "metadata": {"test": True}
        }
        
        response = requests.post("http://localhost:8000/api/v1/documents/", json=doc_data, timeout=10)
        if response.status_code == 201:
            result = response.json()
            print(f"✅ 文档创建成功: {result['doc_id']}")
            return result['doc_id']
        else:
            print(f"❌ 文档创建失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ 文档创建异常: {e}")
        return None

def test_document_analysis(doc_id):
    """测试文档分析"""
    try:
        analysis_data = {
            "doc_id": doc_id,
            "tasks": ["actor_relation"]
        }
        
        response = requests.post("http://localhost:8000/api/v1/analysis/", json=analysis_data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            print("✅ 文档分析成功")
            return True
        else:
            print(f"❌ 文档分析失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 文档分析异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始系统测试...")
    
    # 测试API服务器
    if not test_api_server():
        print("请先启动API服务器: python -m uvicorn src.main:app --reload")
        return
    
    # 测试文档创建
    doc_id = test_document_creation()
    if not doc_id:
        return
    
    # 测试文档分析
    test_document_analysis(doc_id)
    
    print("\\n🎉 测试完成!")

if __name__ == "__main__":
    main()
'''
    
    test_file = Path("test_system.py")
    with open(test_file, "w", encoding="utf-8") as f:
        f.write(test_script)
    print("✅ 创建系统测试脚本: test_system.py")

def create_startup_script():
    """创建启动脚本"""
    print("\n🔧 创建启动脚本...")
    
    # Windows批处理文件
    windows_script = '''@echo off
echo 启动文档分析系统...
echo.

echo 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误：Python未安装或不在PATH中
    pause
    exit /b 1
)

echo.
echo 安装/更新依赖包...
pip install -r requirements.txt

echo.
echo 启动API服务器...
python -m uvicorn src.main:app --host 127.0.0.1 --port 8000 --reload

pause
'''
    
    with open("start_server.bat", "w", encoding="utf-8") as f:
        f.write(windows_script)
    
    # Unix/Linux shell脚本
    unix_script = '''#!/bin/bash
echo "启动文档分析系统..."
echo

echo "检查Python环境..."
python3 --version
if [ $? -ne 0 ]; then
    echo "错误：Python3未安装或不在PATH中"
    exit 1
fi

echo
echo "安装/更新依赖包..."
pip3 install -r requirements.txt

echo
echo "启动API服务器..."
python3 -m uvicorn src.main:app --host 127.0.0.1 --port 8000 --reload
'''
    
    with open("start_server.sh", "w", encoding="utf-8") as f:
        f.write(unix_script)
    
    # 设置执行权限
    try:
        os.chmod("start_server.sh", 0o755)
    except:
        pass
    
    print("✅ 创建启动脚本: start_server.bat (Windows), start_server.sh (Unix/Linux)")

def main():
    """主修复函数"""
    print("🚀 开始修复文档分析系统...")
    print("=" * 50)
    
    create_directories()
    create_environment_file()
    fix_missing_imports()
    create_sample_documents()
    fix_json_parsing_issues()
    create_test_script()
    create_startup_script()
    
    print("\n" + "=" * 50)
    print("✨ 项目修复完成！")
    print("\n📋 后续步骤:")
    print("1. 设置智谱AI API密钥：编辑 .env 文件中的 ZHIPUAI_API_KEY")
    print("2. 安装依赖：pip install -r requirements.txt")
    print("3. 启动服务器：")
    print("   - Windows: 双击 start_server.bat")
    print("   - Unix/Linux: ./start_server.sh")
    print("   - 手动: python -m uvicorn src.main:app --reload")
    print("4. 测试系统：python test_system.py")
    print("5. 访问API文档：http://localhost:8000/docs")
    print("6. 访问Web界面：http://localhost:8000")

if __name__ == "__main__":
    main()
