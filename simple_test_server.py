#!/usr/bin/env python3
"""
Simple test server to verify visualization endpoint works
"""
import asyncio
import sys
import os
from fastapi import FastAPI
from fastapi.responses import JSONResponse
import uvicorn
from datetime import datetime
import json

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.services.visualization_service import VisualizationService

# Create a simple FastAPI app
app = FastAPI()

def json_serializer(obj):
    """JSON serializer for objects not serializable by default json code"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

@app.get("/test-viz/{doc_id}")
async def test_visualization(doc_id: str):
    """Test visualization endpoint"""
    try:
        viz_service = VisualizationService()
        viz_data = await viz_service.generate_visualization_data(doc_id)
        
        if viz_data:
            # Test basic data extraction
            try:
                task_types = [t.value for t in viz_data.task_types]
                charts_count = len(viz_data.charts)
                analysis_date = viz_data.analysis_date.isoformat() if viz_data.analysis_date else None
                
                return JSONResponse(content={
                    "success": True,
                    "doc_id": doc_id,
                    "title": viz_data.title,
                    "task_types": task_types,
                    "charts_count": charts_count,
                    "analysis_date": analysis_date,
                    "summary_keys": list(viz_data.summary.keys()) if viz_data.summary else []
                })
            except Exception as inner_e:
                return JSONResponse(
                    status_code=500,
                    content={"success": False, "error": f"Data extraction error: {str(inner_e)}"}
                )
        else:
            return JSONResponse(
                status_code=404,
                content={"success": False, "error": "No visualization data found"}
            )
    except Exception as e:
        import traceback
        return JSONResponse(
            status_code=500,
            content={"success": False, "error": f"{str(e)} - {traceback.format_exc()}"}
        )

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8004)