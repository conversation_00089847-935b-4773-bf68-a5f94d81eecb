#!/usr/bin/env python3
"""
🎯 AI模型编排系统 - PANTOS核心组件
智能路由和负载均衡不同的AI模型
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import hashlib

logger = logging.getLogger(__name__)

class ModelType(Enum):
    """AI模型类型枚举"""
    PRIMARY_LLM = "primary_llm"
    BACKUP_LLM = "backup_llm"
    EMOTION_MODEL = "emotion_model"
    NETWORK_MODEL = "network_model"
    MULTIMODAL_MODEL = "multimodal_model"
    SPECIALIZED_NLP = "specialized_nlp"

class TaskComplexity(Enum):
    """任务复杂度枚举"""
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"
    CRITICAL = "critical"

@dataclass
class ModelCapability:
    """模型能力描述"""
    model_id: str
    model_type: ModelType
    supported_tasks: List[str]
    max_tokens: int
    cost_per_token: float
    avg_response_time: float
    accuracy_score: float
    availability: float
    current_load: int = 0
    max_concurrent: int = 10

@dataclass
class AnalysisRequest:
    """分析请求对象"""
    task_type: str
    content: str
    document_id: str
    priority: int = 0
    complexity: TaskComplexity = TaskComplexity.MEDIUM
    requires_multimodal: bool = False
    max_wait_time: int = 30  # 秒
    fallback_allowed: bool = True

class ModelOrchestrator:
    """AI模型编排器 - PANTOS的智能大脑"""
    
    def __init__(self):
        self.models: Dict[str, ModelCapability] = {}
        self.task_history: Dict[str, List[Dict]] = {}
        self.performance_cache: Dict[str, Any] = {}
        self.circuit_breakers: Dict[str, Dict] = {}
        
        # 初始化模型注册表
        self._initialize_model_registry()
        
        # 启动性能监控
        self.monitoring_task = None
        
    def _initialize_model_registry(self):
        """初始化模型注册表"""
        
        # 主力大语言模型
        self.models["zhipu_glm4_5"] = ModelCapability(
            model_id="zhipu_glm4_5",
            model_type=ModelType.PRIMARY_LLM,
            supported_tasks=["task_1", "task_2", "task_3", "task_4", "task_5", "task_6"],
            max_tokens=8192,
            cost_per_token=0.0001,
            avg_response_time=2.5,
            accuracy_score=0.92,
            availability=0.99,
            max_concurrent=20
        )
        
        # 备用大语言模型
        self.models["openai_gpt4"] = ModelCapability(
            model_id="openai_gpt4",
            model_type=ModelType.BACKUP_LLM,
            supported_tasks=["task_1", "task_2", "task_3", "task_4", "task_5", "task_6"],
            max_tokens=8192,
            cost_per_token=0.0003,
            avg_response_time=3.0,
            accuracy_score=0.94,
            availability=0.98,
            max_concurrent=15
        )
        
        # 情感分析专门模型
        self.models["emotion_roberta_large"] = ModelCapability(
            model_id="emotion_roberta_large",
            model_type=ModelType.EMOTION_MODEL,
            supported_tasks=["task_9", "task_10"],
            max_tokens=512,
            cost_per_token=0.00005,
            avg_response_time=0.8,
            accuracy_score=0.96,
            availability=0.99,
            max_concurrent=50
        )
        
        # 网络分析模型
        self.models["graph_transformer"] = ModelCapability(
            model_id="graph_transformer",
            model_type=ModelType.NETWORK_MODEL,
            supported_tasks=["task_11", "task_12"],
            max_tokens=2048,
            cost_per_token=0.0002,
            avg_response_time=4.0,
            accuracy_score=0.89,
            availability=0.97,
            max_concurrent=10
        )
        
        # 多模态模型
        self.models["clip_large"] = ModelCapability(
            model_id="clip_large",
            model_type=ModelType.MULTIMODAL_MODEL,
            supported_tasks=["multimodal_analysis"],
            max_tokens=1024,
            cost_per_token=0.0005,
            avg_response_time=5.0,
            accuracy_score=0.88,
            availability=0.96,
            max_concurrent=5
        )
        
        logger.info(f"已注册 {len(self.models)} 个AI模型")
    
    async def route_analysis_request(self, request: AnalysisRequest) -> Dict[str, Any]:
        """智能路由分析请求"""
        
        start_time = datetime.now()
        
        try:
            # 1. 选择最优模型
            optimal_model = await self._select_optimal_model(request)
            
            if not optimal_model:
                raise Exception(f"没有可用的模型处理任务: {request.task_type}")
            
            # 2. 负载检查
            if not await self._check_model_availability(optimal_model):
                if request.fallback_allowed:
                    optimal_model = await self._select_fallback_model(request)
                else:
                    raise Exception(f"模型 {optimal_model} 当前不可用")
            
            # 3. 执行分析
            result = await self._execute_analysis(optimal_model, request)
            
            # 4. 记录性能数据
            execution_time = (datetime.now() - start_time).total_seconds()
            await self._record_performance(optimal_model, request, execution_time, True)
            
            return {
                "success": True,
                "result": result,
                "model_used": optimal_model,
                "execution_time": execution_time,
                "cost_estimate": self._calculate_cost(optimal_model, request)
            }
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"分析请求执行失败: {e}")
            
            # 记录失败
            if 'optimal_model' in locals():
                await self._record_performance(optimal_model, request, execution_time, False)
            
            return {
                "success": False,
                "error": str(e),
                "execution_time": execution_time
            }
    
    async def _select_optimal_model(self, request: AnalysisRequest) -> Optional[str]:
        """选择最优模型"""
        
        # 获取支持该任务的模型
        candidate_models = [
            model_id for model_id, capability in self.models.items()
            if request.task_type in capability.supported_tasks
        ]
        
        if not candidate_models:
            return None
        
        # 多维度评分系统
        model_scores = {}
        
        for model_id in candidate_models:
            model = self.models[model_id]
            
            # 基础评分 (准确性权重40%)
            score = model.accuracy_score * 0.4
            
            # 响应时间评分 (速度权重25%)
            time_score = max(0, 1 - (model.avg_response_time / 10))
            score += time_score * 0.25
            
            # 可用性评分 (稳定性权重20%)
            score += model.availability * 0.2
            
            # 成本评分 (成本权重10%)
            cost_score = max(0, 1 - (model.cost_per_token / 0.001))
            score += cost_score * 0.1
            
            # 负载平衡评分 (负载权重5%)
            load_ratio = model.current_load / model.max_concurrent
            load_score = max(0, 1 - load_ratio)
            score += load_score * 0.05
            
            model_scores[model_id] = score
        
        # 返回得分最高的模型
        optimal_model = max(model_scores.items(), key=lambda x: x[1])[0]
        
        logger.info(f"为任务 {request.task_type} 选择模型: {optimal_model} (得分: {model_scores[optimal_model]:.3f})")
        
        return optimal_model
    
    async def _check_model_availability(self, model_id: str) -> bool:
        """检查模型可用性"""
        
        model = self.models.get(model_id)
        if not model:
            return False
        
        # 检查负载
        if model.current_load >= model.max_concurrent:
            return False
        
        # 检查熔断器状态
        if self._is_circuit_breaker_open(model_id):
            return False
        
        return True
    
    async def _select_fallback_model(self, request: AnalysisRequest) -> Optional[str]:
        """选择备用模型"""
        
        # 简化的备用模型选择逻辑
        for model_id, capability in self.models.items():
            if (request.task_type in capability.supported_tasks and 
                await self._check_model_availability(model_id)):
                logger.warning(f"使用备用模型: {model_id}")
                return model_id
        
        return None
    
    async def _execute_analysis(self, model_id: str, request: AnalysisRequest) -> Dict[str, Any]:
        """执行分析任务"""
        
        # 增加模型负载计数
        self.models[model_id].current_load += 1
        
        try:
            # 模拟模型调用 (在实际实现中会调用具体的AI服务)
            await asyncio.sleep(self.models[model_id].avg_response_time)
            
            # 生成模拟结果
            result = {
                "analysis_id": self._generate_analysis_id(request),
                "model_version": f"{model_id}_v1.0",
                "confidence_score": 0.85 + (self.models[model_id].accuracy_score - 0.85),
                "processing_time": self.models[model_id].avg_response_time,
                "analysis_data": {
                    "task_type": request.task_type,
                    "document_id": request.document_id,
                    "summary": f"使用模型 {model_id} 完成 {request.task_type} 分析"
                }
            }
            
            return result
            
        finally:
            # 减少模型负载计数
            self.models[model_id].current_load -= 1
    
    async def _record_performance(self, model_id: str, request: AnalysisRequest, 
                                execution_time: float, success: bool):
        """记录性能数据"""
        
        performance_record = {
            "timestamp": datetime.now().isoformat(),
            "model_id": model_id,
            "task_type": request.task_type,
            "execution_time": execution_time,
            "success": success,
            "document_id": request.document_id
        }
        
        # 更新任务历史
        if request.task_type not in self.task_history:
            self.task_history[request.task_type] = []
        
        self.task_history[request.task_type].append(performance_record)
        
        # 保持最近1000条记录
        if len(self.task_history[request.task_type]) > 1000:
            self.task_history[request.task_type] = self.task_history[request.task_type][-1000:]
        
        # 更新熔断器状态
        self._update_circuit_breaker(model_id, success)
    
    def _calculate_cost(self, model_id: str, request: AnalysisRequest) -> float:
        """计算成本估算"""
        
        model = self.models.get(model_id)
        if not model:
            return 0.0
        
        # 简化的成本计算 (基于内容长度估算token数)
        estimated_tokens = len(request.content.split()) * 1.3  # 假设1.3个token每个词
        
        return estimated_tokens * model.cost_per_token
    
    def _generate_analysis_id(self, request: AnalysisRequest) -> str:
        """生成分析ID"""
        
        content_hash = hashlib.md5(
            f"{request.document_id}_{request.task_type}_{datetime.now().isoformat()}"
            .encode()
        ).hexdigest()[:8]
        
        return f"analysis_{content_hash}"
    
    def _is_circuit_breaker_open(self, model_id: str) -> bool:
        """检查熔断器是否开启"""
        
        breaker = self.circuit_breakers.get(model_id, {})
        
        if breaker.get("is_open", False):
            # 检查是否可以尝试半开状态
            last_failure = breaker.get("last_failure")
            if last_failure:
                time_since_failure = datetime.now() - datetime.fromisoformat(last_failure)
                if time_since_failure > timedelta(minutes=5):  # 5分钟后尝试恢复
                    self.circuit_breakers[model_id]["is_open"] = False
                    return False
            return True
        
        return False
    
    def _update_circuit_breaker(self, model_id: str, success: bool):
        """更新熔断器状态"""
        
        if model_id not in self.circuit_breakers:
            self.circuit_breakers[model_id] = {
                "failure_count": 0,
                "last_failure": None,
                "is_open": False
            }
        
        breaker = self.circuit_breakers[model_id]
        
        if success:
            breaker["failure_count"] = 0
            breaker["is_open"] = False
        else:
            breaker["failure_count"] += 1
            breaker["last_failure"] = datetime.now().isoformat()
            
            # 连续失败5次后开启熔断器
            if breaker["failure_count"] >= 5:
                breaker["is_open"] = True
                logger.warning(f"模型 {model_id} 熔断器开启，连续失败 {breaker['failure_count']} 次")
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        
        status = {
            "timestamp": datetime.now().isoformat(),
            "total_models": len(self.models),
            "available_models": sum(1 for model_id in self.models.keys() 
                                 if not self._is_circuit_breaker_open(model_id)),
            "models": {}
        }
        
        for model_id, model in self.models.items():
            status["models"][model_id] = {
                "type": model.model_type.value,
                "current_load": model.current_load,
                "max_concurrent": model.max_concurrent,
                "availability": model.availability,
                "avg_response_time": model.avg_response_time,
                "is_circuit_breaker_open": self._is_circuit_breaker_open(model_id),
                "supported_tasks": model.supported_tasks
            }
        
        return status
    
    async def get_performance_analytics(self, 
                                      task_type: Optional[str] = None,
                                      time_range_hours: int = 24) -> Dict[str, Any]:
        """获取性能分析报告"""
        
        cutoff_time = datetime.now() - timedelta(hours=time_range_hours)
        
        analytics = {
            "time_range_hours": time_range_hours,
            "summary": {},
            "by_task_type": {},
            "by_model": {}
        }
        
        total_requests = 0
        successful_requests = 0
        total_execution_time = 0
        
        # 分析任务历史数据
        for task_type_key, records in self.task_history.items():
            if task_type and task_type_key != task_type:
                continue
                
            recent_records = [
                r for r in records 
                if datetime.fromisoformat(r["timestamp"]) > cutoff_time
            ]
            
            if not recent_records:
                continue
            
            task_successful = sum(1 for r in recent_records if r["success"])
            task_total = len(recent_records)
            task_avg_time = sum(r["execution_time"] for r in recent_records) / task_total
            
            analytics["by_task_type"][task_type_key] = {
                "total_requests": task_total,
                "successful_requests": task_successful,
                "success_rate": task_successful / task_total if task_total > 0 else 0,
                "avg_execution_time": task_avg_time,
                "error_rate": (task_total - task_successful) / task_total if task_total > 0 else 0
            }
            
            total_requests += task_total
            successful_requests += task_successful
            total_execution_time += sum(r["execution_time"] for r in recent_records)
        
        # 总体统计
        analytics["summary"] = {
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "overall_success_rate": successful_requests / total_requests if total_requests > 0 else 0,
            "avg_execution_time": total_execution_time / total_requests if total_requests > 0 else 0
        }
        
        return analytics

# 全局编排器实例
model_orchestrator = ModelOrchestrator()

async def initialize_orchestrator():
    """初始化编排器"""
    logger.info("AI模型编排系统初始化完成")
    return model_orchestrator

async def route_analysis(request: AnalysisRequest) -> Dict[str, Any]:
    """对外接口：路由分析请求"""
    return await model_orchestrator.route_analysis_request(request)

async def get_orchestrator_status() -> Dict[str, Any]:
    """对外接口：获取编排器状态"""
    return await model_orchestrator.get_system_status()

if __name__ == "__main__":
    # 测试编排器
    async def test_orchestrator():
        orchestrator = await initialize_orchestrator()
        
        # 创建测试请求
        test_request = AnalysisRequest(
            task_type="task_1",
            content="测试文档内容用于演示AI模型编排系统的智能路由功能",
            document_id="test_doc_001",
            priority=1,
            complexity=TaskComplexity.MEDIUM
        )
        
        # 执行分析
        result = await route_analysis(test_request)
        print(f"分析结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 获取系统状态
        status = await get_orchestrator_status()
        print(f"系统状态: {json.dumps(status, indent=2, ensure_ascii=False)}")
        
        # 获取性能分析
        analytics = await orchestrator.get_performance_analytics()
        print(f"性能分析: {json.dumps(analytics, indent=2, ensure_ascii=False)}")
    
    asyncio.run(test_orchestrator())
