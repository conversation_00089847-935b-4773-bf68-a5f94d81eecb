{"doc_id": "bf4ab72f-9f0c-4ef5-9cb8-34e5cd40e635", "title": "item099_US_Reducing the Risks of Artificial Intelligence for Military Decision Advantage", "text": "Introduction\r\nAdvanced militaries are pursuing artificial intelligence (AI) to gain decision advantage\r\nin future conflicts. Fueling their sense of urgency is the rapid advancement of machine\r\nlearning applications in recent years toward more general-purpose capabilities that\r\ncan handle complex, real-world problems. Applications of ML will augment and\r\nautomate wide-ranging military functions from collecting and fusing intelligence to\r\nrecommending courses of action to executing cyber and electronic warfare operations.\r\nBut ML systems do not reason like humans; they look for patterns in data to make\r\npredictions. As these systems take on increasingly important roles, how will they\r\nimpact human decision-making in a crisis or conflict?\r\nThis question is salient for three reasons. First, even the best ML systems are often\r\nbrittle and unreliable. They work well under ideal conditions but quickly fail in the face\r\nof unforeseen changes in the environment or malicious interference. There is\r\nconsiderable effort to make robust ML systems capable of operating reliably in the face\r\nof changing environmental conditions and adversarial interference.1 But techniques to\r\nimprove robustness are not mature enough to ensure reliable performance even under\r\nrelatively benign conditions, much less those of warfare. The need for systems to learn\r\nand update in deployment makes this problem even more challenging. As a result,\r\nthese capabilities could have baked-in vulnerabilities and flaws with potential\r\nconsequences that are not fully understood.\r\nSecond, militaries will face competitive pressures to adopt ML systems despite their\r\nuncertain reliability. Militaries are developing ML applications to gain decision\r\nadvantage in a variety of contexts including intelligence, surveillance, and\r\nreconnaissance (ISR), decision support, and cyber and electronic warfare. The fear of\r\nfalling behind an adversary’s AI capabilities may trigger a race to deploy systems with\r\nunproven reliability.2 This temptation may be particularly strong for seemingly\r\ninnocuous applications in support of human decision-making or in nonlethal roles such\r\nas cyber operations.\r\nThird, these powerful but flawed ML capabilities could plausibly be deployed on both\r\nsides of a U.S.-China confrontation already fraught with escalation risks. With the\r\nbilateral relationship becoming increasingly fractious, commentators have begun to\r\nraise the alarm over the increased risk of escalation.3 However, both countries share a\r\nmutual interest in avoiding war. Decision makers on each side are aware of the\r\npotential risks of deploying untested and unreliable ML capabilities. Still, the fear of\r\nthe other side gaining an edge may be stronger than concerns over fielding unproven\r\nCenter for Security and Emerging Technology | 5\r\nsystems.4 The greatest threat may be the mutual risk posed by the deployment of\r\nflawed ML systems that undermine stability.\r\nMoreover, ML will become a significant asset to militaries, and could be applied in\r\nways that reduce risks in crisis and conflict. This combination of promise and peril\r\nmakes it essential to understand the potential impacts of the technical characteristics,\r\nquirks, and flaws of these capabilities––especially where they could intersect with\r\nstrategic concerns like crisis management. This policy brief focuses specifically on risks\r\narising from how states design and develop AI systems, and how these systems are\r\nincorporated into critical functions that directly or indirectly influence decision-making.\r\nIt leverages the growing body of technical literature on ML failure modes to consider\r\nhow these could interact with the strategic pressures and human factors giving rise to\r\nescalation risks. The focus on these worst-case scenarios is not to argue that military\r\nAI will be inherently destabilizing––like other technologies, AI capabilities could\r\ninteract with other risk factors in both positive and negative ways.5 The aim here is to\r\nidentify the most dangerous possibilities in order to provide recommendations to\r\nreduce risks.\r\nThis policy brief begins with the technical context: how ML could be applied in the near\r\nto medium term to gain decision advantage in warfare, how ML systems can fail in the\r\nface of dynamic conditions or adversarial interference, and why this problem is unlikely\r\nto be eliminated through technical solutions alone. It then considers the strategic\r\ncontext: how AI might be deployed in a future U.S.-China military confrontation. It then\r\nmaps potential ML risks onto existing escalation pathways. Finally, it identifies\r\npossible measures to mitigate these risks in the development and deployment of ML\r\nsystems.\r\nCenter for Security and Emerging Technology | 6\r\nThe Technical Context: Applying AI in the Military Domain\r\nThe relentless advance of ML over the last decade has brought military applications of\r\nAI closer to reality.6 The combination of cheaper computing power, mass data\r\ncollection, and deep neural network architectures fueled a step change in the capability\r\nof ML systems. These systems are not just getting better at a growing range of tasks,\r\nthey are evolving toward more generalpurpose capabilities that can solve\r\nproblems with less reliance on human\r\nexpertise and narrow, controlled settings.\r\nMuZero, one of DeepMind’s game-playing\r\nreinforcement learning-based systems\r\nannounced in 2019, garnered far less\r\nattention than its predecessor, AlphaGo.7\r\nAlphaGo beat world champion Lee Sedol at\r\nGo in 2016, revealing the power of reinforcement learning to search for and discover\r\noptimal strategies in a highly complex game. But it still relied on a foundation of\r\nhuman knowledge and pre-defined, fixed rules and parameters that made the\r\nchallenge tractable. MuZero surpassed its performance, but more importantly it did so\r\nwithout any prior knowledge of the game to rely on––just an objective to maximize. Far\r\nmore so than AlphaGo, it demonstrates a capability that could be applied to real-world\r\nproblems without predefined rules or set boundaries, where the number of possible\r\nmoves and counter-moves may be virtually infinite, and where there may be little\r\nhuman expertise to draw from.\r\nIndeed, MuZero served as the basis for an “AI copilot” developed by the U.S. Air Force\r\nto locate adversary air defenses. It took only five weeks and five hundred thousand\r\nsimulations to train “ArtuMu” before giving it control over radar and sensors in a test\r\nflight in December 2020.8 The air force is now developing “CetuMu” with jamming\r\ncapabilities to thwart ArtuMu in simulated matches; this type of adversarial training is\r\na powerful technique, which may reveal new electronic warfare tactics only feasible for\r\nsystems operating at machine speed.9\r\nFor now, real-world ML applications like ArtuMu remain largely experimental. But the\r\nprogression from AlphaGo to ArtuMu suggests that the significant potential of ML is\r\ngetting closer to the battlefield. It may soon become technically feasible for militaries\r\nto apply ML in a variety of roles for decision advantage––despite whether or not it is\r\nwise to do so.\r\nThe relentless advance of\r\nML over the last decade has\r\nbrought military applications\r\nof AI closer to reality.\r\nCenter for Security and Emerging Technology | 7\r\nProspects for ML Applications for Decision Advantage\r\nDecision advantage means making better and faster decisions than an adversary. A\r\nmilitary gains it by deploying superior capabilities not only to collect and harness\r\ninformation, but also to protect crucial information and communication flows––and\r\ntarget those of an adversary. ML applications in a range of areas may contribute to\r\ndecision advantage, many of which are far more mature than experiments at the\r\ncutting edge, like ArtuMu.\r\n● ISR: ML can automate aspects of collection and processing, such as the\r\nidentification of objects, selection of potential targets for collection, and\r\nguidance of sensors. ML capabilities for image and audio classification and\r\nnatural language processing are already being used in nonmilitary contexts such\r\nas autonomous vehicles and translation services. These use cases have direct\r\nparallels in military contexts. ML could also fuse data from multiple sources,\r\ndramatically reducing the time from collection to dissemination.\r\n● Decision support: ML can augment and assist human decision-making in a\r\nnumber of ways. First, ML systems could enhance situational awareness by\r\ncreating and updating in real time a common operational picture derived from\r\nmultiple sensors in multiple domains. Second, ML could perform planning and\r\ndecision support functions, including matching available weapon systems to\r\ntargets, generating recommended courses of action and assessing the likelihood\r\nof success for various options.\r\n10 These functions are particularly important for\r\ncoordinating joint operations across domains (space, air, land, sea, and cyber).\r\n● Electronic warfare: ML is well suited for tasks such as analyzing, parsing, and\r\nfiltering signals in support of electromagnetic spectrum operations. These\r\nfunctions have become more technically demanding as radar and\r\ncountermeasures have evolved. Innovations, such as in infrared search and\r\ntrack, have expanded the range of the electromagnetic spectrum capable of\r\nbeing harnessed. But the more game-changing possibility is “cognitive\r\nelectronic warfare”––the use of AI to enable capabilities that adapt\r\nautomatically to adversary tactics and synthesize countermeasures in real\r\ntime.\r\n11\r\n● Cyber warfare: Cybersecurity is ripe for applications of ML. On the defensive\r\nside, ML-enabled intrusion detection can leverage vast amounts of data on\r\nnetwork activity to spot anomalous behavior, while ML-enabled antivirus\r\nsystems can discover patterns in malware to identify unseen or evasive\r\nCenter for Security and Emerging Technology | 8\r\nvariants.12 While more speculative, on the offensive side, attackers might use\r\nML-enabled capabilities to probe adversary networks for weaknesses, gain\r\naccess and spread through networks more stealthily. ML might assist with\r\nspecific offensive challenges like developing payloads to manipulate industrial\r\ncontrol systems, which can require extensive domain-specific knowledge.13\r\nWhile it is easy to overhype ML, these applications are at least plausible in the near to\r\nmedium term for three reasons. First, these applications are technically feasible, even if\r\nsome are not necessarily likely to reach a level of maturity required for deployment.\r\nFor instance, ML has been used for years in limited roles such as malware analysis and\r\nintrusion detection in cybersecurity, but applications based on reinforcement learning\r\nremain experimental.14 Second, these applications may be more politically palatable or\r\nless likely to engender the level of resistance that lethal autonomous weapon systems\r\nface. Militaries may be more willing to deploy systems with greater degrees of\r\nautonomy if they do not trigger as many vexing policy, legal, and ethical questions as\r\nLAWS. Finally, these applications are in areas widely considered to be strategically\r\nimperative for decision advantage in future conflicts, fueling the potential pressures to\r\ndevelop and deploy them. With dwindling barriers to deployment, it is worth exploring\r\nthe potential implications of these capabilities.\r\nFailure Modes in ML\r\nML risks fall into two broad categories: safety and security. The former includes the\r\nrisks of ML systems unintentionally failing due to flaws in the training process or\r\nchanging conditions in the deployment environment. The latter includes the risks of\r\nintentional interference by a malicious actor causing an ML system to fail.15\r\nUnintentional failure modes can result from training data that do not represent the full\r\nrange of conditions or inputs that a system will face in deployment.16 The environment\r\ncan change in ways that cause the data used by the model during deployment to differ\r\nsubstantially from the data used to train the model. Such a “distributional shift” in the\r\ndata often results in rapid degradation in the accuracy of the model’s predictions.17 For\r\ninstance, ML models used by Amazon were not trained on “pandemic behavior” like\r\nbulk-buying products, and their accuracy plummeted at the outset of the COVID-19\r\npandemic.18 A flawed training process can also create an ML system that accomplishes\r\nits primary task, but does so in a manner contrary to the developer’s intent, referred to\r\nas specification gaming.19 An ML system might “hack” its reward, such as the system\r\nthat won a tic-tac-toe competition by causing opponent systems to crash by\r\nrequesting nonexistent moves.20 The system has no intent to cheat at the task, but\r\nsimply finds the most efficient way to accomplish a predetermined objective.\r\nCenter for Security and Emerging Technology | 9\r\nOn the other hand, intentional failure modes include attempts to trick or evade ML\r\nmodels by manipulating the inputs to a system. “Adversarial examples”––inputs (such\r\nas images) subtly altered to fool an ML classifier––demonstrate such evasion attacks.\r\n21\r\nOther attacks include directly hacking data feeds and sensors or shaping the\r\ndeployment environment to deceive a system. In one demonstration, researchers at the\r\nChinese company Tencent placed stickers on a road to trick the lane recognition\r\nsystem in a Tesla self-driving car, causing it to veer into the wrong lane.22 Malicious\r\nactors could also sabotage ML systems during development. Data poisoning attacks\r\ncorrupt training data to undermine a model’s integrity. An attacker with access to a\r\ntraining dataset could simply switch labels on the data to degrade the model’s\r\nperformance. More insidiously, an attacker could create a backdoor so that a model\r\nresponds to a specific input in a way favorable to the attacker. For example, adding a\r\nsmall number of carefully fabricated samples to a training set for an antivirus system\r\ncould cause it to misclassify any sample with a specific digital watermark as benign.23\r\nAdding the watermark to a piece of malware would enable it to slip past defenses\r\nundetected.\r\nThe Robustness Problem\r\nWith growing awareness of these vulnerabilities, AI strategies and proposals\r\nincreasingly acknowledge the need to make ML robust to safety and security risks.\r\nRobustness refers to the ability of ML to perform correctly and reliably under\r\nconditions that would trigger failure modes, including changes in the environment and\r\nadversarial interference.24 Researchers have proposed many techniques to reduce\r\nvulnerabilities and defend ML models\r\nagainst attacks. Yet, there are at least three\r\nreasons to be pessimistic about the\r\nprospects for achieving this goal in the\r\nforeseeable future:\r\nFirst, attackers continue to beat defenders.\r\nA group of researchers put into stark relief\r\nthe challenge facing ML security when, in\r\n2020, they broke a slate of leading\r\nproposed defensive measures against\r\nevasion attacks.25 In many cases, the\r\ndefenses simply had not been properly evaluated against strong attacks.26 Defenses\r\nagainst evasion attacks, in the words of two researchers, were “playing a game of\r\nwhack-a-mole: they close some vulnerabilities, but leave others open.”27 While\r\nWith growing awareness of\r\nthese vulnerabilities, AI\r\nstrategies and proposals\r\nincreasingly acknowledge\r\nthe need to make ML robust\r\nto safety and security risks.\r\nCenter for Security and Emerging Technology | 10\r\nprospective measures could prevent certain attacks like poisoning training data, ML\r\nsystems’ fundamental susceptibility to deception will not be resolved anytime soon.\r\nSecond, testing and evaluating systems for vulnerabilities can be extremely\r\ndifficult. ML systems are highly sensitive to changes to input data. Even a subtle\r\nalteration to just the right features of an input can completely fool a system. Testing an\r\nML system by presenting it with a wide range of likely inputs cannot guarantee that an\r\nattacker will not find one that tricks the system. Testing and evaluation are even more\r\nchallenging for systems that self-adapt in deployment. For instance, an anomaly\r\ndetection system for cybersecurity continuously updates its assumptions about normal\r\nand anomalous network behavior. This can not only invalidate testing and verification,\r\nbut also make the system potentially vulnerable to a patient attacker that gradually\r\ntweaks normal behavior to avoid appearing anomalous.28\r\nThird, fixing ML vulnerabilities often creates other problems.29 To address\r\nvulnerabilities in ML systems, developers have to retrain the system so that it is no\r\nlonger susceptible to that deception. Retraining is not only expensive, but also has\r\ndiminishing returns in terms of addressing the underlying issue. For a typical deep\r\nlearning system, one recent study concluded that “to halve the error rate, you can\r\nexpect to need more than 500 times the computational resources.”30 Developers may\r\ndetermine that marginal improvements to security are not worth exponentially\r\nincreasing costs. Retraining may not even be feasible if developers cannot afford to\r\ntake a system offline or if an attacker has undermined the integrity of training data\r\nentirely.\r\nEqually problematic, fixes like retraining a system with synthetically-generated\r\nadversarial inputs can negatively impact a system’s performance.31 In one experiment,\r\ntraining a robotic system with adversarial inputs to make it robust against attacks that\r\nmanipulated sensors inadvertently made it less accurate overall and more prone to\r\naccidents.32 Hardening a system to one set of attacks from a simulated adversary can\r\nmake it vulnerable to a novel attack.\r\n33 It is possible that training the air force’s ArtuMu\r\nthrough repeated competitions against the hostile CetuMu agent might make it more\r\neffective, but in the process cause it to develop hidden failure modes that could be\r\ntriggered by a novel opponent.34\r\nAt the root of these problems is the fact that ML relies on correlations in data, not\r\nunderstanding causal relationships. This methodology can be incredibly powerful for\r\nsolving certain problems, but it is also a source of weakness. Existing techniques to\r\nimprove robustness cannot change this fundamental characteristic. Instead, they tend\r\nto make trade-offs, making the system perform better under one set of conditions but\r\nCenter for Security and Emerging Technology | 11\r\npotentially worse in others.35 This is a problem in adversarial contexts; an attacker can\r\nadapt its behavior to exploit the lingering weaknesses of the system.\r\nThese persistent problems with safety and security raise the question of whether\r\ndecision makers will trust applications of ML for decision advantage. Operators and\r\nmilitary commanders need to trust that ML systems will operate reliably under the\r\nrealistic conditions of a conflict.36 Ideally, this will militate against a rush to deploy\r\nuntested systems. However, developers, testers, policymakers, and commanders\r\nwithin and between countries may have very different risk tolerances and\r\nunderstandings of trust in AI. Moreover, the pressure to avoid falling behind in the\r\ndeployment of AI may trigger a “race to the bottom” on AI safety, resulting in the\r\nfielding of unreliable systems.37\r\nCenter for Security and Emerging Technology | 12\r\nThe Strategic Context: AI in a U.S.-China Military Confrontation\r\nHarnessing AI has become a priority for both U.S. military and People’s Liberation\r\nArmy (PLA) modernization efforts. Strategists on both sides argue that AI will usher in\r\na new “revolution in military affairs”––a shift from network-centric warfare to decisioncentric warfare.38 Each side is developing\r\ncapabilities and operational concepts to prepare\r\nfor this new form of warfare. While still largely\r\naspirational, these efforts offer insights into how\r\neach side may deploy AI systems for decision\r\nadvantage in a potential conflict.\r\nAI in PLA Military Strategy and Modernization\r\nChinese strategists, decision makers, and Xi\r\nJinping himself appear convinced that AI will\r\nrevolutionize warfare.39 PLA strategists envision\r\na progression from “informatized” warfare enabled by information and communications\r\ntechnologies to “intelligentized” warfare, which leverages AI, big data, cloud\r\ncomputing and related technologies.40 Under such conditions, PLA strategists believe\r\nwars will be won through “systems destruction warfare” focused on paralyzing and\r\ndestroying the operational systems of adversary forces.41 By asymmetrically targeting\r\nan adversary’s command, control, and communications, the PLA aims to overcome a\r\nconventionally superior military.\r\nPLA spending on the development of AI-enabled systems is now upwards of $1.6\r\nbillion according to analysis of PLA procurement data by the Center for Security and\r\nEmerging Technology (CSET).42 While much of this spending goes toward the\r\ndevelopment of autonomous systems and other support functions such as logistics and\r\npredictive maintenance, a major area of focus is developing capabilities to prevail in\r\nsystems destruction warfare. Naturally, a key driver of these investments is the PLA\r\nStrategic Support Force (SSF), created in 2015 to consolidate PLA space, cyber,\r\nelectronic, and information warfare capabilities.43 Examples of PLA efforts to apply AI\r\nfor decision advantage include the following:\r\n• ISR: The PLA is applying ML to fuse information from systems and sensors\r\nacross all domains of warfare to improve situational awareness and decisionmaking.44 This includes capabilities for fusing satellite data and multisource\r\nsensor data, particularly in the maritime domain.45 The PLA also seeks to\r\nHarnessing AI has\r\nbecome a priority for\r\nboth U.S. military and\r\nPeople’s Liberation\r\nArmy (PLA)\r\nmodernization efforts.\r\nCenter for Security and Emerging Technology | 13\r\nenhance early-warning capabilities through the “intelligentized analysis” of\r\nmassive data via deep learning.46\r\n• Decision support: Inspired by AlphaGo’s success, China’s Central Military\r\nCommission Joint Staff Department called for a joint operations command\r\nsystem employing AI to support decision-making in 2016. 47 Around the same\r\ntime, PLA units began experimentation with such capabilities including an\r\nintelligentized joint operations C2 demonstration system developed by the\r\nNational University of Defense Technology, described as an “external brain” for\r\ncommanders.48\r\n Chinese AI companies like DataExa advertise combat decision\r\nsupport services, including the real-time prediction of the movement of foreign\r\nweapons platforms.49\r\n While progress toward operational deployment is\r\ndifficult to assess, PLA strategists believe AI-enabled command decisionmaking is not only possible, but inevitable.50\r\n• Electronic warfare: The PLA is pursuing AI to enable it to manage an ever more\r\ndynamic and contested electromagnetic space. PLA procurement contracts\r\ninclude systems for automatic frequency modulation, microwave jamming,\r\nbroadband automatic gain control, and multisource signal separation; CSET’s\r\nanalysis found numerous contracts focused on “jamming and blinding enemy\r\nsensor networks and using AI for cognitive electronic warfare.”51\r\n• Cyber warfare: Available evidence suggests PLA investments into AI-enabled\r\ncyber capabilities are focused largely on improving its defenses, such as an SSF\r\ncontract for an AI-enabled “cyber threat intelligent sensing and early warning\r\nplatform.”52 A recent report from the Ministry of National Defense claims that\r\nthe National Defense University’s School of Electronic Warfare has developed\r\nAI-based capabilities for automated network defense.53 However, research and\r\nexperimentation with ML could be utilized offensively. Chinese universities with\r\nknown connections to state-sponsored hacking groups conduct research on ML\r\nsecurity and cyber applications.54 There is also evidence of experimentation with\r\nAI-enabled capabilities at cyber ranges used to practice offensive and defensive\r\ncyber operations.55\r\nAI in U.S. Military Strategy and Modernization\r\nIn 2014, AI became a priority for the U.S. military under the Third Offset Strategy,\r\nwhich sought to harness advanced technologies to offset increases in Chinese and\r\nRussian conventional capabilities.56 Four years later, the Department of Defense (DOD)\r\nannounced its Artificial Intelligence Strategy and created the Joint Artificial Intelligence\r\nCenter for Security and Emerging Technology | 14\r\nCenter to guide the integration of AI systems into decision-making and operations\r\nacross the military.57 The JAIC was subsequently incorporated into the Chief Digital\r\nand Artificial Intelligence Office.\r\nThe JAIC’s 2021 AI baseline inventory included 685 AI-related projects and accounts\r\nacross the DOD.58 Applications related to decision advantage in warfare likely\r\ncomprise a small fraction, but include priority areas for modernization. Examples of\r\nsuch efforts include the following:\r\n• ISR: AI-enabled collection and fusion of data from multiple domains into a\r\ncommon operational picture is central to the DOD’s vision for Joint All-Domain\r\nCommand and Control (JADC2).59 JADC2 incorporates a range of initiatives by\r\nindividual services. For instance, the U.S. Army’s Tactical Intelligence Targeting\r\nAccess Node program aims to use ML to synthesize data from ground, aerial,\r\nspace, and aerospace sensors.60 Similarly, the air force is researching the use of\r\nalgorithms to process and fuse sensor data in its Advanced Battle Management\r\nSystem.61\r\n• Decision support: The DOD’s JADC2 Strategy states that it “will leverage\r\nArtificial Intelligence and Machine Learning to help accelerate the commander’s\r\ndecision cycle.”62 In March 2021, Northern Command tested AI-enabled\r\n“decision aids” designed to enable domain awareness, information dominance,\r\nand cross-command collaboration.63\r\n• Electronic warfare: A 2016 study by the DOD’s Defense Science Board argued\r\nthat the automation of sensors, communications, and jamming coordination\r\nusing AI could “achieve information dominance while imposing high ‘cost’ and\r\ndisruption on adversaries.”64 The DOD’s 2020 Command, Control, and\r\nCommunications (C3) Modernization Strategy calls for the application of AI to\r\nenable “agile electromagnetic spectrum operations” in support of C3.65 The U.S.\r\nmilitary is already incorporating capabilities that facilitate the analysis of signals\r\nacross the electromagnetic spectrum to better adapt to adversary systems into\r\noperational electronic warfare systems.66\r\n• Cyber warfare: Research applying ML to counter cyberattacks includes the\r\nDefense Advanced Research Projects Agency’s Harnessing Autonomy for\r\nCountering Cyberadversary Systems program, focused on automatically taking\r\ndown botnets by identifying infected computers, exploiting vulnerabilities to\r\naccess them, and removing botnet implants.67 Partnerships under the army’s\r\nCollaborative Research Alliances include research on AI-enabled cyber\r\nCenter for Security and Emerging Technology | 15\r\ndefenses and counter-AI measures.68 More ambitiously, DOD’s Project IKE seeks\r\nto create a common command and control architecture for cyber operations\r\ncapable of generating different courses of action, and even automatically\r\nexecuting operations using AI.69 The objective, as described by the DOD, is to\r\nuse AI/ML to “assist human understanding of the cyber battlespace, support\r\ndevelopment of cyber warfare strategies and measure and model battle\r\ndamage assessment.”70\r\nTaking Stock: AI in a U.S.-China Confrontation\r\nMany of the desired capabilities are likely years away from being field-ready. Even\r\nafter a system is fully trained, tested, and evaluated, it faces numerous hurdles to\r\ndeployment. Militaries must integrate ML systems into existing legacy systems,\r\ndoctrine, and operational planning. Human\r\npersonnel must be trained, operational concepts\r\ndeveloped, and organizational structures\r\nadapted. In short, it should not be overstated\r\nhow close AI is to being used on the battlefield.\r\nWhat is increasingly clear, however, is that this\r\nis not purely science fiction. Five to ten years out,\r\nit is plausible that AI will begin to impact\r\nstrategic decision-making. ML will shape the information used in critical decisionmaking processes and influence leaders’ perceptions of their adversaries. It could take\r\non more of the burden in developing and evaluating options in operational planning\r\nand execution. Tactical-level decisions from ML systems may guide operations in areas\r\nwhere speed and complexity overwhelm human operators. This makes it prudent to try\r\nand anticipate and proactively manage the risks these systems could introduce.\r\nIt should not be\r\noverstated how close AI\r\nis to being used on the\r\nbattlefield.\r\nCenter for Security and Emerging Technology | 16\r\nAI Failures and Escalation Risks\r\nNumerous studies have cataloged the possible destabilizing effects of AI, including its\r\npotential to accelerate the speed of conflict, generate disinformation to sow confusion,\r\nor even undermine nuclear deterrence.71 This report leverages the vast technical\r\nliterature detailing how and why ML fails to focus on those escalation risks arising\r\nfrom the interaction of technical failures with strategic pressures and human factors in\r\na crisis or conflict. But before examining specific escalation pathways, it is worth noting\r\nseveral features present in the U.S.-China context that make the possible introduction\r\nof flawed AI capabilities particularly concerning.\r\nFirst, the strategic advantage from targeting an adversary’s ML capabilities in a future\r\nconflict will motivate more aggressive actions to compromise an adversary’s ML\r\ncapabilities in anticipation of a conflict. As militaries come to rely on AI systems, we\r\ncan expect efforts to trigger failure modes in adversary systems to become a regular\r\npart of warfare––whether directly (e.g., through hacking) or indirectly, such as by\r\naltering tactics to throw off predictions. Acquiring detailed insights into a target ML\r\nsystem, including its architecture, parameters, or training data, enables more reliable\r\nand sophisticated attacks.72 U.S. and PLA strategists alike already recognize that the\r\nability to degrade or destroy an adversary’s information and decision-making\r\ncapabilities will require extensive operational preparation of the environment (OPE) in\r\nadvance of conflict.73 The deployment of ML capabilities will likely further incentivize\r\nactions prior to a conflict to hunt for vulnerabilities, compromise training processes, or\r\neven sabotage capabilities during development.\r\nSecond, psychological pressures could compound the impacts of system failures in a\r\ncrisis. Crises and conflicts put intense pressures on decision makers to respond quickly\r\nto developments and act faster than their opponents. Under such conditions, ML\r\nsystem failures may be more likely to go undetected or unremedied. There simply may\r\nnot be enough time to second guess a system’s output, such as a questionable\r\nindicator of warning. Decision makers may be more prone to automation bias, or the\r\ntendency to accept uncritically the assessments provided by a system.74 Time\r\npressures can also magnify the impacts of surprise, such as the alarm created by the\r\ndiscovery of a compromised system.75 If a compromise or failure is detected, there\r\nmight not be enough time to diagnose the problem, understand its origin or impacts,\r\nand take steps to fix it like retraining a system.\r\nThird, preexisting sources of misperception could compound misunderstandings arising\r\nfrom incidents involving ML systems. For example, Chinese strategists appear to\r\noverestimate U.S. technological capabilities in general and AI capabilities specifically.76\r\nCenter for Security and Emerging Technology | 17\r\nThey may be prone to view any behavior or impact of a U.S. operation as intentional,\r\nand doubt an explanation that an effect was the result of technical malfunction.\r\nMoreover, there is a lack of common understanding of what each side would view as\r\nescalatory, particularly in the context of cyber operations.77 For instance, Chinese\r\ndecision makers may underestimate how alarming actions such as a cyber intrusion\r\ninto sensitive ISR systems might be. They may overestimate U.S. detection and\r\ndiagnosis capabilities and assume that the U.S. could quickly assess an operation and\r\ndiscern the intent behind it.\r\n78 ML systems add another layer of complexity to the\r\nchallenge of understanding an adversary’s capabilities and anticipating how they\r\nmight perceive or misperceive actions.\r\nFourth, ML systems may amplify the fears and worst-case assumptions of their\r\nmakers. Chinese strategists tend to fixate on “false negatives” in early warning, or the\r\nrisk of failing to detect an incoming U.S. attack.79 Such fears could influence the\r\ncreation of training data or simulations in ways that bias an ML model so that it\r\ninterprets ambiguous actions as positive indicators of an imminent attack. By focusing\r\non minimizing false negatives in the design and training of an ML system, the\r\ndevelopers may unintentionally increase the potential for false positives––i.e.,\r\nmisinterpreting incoming data as indication of an attack.\r\nFinally, few crisis management mechanisms exist to provide an off-ramp if an incident\r\ntriggers escalation. The United States and China have pursued hotlines in various\r\nforms, most notably the Defense Telephone Link created in 2008, but it has been\r\nrarely used.80 Amid increasingly frequent Chinese incursions into Taiwanese and\r\nJapanese airspace, U.S. officials have expressed concern over the lack of any reliable,\r\ndirect lines of communication.81 Efforts to develop stronger military-to-military and\r\nleader-to-leader hotlines have run into structural differences between the two\r\nmilitaries, including how each side divides up theater commands.82 Cultural differences\r\ncreate further impediments. Suspicious of U.S. motives, some Chinese officials fear\r\ncrisis hotlines would embolden the United States to act more aggressively and view\r\ntransparency measures as a tool for spying.83 While U.S. officials view these measures\r\nas ways to build trust, for Chinese counterparts, building trust must precede\r\nmechanisms for transparency.84 Such lack of communication and mutual understanding\r\nwill make it harder for both sides to manage any future incident, let alone one\r\ninvolving unexpected behavior by ML capabilities.\r\nEscalation Pathways\r\nThere is little indication that, in their pursuit of AI capabilities for decision advantage,\r\neither the United States or China contemplates turning strategic decision-making over\r\nCenter for Security and Emerging Technology | 18\r\nto machines. As long as humans remain in the driver’s seat, escalation will largely be a\r\nfunction of the political, strategic, and psychological factors at play during a crisis\r\nrather than technology.85 These factors include uncertainties in decision-making––\r\nabout an adversary’s intentions and resolve, their capabilities, or the consequences of\r\nletting them make the first move. If AI systems work as intended, they could reduce\r\nthese uncertainties, which might be stabilizing. But failures in AI systems could interact\r\nwith these uncertainties in ways that lead to misperception and miscalculation. It is\r\nuseful to map these possible interactions onto well-established escalation pathways\r\ndefined by the underlying cause:86\r\nAccidental escalation results when one side’s action has unforeseen and unintended\r\nimpacts that provoke the other side to respond more intensely.\r\nInadvertent escalation results from an intentional action by one side which triggers an\r\nunexpected escalated response. For example, an attack that crosses an undeclared red\r\nline.\r\nDeliberate escalation results from one side’s actions creating circumstances in which\r\nthe other views escalation as rational or necessary—though not necessarily based on a\r\ncompletely rational or informed calculation.\r\nAccidental Escalation\r\nML capabilities could increase the risk of accidentally escalatory impacts of offensive\r\noperations in two ways. First, operations targeting an adversary’s ML systems might\r\nhave unpredictable effects. For the same reason that ML systems are vulnerable to\r\nbegin with––their sensitivity to subtle changes to input data––it can be difficult for an\r\nattacker to predict precisely how manipulating those inputs will affect behavior.\r\nRealistically, the attacker is unlikely to have complete knowledge of the inner workings\r\nof the target system. They will likely have to develop an attack on a substitute model\r\nsimilar to the target model, and hope that the attack will effectively transfer to the\r\nactual target. However, in practice, the attack might result in unexpected effects. An\r\noperation aiming to degrade an ML system’s performance might break it completely.\r\nMore widespread and indiscriminate damage can result from operations targeting\r\neither publicly available datasets, open-source tools, or shared models.87 For instance,\r\nthe effects of poisoning shared datasets could cascade to any model trained on that\r\ndata, making the overall impact even less predictable. In a similar fashion, corrupting a\r\nbase model used to train others could result in widespread compromise to systems\r\nwell beyond the initial target. An adversary might sabotage a development process\r\nCenter for Security and Emerging Technology | 19\r\nwithout realizing that it could corrupt a system deployed in a critical role, such as for\r\nearly warning.\r\nSecond, the incorporation of ML into offensive capabilities may increase the potential\r\nfor unexpected and unintended impacts. An ML capability might not be adequately\r\ntrained for the target environment or could seek to achieve an objective in a way\r\ncontrary to the operator’s intentions. These concerns are particularly acute with cyber\r\noperations. Global cyber incidents like NotPetya in 2017 demonstrate the potential for\r\nmalware to spread far beyond an initial target, with cascading effects.88 Unintended\r\nimpacts of a cyber operation could be highly escalatory, particularly if they affect\r\nhighly sensitive command and control systems––a major concern in the U.S.-China\r\ncontext.89 Consider a scenario where an ML-enabled cyber operation targets an\r\nadversary’s space-based assets in an attempt to signal resolve, but rather than\r\ntemporarily disrupting communications the capability propagates to guidance systems\r\nthat cause damage to the satellite. The adversary could easily misinterpret the\r\noperation as a deliberate strike.\r\nInadvertent Escalation\r\nML systems that are insecure, inadequately trained, or applied to the wrong kinds of\r\nproblems could fail in ways that inject bad information into decision-making, resulting\r\nin actions that inadvertently escalate a crisis or conflict. ML vulnerabilities raise obvious\r\nconcerns, such as the threat from an adversary or a third party hacking a system to\r\ncreate a deception (e.g., making a commercial airplane look like a military one).\r\nLess obvious are the ways in which ML systems could undermine decision-making by\r\nsimply being applied to problems for which AI is not well suited. As Goldfarb and\r\nLindsay argue, determining whether AI is suitable depends on the type of data, nature\r\nof the problem, and the strategic context. They conclude that, ”intelligence about\r\n‘puzzles’ (such as the locations and capabilities of weapon systems) may be more\r\nreliable than intelligence about ‘mysteries’ (such as future intentions and national\r\nresolve).”90\r\nStates in a crisis or at war are unlikely to behave the same as they do during peace.\r\nLike the retail algorithms that could not cope with the shifts in consumer behavior in\r\nthe pandemic, a system trained on years of historical data from peacetime might have\r\nits assumptions and expectations upended by a sudden change in the strategic\r\nlandscape.\r\nCenter for Security and Emerging Technology | 20\r\nFlawed ML-based assessments of an adversary’s actions or intent might lead to\r\nmisperception and miscalculation. Drawing insights from war games, Wong et al.\r\ndescribe one scenario in which an ML system misinterprets an action designed to\r\nsignal the desire to de-escalate, such as the opponent pulling back forces, as a mistake\r\nby the opponent creating an opportunity to gain the upper hand.\r\n91 The system might\r\nrecommend courses of action that inadvertently escalate the confrontation.\r\nPsychological factors could compound these risks. If a flawed assessment serves to\r\nconfirm preexisting suspicions regarding an adversary’s intentions, it may be less likely\r\nto be questioned or scrutinized. Thus, if one side assumes that an adversary is likely to\r\nlaunch a surprise attack, they might not question a system’s warning that an attack is\r\nlikely even if that warning is based on highly ambiguous moves. Even if there is\r\nwarranted skepticism toward an ML system there might not simply be time to\r\ninterrogate a system before a response is required.\r\nDeliberate Escalation\r\nML systems might contribute to a situation in which one side feels pressured to\r\ndeliberately escalate from fear of suffering an imminent attack or the loss of crucial\r\nwarfighting capacities. This pressure arises in part from perceptions of significant firstmover advantages from launching a surprise attack, especially with cyber and\r\nelectronic warfare capabilities, to cripple an opponent’s C3 and ISR capabilities. Each\r\nside may be prone to assume that at the outset of a conflict the other would attack and\r\ntry to paralyze those assets, creating strong temptations to strike first and\r\npreemptively.92 Dependence upon vulnerable ML systems could heighten such fears.\r\nIn this unstable context, even ambiguous moves by one side could be misinterpreted\r\nas preparation for an attack. This is especially true for cyber operations. Offensive\r\ncyber operations may require significant activity in advance to gain access and lay the\r\ngroundwork for an attack, such as compromising dependencies to reach sensitive\r\ntargets and implanting capabilities to enable future access. Militaries are likely to\r\nengage in OPE during peacetime to create offensive options for use in potential\r\ncontingencies. Yet, it is inherently difficult to distinguish cyber intrusions for espionage\r\nfrom OPE or even an attack already underway. If one side detects an intrusion into\r\nsensitive systems in the midst of a crisis, it might assume the worst-case scenario, that\r\nthe intrusion is preparation for an attack. Buchanan and Cunningham argue that such\r\nescalatory risks of cyber espionage and OPE are likely underestimated, particularly on\r\nthe Chinese side, where there appears to be little to no discussion of the possibility of\r\nmisinterpretation and escalation from cyber espionage and OPE.93\r\nCenter for Security and Emerging Technology | 21\r\nThe introduction of ML systems amplifies these risks in two ways. First, cyber\r\nintrusions targeting ML systems might appear even more threatening to the target\r\nstate. An adversary’s attempts to probe a deployed ML system might be even harder\r\nto interpret. It might be impossible to tell whether an adversary has acquired\r\ninformation enabling them to defeat a system. The precise impacts of a compromise\r\ndiscovered in the development process, such as how tampering with training data may\r\nhave impacted the system’s behavior, could be impossible to rapidly assess. Worstcase assumptions might fill the gaps in decision makers’ understanding of the possible\r\nimpacts on a system and the intent behind them.\r\nSecond, there may be no viable options to quickly remediate a compromised ML\r\nsystem, creating a “use it or lose it” dilemma for the target that may pressure them to\r\nstrike. Not only is the process of “patching” or retraining a system often expensive and\r\ntime consuming, it is also hard to do so without affecting the system’s performance in\r\nother ways. Consider an ML-based cyber defense protecting critical C3 systems\r\ndiscovered to contain a backdoor implanted by an adversary during training. The\r\nbackdoor may have been planted well in advance simply to create options, without the\r\nattacker even realizing what systems it would eventually expose. However, the target\r\nof the intrusion may fear that its C3 systems are fatally exposed with no way of rapidly\r\nfixing the vulnerability. Waiting for the adversary to launch a paralyzing attack might\r\nrisk having to fight without the advantages of crucial C3 capabilities.\r\nThe Dilemma of AI for Decision Advantage\r\nThese escalation scenarios reveal a core dilemma of military AI: decision makers want\r\nto use AI to reduce uncertainty––to see the battlefield more clearly, understand the\r\nadversary’s intentions and capabilities, increase confidence in the effectiveness of their\r\nown capabilities and ability to anticipate or withstand an attack. But the potential\r\nunexpected behaviors or failures of AI systems create another source of uncertainty\r\nthat can lead to misperception and miscalculation. Employing or targeting AI might\r\nmake offensive operations less predictable. AI failures might lead to a false sense of\r\ncertainty about an adversary’s moves or intentions. Even the possibility of a hidden\r\nfailure mode could exacerbate fears about the reliability of potentially compromised\r\nsystems. Harnessing AI effectively requires balancing this trade-off in risks. There is no\r\nway to guarantee that a probabilistic AI system will behave exactly as intended, or that\r\nit will give the right answer. Developers and decision makers must be cognizant and\r\nintentional about where they introduce and how they manage these uncertainties to\r\navoid catastrophic outcomes.\r\nCenter for Security and Emerging Technology | 22\r\nRisk Mitigation\r\nIf done right, ML will become a huge asset to militaries––one that could prove\r\nstabilizing if it enhances human decision-making. But managing the potential\r\nconvergence of technical ML risks and human factors at play in a crisis poses a unique\r\nchallenge. Solving it is not simply a matter of making better ML systems via more\r\ntraining data or larger models. ML development\r\nprocesses often focus on maximizing a system’s\r\naccuracy, but not all types of errors are equally\r\nconcerning.\r\n94 Nor are systems that are highly\r\naccurate necessarily risk free (as in the case of an\r\nML-enabled cyber capability that accomplishes its\r\nobjective in a manner contrary to the\r\ncommander’s intent). Thorough testing and\r\nevaluation, validation, and verification (TEVV)\r\npractices will be essential but need to be tailored\r\nto those specific failure modes that could interact\r\nwith escalation risks. Further, steps to incorporate AI capabilities into broader systems\r\nand processes need to assume the potential for system failures, and contain their\r\nimpacts where they would be catastrophic. This analysis suggests three general steps\r\nto reduce the risks of AI applications for decision advantage:\r\nFirst, define a set of mission-specific properties, standards, and requirements for\r\nML systems used for decision advantage. Developers should work with end users,\r\nincluding decision makers, to define necessary characteristics for ML systems in crucial\r\napplication areas to make them more reliable, predicable, and usable. Depending on\r\nthe application, desired characteristics may include the following:\r\n● Metrics incorporated into systems to quantify uncertainty in predictions,\r\nconveying to decision makers the degree of confidence that a prediction is\r\ncorrect.95\r\n● Verifiable robustness properties that can eliminate certain classes of attacks or\r\nfailure modes to enable deliberate and informed trade-offs between different\r\nrisks.96\r\n● Measures to detect distributional shift, abnormal data or adversarial interference\r\nduring deployment.97\r\nIf done right, ML will\r\nbecome a huge asset\r\nto militaries––one that\r\ncould prove stabilizing\r\nif it enhances human\r\ndecision-making.\r\nCenter for Security and Emerging Technology | 23\r\n● Methods to discover emergent properties. For example, Kenton et al. propose a\r\nmethod of discovering when the system is guided by certain “agent incentives”\r\nthat might lead to undesirable behavior.98\r\nSecond, design decision-making processes to limit the potential consequences of\r\nML failures. Technical measures and TEVV practices can only do so much to reduce\r\nthe risks of failure. Certain scenarios involving ML system failures simply cannot be\r\ntolerated. The solution is to circumscribe where and how ML capabilities are integrated\r\ninto decision-making and operations.\r\n● Refrain from using ML capabilities in certain high-stakes decision-making\r\ncontexts. Numerous experts have called attention to the risks of AI in decisions\r\ninvolving nuclear operations in particular.99 In a working paper submitted to the\r\nTenth Review Conference of the Parties to the Treaty on the Non-Proliferation\r\nof Nuclear Weapons, the United States, the United Kingdom, and France stated\r\nthat “Consistent with long-standing policy, we will maintain human control and\r\ninvolvement for all actions critical to informing and executing sovereign\r\ndecisions concerning nuclear weapons employment.”\r\n100 This echoed previous\r\nformal commitments and statements by senior U.S. officials saying in no\r\nuncertain terms that AI will not be used to automate nuclear command and\r\ncontrol.101 While these assurances are welcome, proscribing AI in nuclear\r\ncommand and control only mitigates the most direct threat. Applications\r\ninfluencing decisions indirectly, such as assessments of early warning of a\r\nnuclear strike, raise escalation concerns that question whether AI should be\r\nused at all.\r\n● Limit reliance on ML assessments based on types of inferences. While the\r\nbroader trend in ML development is toward ever larger and more complex\r\nmodels, applying ML for decision-making will be better served by the opposite\r\ninstinct: decompose problems, apply AI toward narrow questions (“puzzles”)\r\nwhere it is well suited while reserving for human judgment problems such as an\r\ninterpreting an adversary’s intent (“mysteries”), where it is unlikely to prove\r\nreliable. This approach requires a careful risk-benefit analysis based on an\r\nunderstanding of the strengths and limitations of ML.\r\n● Build in redundancy by employing multiple models relying on different data\r\nsources. Diverse types of models and data sources limit the potential impacts of\r\nchanges in data or adversarial compromise of any single system. Additionally,\r\nhaving back-up models enables fall back options in case of a compromise or\r\nfailure.102 These steps would limit the perceived and actual harm from the\r\nCenter for Security and Emerging Technology | 24\r\ndiscovery of a compromise in a deployed system or a sabotaged training\r\nprocess.\r\n● Restrict the affordances of ML systems. Affordances refer to the range of\r\npossible actions or outputs available to a system. For instance, an ML capability\r\nin an autonomous system with access to weapons and navigation systems has\r\ngreater affordances than an ML capability only capable of accessing navigation\r\nsystems. Limiting the affordances of the ML capability might entail segmenting\r\noff the weapons systems. By analogy, limiting the affordances of systems\r\ninvolved in decision support might include restricting the possible courses of\r\naction a system could recommend to those that do not involve nuclear-capable\r\nsystems (where deployment requires consideration of an additional level of\r\ncomplicated contextual factors). Restricting the affordances of ML systems may\r\nbe the only surefire way to prevent certain types of mistakes.\r\nThird, prepare senior decision-makers to be informed users of AI systems. The DOD\r\nalready strives to achieve appropriate levels of trust through training and education of\r\noperators.103 These practices should extend to senior decision-makers relying upon ML\r\nsystems.\r\n● Involve decision makers as early as possible in the development and TEVV of\r\nthe systems upon which they will rely. A lack of communication and\r\nengagement between the engineers, developers, data scientists, and end users\r\ncan lead to problems at the design stage that persist through testing and\r\nevaluation processes.104 Moreover, as Flournoy et al. assert, ML capabilities are\r\nembedded within larger systems that include human factors that will affect their\r\nperformance, and therefore cannot be properly evaluated separate from those\r\nlarger systems.105 The only way to fully understand how a system informing\r\ndecision makers will perform is to test it under realistic conditions, including\r\nwith the actual humans who will ultimately rely upon it.\r\n● Train senior decision-makers to understand how to interpret and judge the\r\nreliability of ML outputs and avoid pitfalls such as automation bias. Senior\r\ndecision-makers should practice making deliberations involving ML systems\r\nunder realistic conditions, including scenarios in which the systems fail.\r\nCenter for Security and Emerging Technology | 25\r\nOf course, both sides must address escalation risks to ensure stability in a crisis. In\r\naddition to general calls for crisis management mechanisms, experts in both the United\r\nStates and China have proposed dialogues\r\non the risks of AI to crisis stability.106 Yet\r\nprospects for crisis management or\r\nconfidence-building measures between the\r\ntwo appear remote. The PLA in particular is\r\nreluctant to engage in any dialogue on\r\nAI.107 Still, the United States can take\r\nmodest steps to provide certain assurances\r\nregarding how it will adopt AI and\r\nencourage China (and other states) to take\r\nsimilar precautions.\r\nFirst, clarify the practices and safeguards\r\nto limit the risks of AI in decision-making. The DOD already leads globally in\r\nadopting and making transparent the principles guiding its approach to AI.108 The\r\nUnited States should explore how to provide further assurances with respect to\r\nparticularly risky applications of ML:\r\n● Provide details on TEVV practices and standards to the degree possible without\r\nconveying information on potential vulnerabilities or weaknesses.109\r\n● Identify areas in which decision makers will not rely on ML capabilities, for\r\nexample, if they are deemed unreliable for certain judgements about an\r\nadversary’s deterrence thresholds or similar types of inferences. This might at\r\nleast signal to China that these risks are taken seriously.\r\nSecond, collaborate internationally to develop mutually-beneficial technical\r\nsafeguards. Imbrie and Kania, among others, propose international cooperation on AI\r\nsafety analogous to the United States’ offer during the Cold War to share with other\r\ncountries Permissive Action Links designed to prevent unauthorized launch of nuclear\r\nweapons.110 Technical cooperation faces numerous hurdles, including overcoming\r\nmutual suspicions and preventing the transfer of capabilities that would improve an\r\nadversary’s AI systems. Informal academic or technical-level exchanges are most\r\nplausible. Two candidate areas in which to explore mutually-beneficial collaboration\r\ninclude:\r\n● Interpretability and explainability techniques to ensure that decision makers are\r\nable to interrogate the outputs of systems.111\r\nStill, the United States can\r\ntake modest steps to\r\nprovide certain assurances\r\nregarding how it will adopt\r\nAI and encourage China (and\r\nother states) to take similar\r\nprecautions.\r\nCenter for Security and Emerging Technology | 26\r\n● Best practices to reduce AI accidents and certain types of failures like reward\r\nhacking, which could trigger accidental escalation.112\r\nThird, commit to restraint in offensive operations carrying significant escalation\r\nrisks. It is in the United States’ interest to exercise restraint in the conduct of actions\r\ncarrying significant escalation risks and to encourage such restraint by others. This is\r\ntrue even if it is unlikely that China or others would commit to such norms for the\r\nforeseeable future. Two areas stand out as acutely concerning from an escalation\r\nperspective:\r\n● Offensive cyber operations: Noting the risks of malware spreading\r\nuncontrollably and causing unintended harm, Adams et al. propose that states\r\nconstrain automation in cyber operations, including via built-in kill switches or\r\nincorporating “conditional execution logic” that would prevent an operation from\r\nimpacting certain targets and restrict effects.113 The use of ML in offensive cyber\r\noperations may compound these automation risks. In such a highly complex and\r\ninterdependent operational environment, inadequate training or poorly-specified\r\nobjectives might result in a capability that causes unintended, cascading effects.\r\n● Adversarial AI: Operations directly targeting AI/ML systems used in an\r\nadversary’s decision-making and C3 may simply be deemed too risky to even\r\nattempt, because of the unpredictable ways this could impact decisions.\r\nSimilarly, operations prior to a conflict aiming to poison or sabotage ML\r\ndevelopment could have unforeseen, cascading effects.114\r\nCenter for Security and Emerging Technology | 27\r\nConclusion\r\nAs they strive for decision advantage, militaries must beware the pitfalls of AI. ML has\r\nboth the potential to dramatically improve the speed and effectiveness of decisionmaking and to introduce new uncertainties that could lead to catastrophic\r\nmiscalculation. The United States leads in establishing policies and processes that will\r\nposition it to manage these risks.115 In China, there are at least signs that technical\r\nexperts are cognizant of the flaws and limitations of AI systems and their potentially\r\ndestabilizing impacts.\r\nThe test will be the resilience of processes and practices against the pressures to fasttrack development and deployment to avoid falling behind. A crisis or conflict could\r\nrapidly change the risk-benefit calculus for deploying certain applications or pressure\r\nmilitaries to shortcut the development and testing of systems. Moreover, in the\r\nabsence of clear firebreaks between tactical and strategic-level decision-making, there\r\nexists the possibility of a steady creep of AI and automation into influential roles\r\nthrough the incremental shift of more and more parts of analytical and deliberative\r\nprocesses to machines. This makes it all the more necessary to explore proactive steps\r\nto limit the risks of AI, even as these capabilities continue to mature and evol", "metadata": {"original_filename": "item099_US_Reducing the Risks of Artificial Intelligence for Military Decision Advantage.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:51:54.538074", "updated_at": "2025-08-28T21:51:54.538074", "word_count": 58593}