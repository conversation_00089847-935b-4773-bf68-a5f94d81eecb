import logging
from typing import Dict, Any, Optional, List
import json
from datetime import datetime
from pathlib import Path
import os
import asyncio

from src.models.schemas import TaskType, AnalysisResult
from src.services.zhipu_client import ZhipuAIClient, get_zhipu_client
from src.core.config import settings

logger = logging.getLogger(__name__)


class AnalysisService:
    """分析服务，负责执行文档分析任务"""

    def __init__(self, zhipu_client: ZhipuAIClient):
        """初始化分析服务
        
        Args:
            zhipu_client: 智谱AI客户端
        """
        self.zhipu_client = zhipu_client
        # 创建结果存储目录
        self.results_dir = Path(settings.BASE_DIR) / "data" / "results"
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载提示词模板
        self.prompts = self._load_prompts()
        logger.info("分析服务初始化完成")

    def _load_prompts(self) -> Dict[TaskType, str]:
        """加载任务提示词模板
        
        Returns:
            Dict[TaskType, str]: 任务类型到提示词模板的映射
        """
        prompts = {}
        prompts_dir = settings.PROMPTS_DIR
        
        # 任务与提示词文件的映射关系
        prompt_files = {
            TaskType.ACTOR_RELATION: "actor_relation_prompt.txt",
            TaskType.ROLE_FRAMING: "role_framing_prompt.txt",
            TaskType.PROBLEM_SCOPE: "problem_scope_prompt.txt",
            TaskType.CAUSAL_MECHANISM: "causal_mechanism_prompt.txt"
        }
        
        # 加载提示词文件
        for task_type, file_name in prompt_files.items():
            file_path = prompts_dir / file_name
            try:
                if file_path.exists():
                    with open(file_path, "r", encoding="utf-8") as f:
                        prompts[task_type] = f.read().strip()
                    logger.debug(f"加载提示词模板成功: {task_type}")
                else:
                    # 如果文件不存在，使用默认提示词
                    prompts[task_type] = self._get_default_prompt(task_type)
                    logger.warning(f"提示词文件不存在，使用默认提示词: {task_type}")
            except Exception as e:
                logger.error(f"加载提示词模板失败: {str(e)}")
                # 使用默认提示词
                prompts[task_type] = self._get_default_prompt(task_type)
        
        return prompts

    def get_prompt_template(self, task_type: TaskType) -> str:
        """获取指定任务类型的提示词模板
        
        Args:
            task_type: 任务类型
            
        Returns:
            str: 提示词模板
        """
        prompt_template = self.prompts.get(task_type)
        if not prompt_template:
            logger.error(f"未找到任务 {task_type} 的提示词模板")
            return self._get_default_prompt(task_type)
        return prompt_template

    def _get_default_prompt(self, task_type: TaskType) -> str:
        """获取默认提示词
        
        Args:
            task_type: 任务类型
            
        Returns:
            str: 默认提示词模板
        """
        default_prompts = {
            TaskType.ACTOR_RELATION: """
请分析以下政策文档中的行为者与关系。

文档内容：
{document}

请执行以下分析任务：
1. 识别文档中的主要行为者（人物、组织、机构等）
2. 分析行为者之间的关系和互动
3. 提取行为者的主要行动和立场
4. 总结关键发现

请以严格的JSON格式返回分析结果，结构如下（不要包含注释）：
{
    "actors": [
        {
            "name": "行为者名称",
            "type": "行为者类型（如：政府、企业、个人等）",
            "description": "行为者描述",
            "actions": ["行为者的主要行动1", "行为者的主要行动2"],
            "stance": "行为者的立场或态度"
        }
    ],
    "relations": [
        {
            "source": "行为者1名称",
            "target": "行为者2名称",
            "type": "关系类型（如：合作、对抗、支持等）",
            "description": "关系描述"
        }
    ],
    "key_findings": [
        "关键发现1",
        "关键发现2"
    ]
}

重要：只返回纯JSON格式的分析结果，不要包含任何额外的解释、注释或说明文字。
            """,
            
            TaskType.ROLE_FRAMING: """
请分析以下政策文档中的角色塑造框架。

文档内容：
{document}

请执行以下分析任务：
1. 识别文档中的英雄角色塑造（谁被塑造为解决问题的英雄）
2. 识别文档中的受害者角色塑造（谁被塑造为问题的受害者）
3. 识别文档中的反派角色塑造（谁被塑造为问题的制造者）
4. 分析叙事策略和框架手法
5. 总结关键发现

请以严格的JSON格式返回分析结果，结构如下（不要包含注释）：
{
    "heroes": [
        {
            "name": "英雄名称",
            "description": "如何被塑造为英雄",
            "evidence": ["支持证据1", "支持证据2"]
        }
    ],
    "victims": [
        {
            "name": "受害者名称",
            "description": "如何被塑造为受害者",
            "evidence": ["支持证据1", "支持证据2"]
        }
    ],
    "villains": [
        {
            "name": "反派名称",
            "description": "如何被塑造为反派",
            "evidence": ["支持证据1", "支持证据2"]
        }
    ],
    "narratives": [
        {
            "type": "叙事策略类型",
            "description": "策略描述",
            "examples": ["例子1", "例子2"]
        }
    ],
    "key_findings": [
        "关键发现1",
        "关键发现2"
    ]
}

重要：只返回纯JSON格式的分析结果，不要包含任何额外的解释、注释或说明文字。
            """,
            
            TaskType.PROBLEM_SCOPE: """
请分析以下政策文档中的问题范围策略。

文档内容：
{document}

请执行以下分析任务：
1. 识别文档中的问题扩大化策略（如何将问题描述得比实际更大）
2. 识别文档中的问题缩小化策略（如何将问题描述得比实际更小）
3. 分析问题框架模式
4. 总结关键发现

请以严格的JSON格式返回分析结果，结构如下（不要包含注释）：
{
    "expansion_strategies": [
        {
            "type": "扩大化策略类型",
            "description": "策略描述",
            "examples": ["例子1", "例子2"]
        }
    ],
    "reduction_strategies": [
        {
            "type": "缩小化策略类型",
            "description": "策略描述",
            "examples": ["例子1", "例子2"]
        }
    ],
    "framing_patterns": [
        {
            "type": "框架模式类型",
            "description": "模式描述",
            "examples": ["例子1", "例子2"]
        }
    ],
    "key_findings": [
        "关键发现1",
        "关键发现2"
    ]
}

重要：只返回纯JSON格式的分析结果，不要包含任何额外的解释、注释或说明文字。
            """,
            
            TaskType.CAUSAL_MECHANISM: """
请分析以下政策文档中的因果机制描述。

文档内容：
{document}

请执行以下分析任务：
1. 识别文档中描述的因果链（事件A导致事件B导致事件C...）
2. 分析归因模式（问题被归因于什么因素）
3. 分析责任框架（谁被认为应对问题负责）
4. 总结关键发现

请以严格的JSON格式返回分析结果，结构如下（不要包含注释）：
{
    "causal_chains": [
        {
            "sequence": ["原因1", "结果1/原因2", "结果2"],
            "description": "因果链描述"
        }
    ],
    "attribution_patterns": [
        {
            "target": "归因对象",
            "factors": ["归因因素1", "归因因素2"],
            "evidence": ["支持证据1", "支持证据2"]
        }
    ],
    "responsibility_framing": {
        "responsible_actors": ["负责任的行为者1", "负责任的行为者2"],
        "absolved_actors": ["被免责的行为者1", "被免责的行为者2"],
        "framing_strategy": "责任框架策略描述"
    },
    "key_findings": [
        "关键发现1",
        "关键发现2"
    ]
}

重要：只返回纯JSON格式的分析结果，不要包含任何额外的解释、注释或说明文字。
            """
        }
        
        return default_prompts.get(task_type, "请分析以下文档：\n{document}")

    async def analyze(self, text: str, task_type: TaskType) -> AnalysisResult:
        """执行文档分析任务
        
        Args:
            text: 文档文本内容
            task_type: 分析任务类型
            
        Returns:
            AnalysisResult: 分析结果
        """
        try:
            logger.info(f"开始执行分析任务: {task_type}")
            
            # 获取任务提示词模板
            prompt_template = self.prompts.get(task_type)
            if not prompt_template:
                logger.error(f"未找到任务 {task_type} 的提示词模板")
                return AnalysisResult(
                    task_type=task_type,
                    result={"error": "未找到任务提示词模板"},
                    success=False,
                    error="未找到任务提示词模板"
                )
            
            # 调用智谱AI进行分析
            response = await self.zhipu_client.analyze_document(
                text=text,
                task_type=task_type.value,
                prompt_template=prompt_template
            )
            
            if not response.get("success", False):
                logger.error(f"分析任务 {task_type} 失败: {response.get('error', '未知错误')}")
                return AnalysisResult(
                    task_type=task_type,
                    result={"error": response.get("error", "分析失败")},
                    success=False,
                    error=response.get("error", "分析失败")
                )
            
            logger.info(f"分析任务 {task_type} 完成")
            return AnalysisResult(
                task_type=task_type,
                result=response["result"],
                success=True
            )
        except Exception as e:
            logger.error(f"执行分析任务 {task_type} 异常: {str(e)}")
            return AnalysisResult(
                task_type=task_type,
                result={"error": str(e)},
                success=False,
                error=str(e)
            )

    async def get_results(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """获取文档分析结果
        
        Args:
            doc_id: 文档ID
            
        Returns:
            Dict[str, Any]: 分析结果，如不存在则返回None
        """
        try:
            file_path = self.results_dir / f"{doc_id}.json"
            if not file_path.exists():
                logger.warning(f"文档 {doc_id} 的分析结果不存在")
                return None
            
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            
            # 返回results字段，这是可视化服务期望的格式
            results = data.get("results", {})
            logger.info(f"获取文档 {doc_id} 分析结果成功")
            return results
        except Exception as e:
            logger.error(f"获取分析结果失败: {str(e)}")
            raise

    async def save_results(self, doc_id: str, results: Dict[TaskType, AnalysisResult]) -> bool:
        """保存文档分析结果
        
        Args:
            doc_id: 文档ID
            results: 分析结果
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 构建结果数据
            result_data = {
                "doc_id": doc_id,
                "results": {task.value: result.dict() for task, result in results.items()},
                "created_at": datetime.now().isoformat()
            }
            
            # 保存结果
            file_path = self.results_dir / f"{doc_id}.json"
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"保存文档 {doc_id} 分析结果成功")
            return True
        except Exception as e:
            logger.error(f"保存分析结果失败: {str(e)}")
            return False


# 创建服务实例
async def get_analysis_service() -> AnalysisService:
    """获取分析服务实例"""
    zhipu_client = await get_zhipu_client()
    return AnalysisService(zhipu_client)
