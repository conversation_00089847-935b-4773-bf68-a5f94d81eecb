{"doc_id": "41d15b7f-1c2b-4d50-a14f-fd8642e826f1", "title": "item200_US_Assembling Accountability Algorithmic Impact Assessment for the Public Interest", "text": "Assembling Accountability: Algorithmic Impact Assessment for the Public Interest\r\n\r\nThe last several years have been a watershed for algorithmic accountability. Algorithmic systems have been used for years, in some cases decades, in all manner of important social arenas: disseminating news, administering social services, determining loan eligibility, assigning prices for on-demand services, informing parole and sentencing decisions, and verifying identities based on biometrics among many others. In recent years, these algorithmic systems have been subjected to increased scrutiny in the name of accountability through adversarial quantitative studies, investigative journalism, and critical qualitative accounts. These efforts have revealed much about the lived experience of being governed by algorithmic systems. Despite many promises that algorithmic systems can remove the old bigotries of biased human judgement,1 there is now ample evidence that algorithmic systems exert power precisely along those familiar vectors, often cementing historical human failures into predictive analytics. Indeed, these systems have disrupted democratic electoral politics,2 fueled violent genocide,3 made vulnerable families even more vulnerable,4 and perpetuated racial- and gender-based discrimination.5 Algorithmic justice advocates, scholars, tech companies, and policymakers alike have proposed algorithmic impact assessments (AIAs)—borrowing from the language of impact assessments from other domains—as a potential process for addressing algorithmic harms that moves beyond narrowly constructed metrics towards real justice.6 Building an impact assessment process for algorithmic systems raises several challenges. For starters, assessing impacts requires assembling a multiplicity of viewpoints and forms of expertise. It involves deciding whether sufficient, reliable, and adequate amounts of evidence have been collected about systems’ consequences on the world, but also about their formal structures—technical specifications, operating parameters, subcomponents, and ownership.7 Finally, even when AIAs (in whatever form they may take) are conducted, their effectiveness in addressing on-theground harms remains uncertain.\r\n\r\nCritics of regulation, and regulators themselves, have often argued that the complexity of algorithmic systems makes it impossible for lawmakers to understand them, let alone craft meaningful regulations for them.8 Impact assessments, however, offer a means to describe, measure, and assign responsibility for impacts without the need to encode explicit, scientific understandings in law.9 We contend that the widespread interest in AIAs comes from how they integrate measurement and responsibility—an impact assessment bundles together an account of what this system does and who should remedy its problems. Given the diversity of stakeholders involved, impact assessments mean many different things to different actors—they may be about compliance, justice, performance, obfuscation through bureaucracy, creation of administrative leverage and influence, documentation, and much more. Proponents of AIAs hope to create a point of leverage for people and communities to demand transparency and exert influence over algorithmic systems and how they affect our lives. In this report we show that the choices made about an impact assessment process determine how, and whether, these goals are achieved. Impact assessment regimes principally address three questions: what a system does; who can do something about what that system does; and who ought to make decisions about what the system is permitted to do. Attending to how AIA processes are assembled is imperative because they may be the means through which a broad cross-section of society can exert influence over how algorithmic systems affect everyday life. Currently, the contours of algorithmic accountability are underspecified. A robust role for individuals, communities, and regulatory agencies outside of private companies is not guaranteed. There are strong economic incentives to keep accountability practices fully internal to private corporations. In tracing how IA processes in other domains have evolved over time, we have found that the degree and form of accountability emerging from the construction of an impact assessment regime varies widely and is a result of decisions made during their development. In this report, we illustrate the decision points that will be critical in the development of AIAs, with a particular focus on protecting and empowering individuals and communities who are systemically vulnerable to algorithmic harms. One of the central challenges to designing AIAs is what we call the specification dilemma: Algorithmic systems can cause harm when they fail to work as specified—i.e., in error—but may just as well cause real harms when working exactly as specified. A good example for this dilemma is facial recognition technologies. Harms caused by inaccuracy and/ or disparate accuracy rates of such technologies are well documented. Disparate accuracy across demographic groups is a form of error, and produces harms such as wrongful arrest,10 inability to enter one’s own apartment building,11 and exclusion from platforms on which one earns income.12 In particular, false arrests facilitated by facial recognition have been publicly documented several times in the past year.13 On such occasions, the harm is not merely the error of an inaccurate match, but an ever-widening circle of consequences to the target and their family: wrongful arrest, time lost to interrogation, incarceration and arraignment, and serious reputational harm. Harms, however, can also arise when such technologies are working as designed.14 Facial recognition, for example, can produce harms by chilling rights such as freedom of assembly, free association, and protections against unreasonable searches.15 Furthermore, facial recognition technologies are often deployed to target minority communities that have already been subjected to long histories of surveillance.16 The expansive range of potential applications for facial recognition presents a similar range of its potential harms, some of which fit neatly into already existing taxonomies of algorithmic harm,17 but many more of which are tied to their contexts of design and use. Such harms are simply not visible to the narrow algorithmic performance metrics derived from technical audits. Another process is needed to document algorithmic harms, allowing: (a) developers to redesign their products to mitigate known harms; (b) vendors to purchase products that are less harmful; and (c) regulatory agencies to meaningfully evaluate the tradeoff between benefits and harms of appropriating such products. Most importantly, the public—particularly vulnerable individuals and communities—can be made aware of the possible consequences of such systems. Still, anticipating algorithmic harms can be an unwieldy task for any of these stakeholders—developers, vendors, and regulatory authorities—individually. Understanding algorithmic harms requires a broader community of experts: community advocates, labor organizers, critical scholars, public interest technologists, policy makers, and the third-party auditors who have been slowly developing the tools for anticipating algorithmic harms. This report provides a framework for how such a diversity of expertise can be brought together. By analyzing existing impact assessments in domains ranging from the environment to human rights to privacy, this report maps the challenges facing AIAs.\r\n\r\nFor an AIA process to really achieve accountability, a number of questions about how to structure these assessments will need to be answered. Many of these questions can be addressed by carefully considering how to tailor each of the 10 constitutive components of an impact assessment process specifically for AIAs. Like at any restaurant, a menu of options exists for each course—but it may sometimes be necessary to order “off menu.” Constructing an AIA process also needs to satisfy the multiple, overlapping, and disparate needs of everyone involved with algorithmic systems.135 A robust AIA process will also need to lay out the scope of harms that are subject to algorithmic impact assessment. Quantifiable algorithmic harms like disparate impacts to protected classes of individuals are well studied, but there are a range of other algorithmic harms which require consideration in how impacts get assessed. These algorithmic harms include (but are not limited to) representational harms, allocational harms, and harms to dignity.136 For an AIA process to encompass the appropriate scope of potential harms, it will need to first consider: (1) how to integrate the interests and agency of affected individuals and communities into measurement practices; and (2) the mechanisms through which community input will be balanced against the power and autonomy of private developers of algorithmic systems, and (3) the constellation of other governance and accountability mechanisms at play within a given domain. A robust AIA process will also need to acknowledge that not all algorithmic systems may require an AIA—all computation is built on “algorithms” in a strictly technical sense, but there is a vast difference between something like a bubble-sort algorithm that is used in prosaic computational processes like alphabetizing lists, and algorithmic systems that are used to shape social, economic, and political life, for example, to decide who gets a job and who does not. Many algorithmic systems will not clearly fall into neat categories that either definitely require or are definitely exempt from an AIA. Furthermore, technical methods alone will not illuminate which category a system belongs in. Algorithmic impact assessment will require an accountable process for determining what catalyzes an AIA, based on the context and the content of an algorithmic system and its specified purpose. These characteristics may include the domain in which it operates, as above, but might also include the actor operating the system, the funding entity, the function the system serves, the type of training data involved, and so on. The proper role of government regulators in outlining requirements for when an AIA is necessary, what it consists of in particular contexts, and how it is to be evaluated, also remain to be determined. Given the differences in impact assessment processes laid out above, and the variability of algorithmic systems and their myriad effects on the world, it is worthwhile to step back and observe how impact assessments, in general, act in thworld. Namely, impact assessments structure power, sometimes in ways that reinforce structural inequalities and unjust hierarchies. They produce and distribute risk, they are exercises of power, and they provide a means to contest power and distribution of risk. In analyzing impact assessments as accountability mechanisms, it is crucial to see impact assessments themselves as sets of power-laden practices that instantiate and structure power at the same time as they provide a means for contesting existing power relationships. For AIAs, the ways in which various components are selected and various forms of expertise are assembled, are directly implicated in the distribution of power. Therefore, these components must be selected with an awareness of how impact assessment can at times fall short of equitably distributing power, replicating already existing hierarchies, and produce the appearance of accountability without tangibly reducing harms. With these observations in mind, we can begin to ask practical questions about  how to construct an algorithmic impact  assessment process. One of the first questions that needs to be addressed is: who should be considered as stakeholders for the purposes of an AIA? These stakeholders could include system developers (private technology companies, civic tech organizations, and government agencies that build such systems themselves), system operators (businesses and government agencies that purchase or license systems from third-party vendors), independent critical scholars who have developed a wide range of disciplinary forms of expertise to investigate the social and environmental implications of algorithmic systems, independent auditors who can conduct thorough technical investigations into the design and behavior of algorithmic systems, community advocacy organizations that are closely connected to the individuals and communities most vulnerable to potential harms, and government agencies tasked with oversight, permitting,  and/or regulation. Another question that needs to be asked is: What should the relationship between stakeholders be? Multi-stakeholder actions can be coordinated through a number of means, from implicit norms to explicit legislation, and an AIA process will have to determine whether government agencies ought to be able to mandate changes in an algorithmic system developed or operated by a private company, or if third-party certification of acceptable impacts are sufficient. It will also have to determine the appropriate role of public participation and the degree of access offered to community advocates and other interested individuals. AIAs will also have to identify the role independent auditors and investigators might be required to play, and how they would  be compensated. In designing relationships between stakeholders, questions of power arise: Who is empowered through an AIA and who is not? Relatedly, how do disparate forms of expertise get represented in an AIA process? For example, if one stakeholder is elevated to the role of accountability forum, it is given significant power over other actors. Similarly, the ways different forms of expertise are brought into relation to each other also shapes who wields power in an AIA process. The expertise of an advocacy organization in documenting the extent of algorithmic harms is different than that of a system developer in determining, for example, the likely false positive rates of their system. Carefully selecting the components of an AIA will influence whether such forms of expertise interact adversarially or learn from each other. \r\nThese questions form the theoretical basis for addressing more practical legal, policy, and technical concerns, particularly around: 1. The role of private industry—those who develop AI systems for their own products and those who act as vendors to government and other private enterprises—in providing technical descriptions of the systems they build and documenting their potential, or actual, impacts. 2. The role of independent experts on  algorithmic audit and community studies  of AI systems, external auditors commissioned by AI system developers, and internal technical audits conducted by AI system developers in delineating the likely impacts  of such systems. 3. The appropriate relationship between regulatory agencies, community advocates, and private industry in negotiating the scope of impacts to be assessed, the acceptable thresholds for those impacts, and the means by which those impacts are to be minimized or mitigated. 4. Whether private sector and public sector uses of algorithmic systems should be regulated by the same AIA mechanism. 5. How to specify the scope of AIAs to reasonably delineate what types of algorithmic systems, using which types of data, operating at what scale, and affecting which people or activities should be subject to audit and assessment, and which institutions—private organizations, government agencies, or other entities—should have authority to mandate, evaluate, and/or enforce them. Governing algorithmic systems through AIAs will require answering these questions in ways that reflect the current configurations of resources in the development, procurement, and operation of such systems while also experimenting with ways to shift political power and agency over these systems to affected communities. These current configurations need not, and should not, be taken as fixed in stone but merely as the starting point from which the impacts to those most affected by algorithmic systems, and most vulnerable to harms, can be incorporated into structures of accountability. This will require a far better understanding of the value of algorithmic systems for people who live with them, and their evaluations of and responses to the types of algorithmic risks and harms they might experience. It will also require deep knowledge of the legal framings and governance structures that could plausibly regulate such systems, and their integration with the technical and organizational affordances of firms developing algorithmic systems. Finally, this report points to a need to develop robust frameworks in which consensus can be developed from among the range of stakeholders necessary to assemble an algorithmic impact assessment process. Such multi-stakeholder collaborations are necessary to adequately assemble, evaluate, and document algorithmic impacts and are shaped by evolving sociocultural norms and organizational practices. Developing consensus will also require constructing new tools for evaluating impacts, and understanding and resolving the relationship between actual or potential harms and the way such harms are measured as impacts. The robustness of impacts as proxies of harms can only be maintained by bringing together the multiple disciplinary and experiential forms of expertise in engaging with algorithmic systems. After all, impact assessments are a means to organize whose voices count in governing algorithmic systems.", "metadata": {"original_filename": "item200_US_Assembling Accountability Algorithmic Impact Assessment for the Public Interest.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T22:21:34.994810", "updated_at": "2025-08-28T22:21:34.994810", "word_count": 17709}