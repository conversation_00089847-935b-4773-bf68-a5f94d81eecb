"""
🏗️ 基础设施模块
提供API网关、服务发现、监控等基础设施服务
"""

# 导入核心基础设施组件
try:
    from .api_gateway.model_orchestrator import model_orchestrator, initialize_orchestrator
    MODEL_ORCHESTRATOR_AVAILABLE = True
except ImportError:
    MODEL_ORCHESTRATOR_AVAILABLE = False

# 基础设施组件状态
INFRASTRUCTURE_STATUS = {
    "api_gateway": MODEL_ORCHESTRATOR_AVAILABLE,
    "service_discovery": False,  # 开发中
    "monitoring": False,  # 开发中
    "load_balancer": False,  # 开发中
}

def get_infrastructure_status():
    """获取基础设施状态"""
    return {
        "components": INFRASTRUCTURE_STATUS,
        "total_components": len(INFRASTRUCTURE_STATUS),
        "available_components": sum(INFRASTRUCTURE_STATUS.values()),
        "version": "1.0.0"
    }

__all__ = [
    "model_orchestrator", 
    "initialize_orchestrator",
    "get_infrastructure_status"
]
