#!/usr/bin/env python3
"""
系统测试脚本
"""
import sys
import asyncio
import requests
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_api_server():
    """测试API服务器是否启动"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ API服务器运行正常")
            return True
        else:
            print(f"❌ API服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到API服务器: {e}")
        return False

def test_document_creation():
    """测试文档创建"""
    try:
        doc_data = {
            "title": "测试文档",
            "text": "这是一个测试文档，用于验证系统功能。政府将推进科技创新，企业应承担社会责任。",
            "metadata": {"test": True}
        }
        
        response = requests.post("http://localhost:8000/api/v1/documents/", json=doc_data, timeout=10)
        if response.status_code == 201:
            result = response.json()
            print(f"✅ 文档创建成功: {result['doc_id']}")
            return result['doc_id']
        else:
            print(f"❌ 文档创建失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ 文档创建异常: {e}")
        return None

def test_document_analysis(doc_id):
    """测试文档分析"""
    try:
        analysis_data = {
            "doc_id": doc_id,
            "tasks": ["actor_relation"]
        }
        
        response = requests.post("http://localhost:8000/api/v1/analysis/", json=analysis_data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            print("✅ 文档分析成功")
            return True
        else:
            print(f"❌ 文档分析失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 文档分析异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始系统测试...")
    
    # 测试API服务器
    if not test_api_server():
        print("请先启动API服务器: python -m uvicorn src.main:app --reload")
        return
    
    # 测试文档创建
    doc_id = test_document_creation()
    if not doc_id:
        return
    
    # 测试文档分析
    test_document_analysis(doc_id)
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    main()
