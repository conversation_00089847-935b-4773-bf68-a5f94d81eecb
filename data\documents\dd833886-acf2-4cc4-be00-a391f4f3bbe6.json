{"doc_id": "dd833886-acf2-4cc4-be00-a391f4f3bbe6", "title": "item111_US_Cybersecurity and Artificial Intelligence Problem Analysis and US Policy Recommendations", "text": "Cybersecurity and Artificial Intelligence: Problem Analysis and US Policy Recommendations\r\nPolicy Recommendations \r\nIn light of the significant challenges analyzed in the previous section, considerable attention from policymakers is necessary to ensure the safety and security of the American people. The following policy recommendations represent critical, targeted first steps to mitigating these risks: • Minimum Cybersecurity Requirements for Advanced AI Developers: Only a handful of AI developers, primarily based in the United States, are presently developing the world’s 4FUTURE OF LIFE INSTITUTE most advanced AI systems, with significant implications for American economic stability and national security. In order to safeguard these AI systems from malicious state and non-state actors, minimum cybersecurity requirements should be adopted for  those developing and maintaining them, as is the case with high-risk biosafety labs (BSLs) and national nuclear laboratories (NNLs). These standards should include minimum criteria for cybersecurity personnel numbers, red-team tests, and external evaluations. • Explicitly Focus on AI-Enabled Cyberattacks in National Cyber-Strategies: Artificial intelligence goes completely unmentioned in the National Cybersecurity Strategy  Implementation Plan published by the White House in July 2023, despite recognition of cyber risks of AI in the National Cybersecurity Strategy itself.3 AI risks need to be integrated explicitly into a broader cybersecurity posture, including in the DOD Cyber Strategy, the National Cyber Incident Response Plan (NCIRP), the National Cybersecurity Investigative Joint Task Force (NCIJTF) and other relevant plans. • Establish Minimum Standards for Integration of AI into Cybersecurity Systems and Critical Infrastructure: Integrating unpredictable and vulnerable AI systems into critical cybersecurity systems may create cyber-vulnerabilities of its own. Minimum standards regarding transparency, predictability and robustness of these systems should be set up before they are used for cybersecurity functions in critical industries. Additionally, building on guidance issued in accordance with EO 13636 on Improving Critical Infrastructure Cybersecurity4, EO 13800 on Strengthening the Cybersecurity of Federal Networks and Critical Infrastructure5, and the Framework for Improving Critical Infrastructure Cybersecurity published by NIST6, AI-conscious standards for cybersecurity in critical infrastructure should be developed and enforced. Such binding standards should account in particular for risks from AI-enabled cyber-attacks, and should be developed in coordination with CISA, SRMA and SLTT offices. More general oversight and governance infrastructure for advanced AI systems is also essential to protect against cyber-risks from AI, among many other risks. We further recommend these broader regulatory approaches to track, evaluate, and incentivize the responsible design of advanced AI systems: • Require Advanced AI Developers to Register Large Training Runs and to “Know Their Customers”: The Federal Government lacks a mechanism for tracking the development and proliferation of advanced AI systems that could exacerbate cyber-risk. In order to adequately mitigate cybersecurity risks, it is essential to know what systems are being developed and who has access to them. Requiring registration for the acquisition of large amounts of computational resources for training advanced AI systems, and fo", "metadata": {"original_filename": "item111_US_Cybersecurity and Artificial Intelligence Problem Analysis and US Policy Recommendations.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T22:21:33.320492", "updated_at": "2025-08-28T22:21:33.320492", "word_count": 3498}