{"doc_id": "doc_1756359669907", "title": "item096_US_Enabling Principles for AI Governance", "text": "Introduction\nThe question of how to govern artificial intelligence (AI) is rightfully top of mind for\nU.S. lawmakers and policymakers alike. Strides in the development of high-powered\nlarge language models (LLMs) like ChatGPT/GPT-4o, <PERSON>, <PERSON>, and Microsoft\nCopilot have demonstrated the potentially transformative impact that AI could have on\nsociety, replete with opportunities and risks. At the same time, international partners in\nEurope and competitors like China are taking their own steps toward AI governance.1\nIn\nthe United States and abroad, public analyses and speculation about AI’s potential\nimpact generally lie along a spectrum ranging from utopian at one end—AI as\nenormously beneficial for society—to dystopian on the other—an existential risk that\ncould lead to the end of humanity—and many nuanced positions in between.\nLLMs grabbed public attention in 2023 and sparked concern about AI risks, but other\nmodels and applications, such as prediction models, natural language processing\n(NLP) tools, and autonomous navigation systems, could also lead to myriad harms and\nbenefits today. Challenges include discriminatory model outputs based on bad or\nskewed input data, risks from AI-enabled military weapon systems, as well as\naccidents with AI-enabled autonomous systems.\nGiven AI’s multifaceted potential, in the United States, a flexible approach to AI\ngovernance offers the most likely path to success. The different development\ntrajectories, risks, and harms from various AI systems make the prospect of a one-sizefits-all regulatory approach implausible, if not impossible. Regulators should begin to\nbuild strength through the heavy lifting of addressing today’s challenges. Even if early\nregulatory efforts need to be revised regularly, the cycle of repetition and feedback will\nlead to improved muscle memory, crucial to governing more advanced future systems\nwhose risks are not yet well understood.\nPresident Biden’s October 2023 Executive Order on the Safe, Secure, and Trustworthy\nDevelopment and Use of Artificial Intelligence, as well as proposed bipartisan AI\nregulatory frameworks, have provided useful starting points for establishing a\ncomprehensive approach to AI governance in the United States.2 These stand atop\nexisting statements and policies by federal agencies like the U.S. Department of\nJustice, the Federal Trade Commission, as well as the U.S. Equal Employment\nOpportunity Commission, among others.3\nIn order for future AI governance efforts to prove most effective, we offer three\nprinciples for U.S. policymakers to follow. We have drawn these thematic principles\nCenter for Security and Emerging Technology | 2\nfrom across CSET’s wide body of original, in-depth research, as well as granular\nfindings and specific recommendations on different aspects of AI, which we cite\nthroughout this report. They are:\n1. Know the terrain of AI risk and harm: Use incident tracking and horizonscanning across industry, academia, and the government to understand the\nextent of AI risks and harms; gather supporting data to inform governance\nefforts and manage risk.\n2. Prepare humans to capitalize on AI: Develop AI literacy among policymakers\nand the public to be aware of AI opportunities, risks, and harms while employing\nAI applications effectively, responsibly, and lawfully.\n3. Preserve adaptability and agility: Develop policies that can be updated and\nadapted as AI evolves, avoiding onerous regulations or regulations that become\nobsolete with technological progress; ensure that legislation does not allow\nincumbent AI firms to crowd out new competitors through regulatory capture.\nThese principles are interlinked and self-reinforcing: continually updating the\nunderstanding of the AI landscape will help lawmakers remain agile and responsive to\nthe latest advancements, and inform evolving risk calculations and consensus.\n1. Know the terrain of AI risk and harm\nAs AI adoption progresses, supporting data will be necessary to better understand the\ntypes, and extent of, various public and societal risks and harms. U.S. regulators should\nprioritize collecting information on AI incidents to inform policymaking and take\nnecessary corrective measures, while preserving the technology’s benefits and not\nstifling innovation. Ideally, an effective, multipronged approach to AI governance\nwould mix incident reporting, evaluation science, and intelligence collection.\nCapture data on AI harms through incident reporting. AI systems should be tested\nrigorously before deployment, including with each update, but they may be prone to\ndrift or failure in environments dissimilar to their testing conditions and can behave in\nways unforeseen by system developers.4Malicious actors can also use AI to cause\nintentional harm, for instance using generative AI to perpetuate fraud by creating\ndeepfake images or videos.\n5\nIn conceptualizing harm on the spectrum of minimal to\nexistential risk, lawmakers can consider harm exposure in four buckets: 1)\ndemonstrated harms; 2) probable harms involving known risks in deployed AI systems;\n3) implied harms, where studies could uncover new weaknesses in deployed systems;\nCenter for Security and Emerging Technology | 3\nand 4) speculative harms, including existential risks.\n6 These four risk-based buckets\nprovide structure to different harms that regulators can use in AI governance.\nIncident collection would entail collecting data from accidents and events where AI\nsystems caused harm, relying on mandatory, voluntary, and citizen reporting of risks\nand harms.\n7 A public incident reporting system would not cover military or intelligence\nAI incidents, and there could be a separate channel for reporting sensitive AI incidents,\nprotected within secure enclaves. Mandatory and voluntary reporting would likely\nneed to be overseen by federal agencies with clear regulatory roles and distance from\nAI developers, such as the Federal Aviation Administration or the Securities and\nExchange Commission.\n8 Citizen reporting could be collected either as part of a\ngovernmental complaint reporting system or for public consumption by\nnongovernmental organizations like the UL Research Institutes, the Organization for\nEconomic Cooperation and Development, or even a news media outlet. Initially,\nincident reporting could prioritize incidents that generate tangible harms and shift\npolitical will, including fatalities, major property damage, or child safety. CSET research\nhas explored the pros and cons of these risk collection approaches.9\nKnowledge garnered through incident reporting would help achieve several goals.\nFirst, it could help improve public awareness around existing real-world AI risks and\nharms. With clearer insights into today’s most pressing AI challenges, regulators and\nlegislators can better shape laws and address liability issues of public interest.\nSecond, as patterns of AI incidents develop across different industries, regulators may\nbe able to prioritize certain AI governance actions based on the prevalence of certain\nharms. For example, regulators might create risk-based requirements for certain AI\nsystems to undergo retesting and recertification if and when iterative improvements\nare made to models, similar to how the U.S. Food and Drug Administration subjects\nhigh-risk medical devices like pacemakers to continuous evaluation.\n10 Incident\ncollection would provide regulators with more granular data to better identify new or\nmore serious harms and to rapidly devise robust responses.11\nThird, developing an incident reporting system is a concrete bureaucratic step that\ncould beget more government action to address AI harms. It would require\ndetermining where a mandatory and voluntary reporting incident collection body\nwould sit within the U.S. government, along with the criteria for different reporting\nrequirements. It would also require an action plan and implementation process to\nstand it up, and the establishment of a decision-making process for budgeting and\nCenter for Security and Emerging Technology | 4\nresource allocation. The process of establishing this body would generate momentum\nand build muscle memory that carries over to work on thornier AI governance\nquestions.\nFinally, incident reporting could help build U.S. leadership in AI governance globally.\nBuilding a strong exemplar of an incident monitoring and reporting center could\nfacilitate collaboration, exchanges, and best-practice sharing with other nations.\nIncubating international cooperation could make the United States more aware and\nbetter prepared to address AI harms that may be more prevalent in other parts of the\nworld, and help build a common foundation with other countries to monitor and spread\nawareness of shared AI risks.\nInvest in evaluation and measurement methods to strengthen our understanding of\ncutting-edge AI systems. The science of measuring the properties of AI systems,\nespecially the capabilities of foundation models that can be adapted for many different\ndownstream tasks, is currently in early development. Investment is needed to advance\nbasic research into how to evaluate AI models and systems, and to develop\nstandardized methods and tool kits that AI developers and regulators can use.\nPolicymakers’ creation of appropriate governance mechanisms for AI depends on their\nability to understand what AI systems can and cannot do, and how these systems rate\non trustworthiness properties such as robustness, fairness, and security. The\nestablishment of the U.S. Artificial Intelligence Safety Institute within the National\nInstitute of Standards and Technology is a promising step in this direction, though it\nmay currently lack sufficient resourcing to accomplish the tasks it has been set under\nthe 2023 AI executive order and other policy guidance.\nBuild a robust horizon scanning capability to monitor new and emerging AI\ndevelopments, both domestically and internationally. Alongside incident collection,\nmaintaining information awareness and avoiding technological surprise (unexpectedly\ndiscovering that competitors have developed advanced capabilities) will allow U.S.\nlegislators and regulators to be adaptive in addressing risks and potential harms.12\nHorizon scanning capabilities would be relevant for a range of agencies and bodies,\nand could take on unique relevant focus areas.\nFor instance, an open-source technical monitoring center would be instrumental for the\nUnited States. It could help the U.S. intelligence community and other federal agencies\nby establishing a core capability to track progress in various AI fields throughout\ncommercial industry, academia, and government. This would not only keep the\ncommunity well-informed but also enhance the integration of open-source knowledge\nCenter for Security and Emerging Technology | 5\nwith classified sources, thereby improving the overall intelligence gathering and\ninterpretation process––particularly focused outside of the United States.13 For\nintelligence community agencies, this monitoring would likely focus on specific\ntechnology that augments military systems; agencies outside the intelligence\ncommunity might focus their horizon scanning on AI applications that could have a\nsignificant (though less clearly defined) impact on the economic competitiveness and\nsocietal well-being of the United States. Scanning the horizon for new and emerging\ncapabilities can help to ensure that regulators are prepared to handle emerging\nchallenges from abroad. This could be valuable amid competition with China or other\nauthoritarian states that develop capabilities with negative implications for democratic\nsocieties, such as AI for mass surveillance or for generating and spreading political\ndisinformation. Robust U.S. horizon-scanning capabilities could improve policymakers’\nresponsiveness to the latest threats across AI fields and applications.14\n2. Prepare humans to capitalize on AI\nAI is ultimately a tool, and like other tools, familiarity with its strengths and limitations\nis critical to its effective use. Without adequately educated and trained human users,\nsociety will struggle to realize AI’s potential safely and securely. This section presents\nseveral points for how regulators and policymakers can prepare the human side of the\nequation for emerging AI policy challenges.\nDevelop AI literacy among policymakers. AI literacy for policymakers is key to\neffectively understanding and governing risks from AI. At a minimum, policymakers\nshould understand different types of AI models at a basic level. They should also grasp\nAI’s present strengths and limitations for certain tasks, recognize AI models’ outputs,\nand acknowledge the technical and societal risks from factors like bias or data issues.\nPolicymakers should be keenly aware of the ways that AI systems can be imperfect\nand prone to unexpected, sometimes strange failures, often with limited transparency\nor explainability. They will need to understand in what contexts using certain AI\nmodels is suitable and how machine inputs may bias human decision-making.\nGrounding in these and other details of AI systems will be important for understanding\nhow new AI differs from current models and for anticipating new regulatory\nchallenges.15 Developing training and curricula for those in policy positions could help\nbuild AI literacy today, while investing in AI education would benefit the policymakers\nof tomorrow and society in general.\n16\nDevelop AI literacy among the public. Building public AI literacy, beginning as early\nas possible and continuing throughout adulthood, can help citizens grasp the\nCenter for Security and Emerging Technology | 6\nopportunities, risks, and harms posed by AI to society. For instance, AI literacy can help\nworkers across fields where intelligent systems are already starting to be applied––\nranging from industrial manufacturing to healthcare and finance––to better understand\nthe limitations of systems that help them perform their jobs. Knowing when to rely on\nthe outputs of AI systems or to exercise skepticism, particularly in decision-making\ncontexts, will be important. Alerting workers in other fields to the possibility of\nupskilling programs and accreditations could create employment opportunities beyond\nthe cutting-edge of AI in competencies like computer and information science. AI\nliteracy will be key to participation in the economy of the future for both workers and\nconsumers. Promoting AI literacy could also help the public use outputs from systems\nlike LLMs appropriately to boost productivity and grasp where risks of plagiarism or\ncopyright infringement might exist. The United States could look to countries that have\nattempted to implement their own public AI literacy programs, such as Finland, for\nbest practices and lessons learned in trying to provide citizens with digital skills.17\nMore broadly, alerting the public to the risks of convincing AI-generated\ndisinformation, including text, images, videos, and other multimedia that could\nmanipulate public opinion, could help citizens remain alert to risks from artificial\ncontent.18 This could be a first line of defense against nefarious attempts by malicious\nactors to use AI to harm democratic processes and societies. AI developers should also\nbe alert to and versed in the risks of harm that integrating their models into different\nproducts could create.\n3. Preserve adaptability and agility\nFinally, given the dynamic nature of AI research, development, deployment, and\nadoption, policymakers must be able to incorporate new knowledge into governance\nefforts. Allowing space to iteratively build and update policies as technology changes\nand incorporating learning into policy formulation could make AI governance more\nflexible and effective.\nConsider where existing processes and authorities can already help govern AI if\ncertain implementation gaps are addressed. AI is likely to require some new types of\nregulations and novel policy solutions, but not all regulations for AI will need to be cut\nfrom whole cloth. Using existing regulations offers the benefits of speed and familiarity\nto lawmakers, as well as the ability to fall back on previously delineated authorities\namong federal agencies (compared to the need to litigate overlapping authorities\nbetween existing agencies and newly created AI governance agencies). Policymakers\nwill need to differentiate between truly novel and comparatively familiar questions\nCenter for Security and Emerging Technology | 7\nthat AI systems may raise. There are harms that existing protections, such as the\nFederal Trade Commission Act and the Civil Rights Act of 1964, might already cover\nwhen it comes to issues like copyright infringement or discrimination. Other AI\napplications mix corporate activity, product development, and commercialization in\nfamiliar ways that are already covered by protections by bodies like the Federal Trade\nCommission or the U.S. Food and Drug Administration.19\nFor effective AI governance, policymakers must identify where gaps exist in legal\nstructures and authorities, as well as areas where implementation infrastructure could\nbe lacking. Where applicable legislation does already exist, it will be important to\nconsider where agencies require new resources for analyzing AI systems and\napplications, such as relevant expertise, sandboxes, and other assessment tools. Given\nAI’s wide-ranging applications and their tendency to get at points of tension in current\npractices and procedures, new guidance and implementing statutes may be necessary\nto ensure that existing laws are effective. In some cases, the regulators that enforce\nthese laws may be able to address some of the challenges posed by AI, but they may\nbe reluctant to do so based on resource constraints, lack of precedent with a new\ntechnology, or the need to overcome procedural hurdles. Examining where procedural\nchanges or additional resources can unlock the potential for existing laws to be applied\nto AI may allow lawmakers to move more quickly in addressing harms with regulation,\nrather than tailoring bespoke solutions to AI problems.\nWhere it is less clear that existing regulatory or legal frameworks apply, regulators\nshould consider how to develop frameworks that are flexible and can be adapted to\nincorporate new information. The National Institute of Science and Technology’s\nArtificial Intelligence Risk Management Framework (AI RMF 1.0) is a compelling\nexample of a policy document designed to be adapted based on new information and\nknowledge.20 The United States can also draw on its mix of state and federal\nregulations to aggregate data and information and explore the suitability of flexible,\nexperimental governance approaches.21\nRemain open to future AI capabilities that may evolve in new, unanticipated, and\nunexpected ways. AI models and applications are diverse, and not all technological\nprogress will be identical. Policymakers should remain open to the possibility that\nfuture AI advancements will not rely on the same factors that enabled recent progress.\nFor example, much of the progress in LLM development was driven by a mix of\nalgorithmic improvements and increases in computing power, achieved at great cost,\nover roughly the past decade.22 Companies may use more compute to fill the increasing\ndemand for LLM-based products and to continue to innovate in the near term, at an\nCenter for Security and Emerging Technology | 8\nincreasingly high cost. That said, it is possible that meaningful future advancement\nmay come not just from research achieved with massive compute, but also from\nalgorithmic innovation or improvements in data processing that require smaller\namounts to advance the state of the art.\n23 Indeed, CSET research suggests that growth\nin the amount of compute used to train large models appears to be slowing.24\nPolicymakers should be aware of new trends—through connection to information\nsources like open-source collection, incident reporting, and horizon scanning—and be\nprepared to effectively regulate to mitigate the risks and capitalize on the opportunities\ninherent in new AI models.\nLawmakers should consider the costs and tradeoffs involved when planning AI\ngovernance approaches. Estimating the labor and resourcing required to implement\nvarious governance regimes is an essential step in selecting a feasible strategy. For\nexample, consider regulatory capture, which occurs when a regulatory agency, created\nto act in the public's interest, instead advances the commercial or special interests of\nthe industry it is charged with regulating, often resulting in policies and decisions that\nfavor the regulated entities rather than the public. Congress should welcome not only\ninput from AI companies as legislators develop regulatory policy, but also their\ncooperation in regulatory enforcement. Industry can help identify the latest trends in AI\ndevelopment, including nascent risks and harms, and it has a large, highly-skilled\nworkforce whose knowledge the government can draw on.25 However, lawmakers\nshould keep in mind that companies are not disinterested parties and have their own\nvisions for how to gain and cement advantageous market positions.26 Regulatory\ncapture presents similar risks in AI as in other industries.27 However, avoiding it is likely\nto require the maintenance of a large, skilled government workforce capable of tasks\nlike assessing risks and harms from AI models, and performing analysis and testing.\nThis is likely to be both difficult to attain and costly. While the government could limit\nsuch costs by adopting governance models that shift responsibility for testing and risk\nmitigation onto firms, allowing major AI firms to entrench regulatory positions could\npermit firms to develop standards that benefit their development models at the\nexpense of others.\nDepending on the scope of effort involved, if lawmakers seek to eliminate certain AI\nrisks, they may be more willing to devote costly resources to develop a high-intensity,\ngovernment-first approach that avoids regulatory capture. If risk minimization is\nsufficient, avoiding regulatory capture may be less of a priority. Keeping these tradeoffs in mind will be key going forward.\nCenter for Security and Emerging Technology | 9\nConclusion\nAI governance shapes how humans develop and use AI in ways that reflect their\nsocietal values. By adhering to the principles outlined in this brief—understanding AI\nincidents, closely monitoring tech advancement, fostering AI literacy, and maintaining\nregulatory flexibility—the United States can lead in responsible AI development. This\napproach will help safeguard important societal values, promote innovation, and\nnavigate the dynamic landscape of AI advancements. These enabling principles offer a\nroadmap for crafting agile, informed policies that can keep pace with technological\nprogress and ensure AI benefits society as a whole. The next step is for leaders,\npolicymakers, and regulators to craft governance oversight that allows innovation to\nprogress under watchful supervision and in an atmosphere of accountability.", "metadata": {"source": "Web UI", "created_at": "2025-08-28T05:41:09.907Z"}, "created_at": "2025-08-28T13:41:09.912802", "updated_at": "2025-08-28T13:41:09.912802", "word_count": 23097}