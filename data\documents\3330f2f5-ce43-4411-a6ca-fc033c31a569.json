{"doc_id": "3330f2f5-ce43-4411-a6ca-fc033c31a569", "title": "item093_US_Securing CriticalSecuring Critical Infrastructure in the Age of AI", "text": "Opportunities, and Barriers Associated with AI\r\nAs evidenced by the wide range of current and potential use cases for AI in critical\r\ninfrastructure, many workshop participants expressed interest in adopting AI\r\ntechnologies in their respective sectors. However, many were also concerned about the\r\nbroad and uncharted spectrum of risks associated with AI adoption, both from external\r\nmalicious actors and from internal deployment of AI systems. CI sectors also face a\r\nvariety of barriers to AI adoption, even for use cases that may be immediately\r\nbeneficial to them. This section will briefly summarize the discussion concerning these\r\nthree topics: risks, opportunities, and barriers to adoption.\r\nRisks\r\nAI risk is twofold, encompassing both malicious use of AI systems and AI system\r\nvulnerabilities or failures. This subsection will address both of these categories,\r\nstarting with risks from malicious use, which several workshop participants raised\r\nconcerns about given the current prevalence of cyberattacks on U.S. critical\r\ninfrastructure. These concerns included how AI might help malicious actors discover\r\nnew attack vectors, conduct reconnaissance and mapping of complex CI networks, and\r\nmake cyberattacks more difficult to detect or defend against. AI-powered tools lower\r\nthe barrier to entry for malicious actors, giving them a new (and potentially low-cost)\r\nway to synthesize vast amounts of information to conduct cyber and physical security\r\nattacks. However, the addition of AI alone does not necessarily present a novel threat,\r\nas CI systems are already targets for various capable and motivated cyber actors.8\r\nMost concerns about AI in this context centered on its potential to enable attacks that\r\nmay not currently be possible or increase the severity of future attacks. A more\r\ntransformative use of AI by attackers could involve seeking improved insights as to\r\nwhat systems and data flows to disrupt or corrupt to achieve the greatest impact.\r\nGenerative AI capabilities are currently increasing threats to CI providers in certain\r\ncases. These threats include enhanced spear phishing, enabled by large language\r\nmodels. Researchers have observed threat actors exploring the capabilities of\r\ngenerative AI systems, which are not necessarily game-changing but can be fairly\r\nuseful across a wide range of tasks such as scripting, reconnaissance, translation, and\r\nsocial engineering.9 Furthermore, as AI developers strive to improve generative\r\nmodels’ capabilities by enabling the model to use external software tools and interact\r\nwith other digital systems, digital “agents” that can translate general human\r\ninstructions into executable subtasks may soon be used for cyber offense.\r\nCenter for Security and Emerging Technology | 12\r\nThe other risk category participants identified was related to AI adoption, such as the\r\npotential for data leakage, a larger cybersecurity attack surface, and greater system\r\ncomplexity. Data leakage was a significant concern, regarding both the possibility of a\r\nCI operator’s data being stored externally (such as by an AI provider) and the potential\r\nfor sensitive information to accidentally leak due to employee usage of AI (such as by\r\nprompting an external large language model).\r\nIncorporating AI systems could also increase a CI operator’s cybersecurity attack\r\nsurface in new—or unknown—ways, especially if the AI system is used for either OT or\r\nIT. (A use case encompassing OT and IT, which are typically strictly separated with\r\nfirewalls to limit the risk of compromise, would increase the attack surface even\r\nfurther.) For certain sectors, participants pointed out that even mapping an operator’s\r\nnetworks to evaluate an AI system’s usefulness—and subsequently storing or sharing\r\nthat sensitive information—could present a target for motivated threat actors. CI\r\noperators face more constraints than organizations in other industries and therefore\r\nneed to be extra cautious about disclosing information about their systems. Newer AI\r\nproducts, especially generative AI systems, may also fail unexpectedly because it is\r\nimpossible to thoroughly test the entire range of inputs they might receive.\r\nFinally, AI systems’ complexity presents a challenge for testing and evaluation,\r\nespecially given that some systems are not fully explainable (in the sense of not being\r\nable to trace the processes that lead to the relationship between inputs and outputs).\r\nRisks associated with complexity are compounded by the fact that there is a general\r\nlack of expertise at the intersection of AI and critical infrastructure, both within the CI\r\ncommunity and on the part of AI providers.\r\nOpportunities\r\nDespite acknowledgment of the risks associated with the use of AI, there was general\r\nagreement among participants that there are many benefits to using AI technologies in\r\ncritical infrastructure.\r\nAI technologies are already in use in several sectors for tasks such as anomaly\r\ndetection, operational awareness, and predictive analytics. These are relatively mature\r\nuse cases that rely on older, established forms of AI and machine learning (such as\r\nclassification systems) rather than newer generative AI tools.\r\nOther opportunities for AI adoption across CI sectors include issue triage or\r\nprioritization (such as for first responders), the facilitation of information sharing in the\r\ncybersecurity or fraud contexts, forecasting, threat hunting, Security Operations Center\r\nCenter for Security and Emerging Technology | 13\r\n(SOC) operations, and predictive maintenance of OT systems. More generally,\r\nparticipants were interested in AI’s potential to help users navigate complex situations\r\nand help operators provide more tailored information to customers or stakeholders\r\nwith specific needs.\r\nBarriers to Adoption\r\nEven after considering the risk-opportunity trade-offs, however, several participants\r\nnoted that CI operators face a variety of barriers that could prevent them from\r\nadopting an AI system even when it may be fully beneficial.\r\nSome of these barriers to adoption are related to hesitancy around AI-related risks,\r\nsuch as data privacy and the potential broadening of one’s cybersecurity attack surface.\r\nSome operators are particularly hesitant to adopt AI in OT (where it might affect\r\nphysical systems) or customer-facing applications. The trustworthiness—or lack\r\nthereof—of AI systems is also a source of hesitancy.\r\nOther barriers are due to the unique constraints faced by CI operators. For instance, the\r\nfact that some systems have to be constantly available is a challenge unique to CI.\r\nOperators in sectors with important dependencies—such as energy, water, and\r\ncommunications—have limited windows in which they can take their systems offline.\r\nOT-heavy sectors also must contend with additional technical barriers to entry, such as\r\na general lack of useful data or a reliance on legacy systems that do not produce\r\nusable digital outputs. In certain cases, it may also be prohibitively expensive—or even\r\ntechnically impossible—to conduct thorough testing and evaluation of AI applications\r\nwhen control of physical systems is involved.\r\nA third category of barriers concerns compliance, liability, and regulatory requirements.\r\nCI operators are concerned about risks stemming from the use of user data in AI\r\nmodels and the need to comply with fractured regulatory requirements across different\r\nstates or different countries. For example, multinational corporations in sectors such as\r\nIT or communications are beholden to the laws of multiple jurisdictions and need to\r\nadhere to regulations such as the European Union’s General Data Protection\r\nRegulation (GDPR), which may not apply to more local CI operators.\r\nFinally, a significant barrier to entry across almost all sectors is the need for workers\r\nwith AI-relevant skills. Participants noted that alleviating workforce shortages by\r\nhiring new workers or skilling up current employees is a prerequisite for adopting AI in\r\nany real capacity.\r\nCenter for Security and Emerging Technology | 14\r\nObservations\r\nThroughout the workshop, four common trends emerged from the broader discussion.\r\nDifferent participants, each representing different sectors or government agencies,\r\nraised them at multiple points during the conversation, an indicator of their saliency.\r\nThese topics include the disparities between large and small CI providers, the difficulty\r\nin defining lines between AI- and cyber-related issues, the lack of clear ownership over\r\nAI risk within an organization, and the challenges posed by fractured regulation and\r\nguidance. In the following sections, we examine these observations and highlight the\r\nissues raised during the workshop.\r\nDisparities Between and Within Sectors\r\nCI in the United States covers many different organizations and missions, ranging from\r\nnationwide banks to regional electric utilities to local water providers that may serve\r\nonly a few thousand residents. The wide gap in resources across CI providers, falling\r\nroughly along the lines of large organizations and small providers, was repeatedly\r\nraised throughout the workshop. This disparity can exist between sectors, such as\r\nbetween the comparatively better-resourced financial services sector and the less\r\nwell-resourced water sector, and within sectors, such as between major banks and\r\nregional lenders.\r\nThese resource disparities between providers impact cybersecurity and the prospects\r\nof AI adoption within CI in several ways.\r\n• Financial resources: Differences across and within sectors in available monetary\r\nresources to implement AI have led and likely will continue to lead to the\r\nconcentration of AI adoption among the most well-financed organizations. As\r\nsuch, the numerous potential benefits of AI discussed previously will likely be\r\nout of reach for many small providers without financial or technical assistance.\r\n• Talent: Closely related to the issue of adequate funding is the limited technical\r\nexpertise that different providers have on staff or have the ability to hire.\r\nWorkers with AI and cybersecurity skills are already scarce. The competitive job\r\nmarket, and higher salaries for these positions, make it difficult for smaller\r\nproviders to attract requisite talent. Some sectors, such as IT and finance,\r\nalready have large technical staffs and are well positioned to incorporate and\r\nsupport new AI talent compared to organizations in the manufacturing, electric,\r\nor water sectors, which typically have more limited IT operations and staff.\r\nCenter for Security and Emerging Technology \r\n• Data: The ability to produce or obtain large amounts of data for use in AI\r\napplications can be a substantial challenge for small providers. The size of the\r\norganization and scale of operations is only one aspect of the problem. Small\r\nutilities often operate older or bespoke OT systems that generate limited data or\r\nlack digital output. Making bespoke data usable for AI applications is often\r\ncostly and time-consuming. Furthermore, many of these systems are configured\r\nto fit the unique needs of the provider, which may prevent the generalization of\r\nmodels trained on data from the same machines or devices deployed in other\r\nenvironments.\r\n• Forums: Methods of communication and coordination between organizations\r\nwithin sectors vary widely. While trusted third parties—such as the Sector\r\nCoordinating Councils and Information Sharing and Analysis Centers (ISACs)—\r\nexist in most sectors, certain sectors have additional forums to facilitate\r\ncollaboration, sharing of threat information, and the development of best\r\npractices, all of which play a key role in the adoption of new technology such as\r\nAI. Examples of well-established forums for collaboration include the Financial\r\nand Banking Information Infrastructure Committee and the Cyber Risk Institute\r\nin the financial services sector and the Electricity Subsector Coordinating\r\nCouncil’s Cyber Mutual Assistance Program in the energy sector. The\r\nCybersecurity and Infrastructure Security Agency (CISA), Sector Risk\r\nManagement Agencies (SRMAs), and the sectors themselves will need to\r\nidentify, deconflict, and potentially expand existing forums to manage emerging\r\nAI risks and security issues. This could also include additional cross-sector\r\ncoordination.*\r\n• Voice: Smaller organizations within sectors face many obstacles in contributing\r\nto the formation of best practices and industry standards. The absence of input\r\nfrom these groups risks the development of AI standards that do not account for\r\nresource constraints and, lacking appropriate guidance on prioritizing practices,\r\ncan be difficult or infeasible for smaller organizations to implement.\r\nDespite all these challenges, there are compelling reasons to pursue AI applications\r\neven for smaller, less well-resourced organizations and sectors. Of the many potential\r\nbenefits afforded by AI, the use of this technology for anomaly and threat detection is\r\nparticularly impactful and, in the context of CI, vitally important. Smaller providers can\r\nill afford to be left behind in adopting AI for cyber defense, especially given the\r\n* The recently formed DHS AI Safety and Security Board could serve as another forum as its roles and\r\nresponsibilities are further delineated.\r\nCenter for Security and Emerging Technology | 16\r\npotential threat posed by faster, subtler, and more sophisticated AI-enabled\r\ncyberattacks. Solutions offered as a service or that work to tailor AI for bespoke\r\napplications would help lower these barriers and enable the use of sector or\r\norganizational datasets—once properly formatted for AI training—to support IT or OT\r\nsecurity tasks.\r\nUnclear Boundary Between AI and Cybersecurity\r\nDistinguishing between the issues related to AI and cybersecurity, as well as the\r\noverlap between the two, was a common challenge identified across sectors. In\r\ngeneral, this challenge reflects the underlying ambiguity between AI safety and AI\r\nsecurity—two academic disciplines that have developed separately, but both of which\r\nare needed for robust AI risk management.10 This ambiguity arose in three contexts:\r\nrisk, incidents, and the workforce.\r\n• Risk: Determining whether a given risk associated with an AI system is an AI\r\nrisk, which would fall under the purview of the National Institute of Standards\r\nand Technology's AI Risk Management Framework (AI RMF), or a cybersecurity\r\nrisk, which would align with NIST’s Cybersecurity Framework 2.0 (CSF), is not\r\nabundantly clear. This ambiguity raises the question whether this explicit\r\ndistinction needs to be made at all, yet the existence of separate frameworks\r\nand the division of risk ownership within corporate structures—both discussed\r\nin detail later in this report—seems to demand this distinction be made. Take,\r\nfor example, the question of whether issues of bias and fairness are an AI risk, a\r\ncyber risk, or both. This may largely depend on the context of the application in\r\nwhich AI is being used and how it pertains to the critical function of the provider.\r\nFor example, bias and fairness surrounding the use of AI in decisions regarding\r\ncredit scores, a critical function in the financial sector, presents a risk that spans\r\nacross safety and security. This presents a serious challenge for organizations\r\nattempting to clearly divide AI and cybersecurity—or, alternatively, AI safety\r\nand AI security—risk management responsibilities. As the AI RMF\r\nacknowledges, “Treating AI risks along with other critical risks, such as\r\ncybersecurity and privacy, will yield a more integrated outcome and\r\norganizational efficiencies.” However, it was clear during the discussion with\r\nworkshop participants that further guidance on how to implement a unified\r\napproach to risk is needed.\r\n• Incidents: There is similar ambiguity surrounding what qualifies as a cyber\r\nincident, an AI incident, a safety incident, an ethical incident, or some\r\ncombination of these. While there are clear requirements and channels for\r\nCenter for Security and Emerging Technology | 17\r\ncyber-incident reporting, which could possibly cover AI-related cyber incidents,\r\nit is unclear if and how information related to non-cyber AI incidents should be\r\nshared. Furthermore, the analogues between cyber and AI incidents are not\r\nperfect. For example, some AI incidents may not have easily defined\r\nremediations or patches, as has been noted in other research.11 This suggests\r\nthat remediation efforts for AI incidents will need additional mitigation\r\nstrategies.12 Defining the range of AI-related incidents and what subset falls\r\nunder existing reporting requirements would be valuable. For AI incidents that\r\nare not covered by existing requirements, the benefit to sharing information as it\r\npertains to AI-related failures, mitigations, and best practices was widely\r\nrecognized by workshop participants. However, there was disagreement as to\r\nwhether this information sharing should be done through formal channels, with\r\nexplicit reporting requirements, or informal channels such as the AI Incident\r\nDatabase or other proposed public repositories.13 Clarity on what constitutes an\r\nAI incident, which incidents should be reported, the thresholds for reporting, and\r\nwhether existing cyber-incident reporting channels are sufficient would be\r\nvaluable. Ongoing work at CISA, through the Joint Cyber Defense Collaborative\r\n(JCDC), aims to provide further guidance later this year.14\r\n• Workforce: Projecting what workforce CI organizations will need to leverage AI\r\nand meet the challenges posed by AI-enabled threats is difficult. It is unclear if\r\nAI risk management will require personnel with AI-specific skills, cybersecurity\r\nexperts with specialization or cross-training in AI risk, or a completely new set\r\nof personnel with both AI and cybersecurity expertise. Some aspects of\r\ntraditional cybersecurity best practices such as authentication and data\r\nprotection also apply to managing AI risk. However, the design and\r\nimplementation of AI systems requires unique expertise that many CI providers\r\nmay not have in their current cyber workforce. At a minimum, the AI and\r\ncybersecurity experts in an organization will need some cross-training to\r\ncollaborate effectively and speak a common language to address the full range\r\nof AI and cyber risks.\r\nChallenges in AI Risk Management\r\nAs AI applications become more prevalent in CI, sectors and organizations must\r\nmanage the attendant risk. Participants noted the need to integrate AI risk\r\nmanagement into existing processes at many organizations. Yet, at the same time,\r\nownership of AI risk can be ambiguous within current corporate structures. It was\r\nreferred to by one participant as the AI “hot potato” being tossed around the C-suite.\r\nCenter for Security and Emerging Technology | 18\r\nToday, AI risk management does not neatly fall under any single corporate leadership\r\nposition, such as the chief information security officer, the chief technology officer, the\r\nchief information officer, or the chief data officer. Aspects of AI, and its related risk,\r\noften span the responsibilities of these different roles. While the need to include AI risk\r\nmanagement into the overall enterprise strategy is clear, who owns AI risk within the\r\norganization is anything but. For example, Govern 2.1 of the NIST AI RMF states that\r\n“roles and responsibilities and lines of communication related to mapping, measuring,\r\nand managing AI risks are documented and are clear to individuals and teams\r\nthroughout the organization,” but the details on which actors should be directly\r\nresponsible are limited.15 Some organizations are approaching this challenge by\r\nappointing a new chief AI officer, while others have rolled it into the responsibilities of\r\na chief resilience officer. However, the most common—albeit potentially less\r\npermanent—solution has been for organizations to share the responsibility across\r\nroles or to “dual-hat” an existing officer, typically the chief data officer.\r\nWhile organizations within and outside of CI are grappling with how to manage risks\r\nposed by AI, these challenges may be particularly acute within the CI sectors. Many CI\r\nproviders have a “compliance culture” due to the high degree of regulation they face\r\nand the essential services they manage, such as providing clean water or keeping the\r\nlights on. Therefore, regulatory requirements and resulting organizational policies are\r\noften written in a binary manner—either the organization does or does not meet the\r\ngiven requirement. However, the same approach does not apply well in the context of\r\nAI. The output of AI models is inherently probabilistic: a system will or will not produce\r\na certain outcome with probability of n. This is at odds with policies and requirements\r\nunder a compliance-oriented regime that specify a system will (a 100 percent\r\nlikelihood) or will not (a 0 percent likelihood) do something with complete certainty. As\r\nsuch, AI risk management demands a “risk-aware culture” in which the focus is on\r\nreducing the likelihood of harm rather than meeting a checklist of requirements. These\r\ndifferences in risk management cultures may affect the safe and secure adoption of AI\r\nin many CI sectors.\r\nFractured Guidance and Regulation\r\nA commonly expressed concern during the workshop was that many CI providers are\r\nstruggling to operationalize AI risk management. In addition to the resource constraints\r\ndiscussed earlier, two key factors contribute to this problem: fractured guidance and\r\nregulation.\r\n• Guidance: There are a multitude of overlapping frameworks that pertain to AI,\r\ncybersecurity, and privacy. These include NIST’s AI RMF (and subsequent\r\nCenter for Security and Emerging Technology | 19\r\n“Playbook” and draft “Generative AI Profile”), CSF, and Privacy Framework; the\r\nFederal Trade Commission’s Fair Information Practice Principles; and a variety of\r\nstandards from the International Organization for Standardization.\r\nUnderstanding how these frameworks work together, which set of guidance is\r\napplicable where, and how to operationalize recommended practices for a given\r\nAI use case represents a substantial hurdle for organizations. Participants noted\r\ntwo key challenges related to this issue.\r\no First, each respective framework presents numerous recommended practices\r\nto implement, and, when combined, the scope of those recommendations\r\ncan become burdensome, even for well-resourced organizations. The lack of\r\ngeneral guidance on how to prioritize among the multitude of recommended\r\npractices, particularly when facing resource constraints, and the lack of\r\nguidance tailored to specific sectors were highlighted as major obstacles to\r\noperationalizing recommended practices. Participants noted that community\r\nprofiles, like those produced to accompany the CSF, were helpful additions\r\nto the high-level guidance. However, these profiles take time to develop, and\r\ncurrently there are no finalized profiles for the AI RMF. With the rapid pace\r\nof AI development and the push for adoption, there may be an important role\r\nfor trusted third parties to move faster in addressing this guidance gap.\r\no Second, the ambiguity at the intersection of these overlapping frameworks\r\nmakes it challenging for organizations to interpret what guidance applies\r\nwhere. For example, the core activities in both the cybersecurity and privacy\r\nframeworks include a protect function (“Protect” and “Protect-P,”\r\nrespectively), which covers recommended safeguards and security measures.\r\nYet, the AI RMF does not have a protect function. While organizations can\r\ndraw on security practices from the CSF, analogues from cybersecurity—\r\nsuch as red-teaming—do not always translate directly to the context of AI.16\r\nFurthermore, these measures may not protect against the range of\r\nvulnerabilities unique to AI systems.17 The ambiguity and potential gaps that\r\narise at the intersection of these frameworks make it difficult to piece\r\ntogether how they should be applied in concert. As a result, CI providers\r\nlooking to implement safe and secure AI systems face the challenge of trying\r\nto deconflict implementation guidance from a patchwork set of frameworks,\r\ntechnical research reports, and industry practices. Distilling this information\r\nrequires time and expertise that many organizations, particularly less well resourced\r\nones, cannot afford without assistance. Ongoing efforts within\r\nCenter for Security and Emerging Technology | 20\r\nNIST, such as the Data Governance and Management Profile, are likely to\r\nhelp in this regard and were deemed a high priority by participants.18\r\n• Regulation: Concerns over the fractured regulatory environment regarding data\r\nprotection and cybersecurity, and the potential for a similar governance regime\r\nfor AI, pose another major barrier for CI providers in adopting AI systems. With\r\nthe lack of overarching federal regulation for privacy or cybersecurity, a\r\npatchwork of requirements has been made at the state level that various CI\r\nproviders must comply with. Furthermore, some CI providers have a global\r\npresence and are impacted by international regulations as well, notably the\r\nEuropean Union’s GDPR and the more recent Artificial Intelligence Act. The lack\r\nof harmonization between these different regulations poses a compliance risk\r\nfor organizations seeking to implement AI systems, particularly those that may\r\nThe following recommendations stem from discussions held during the workshop and\r\nare designed to provide an array of policy options for governing the future use of AI in\r\ncritical infrastructure. They are divided into four subsections by stakeholders at\r\ndifferent levels of governance: (1) cross-cutting recommendations that apply to all\r\nactors at the intersection of AI and critical infrastructure; (2) recommendations for\r\ngovernment actors to consider; (3) recommendations for CI sectors; and (4)\r\nrecommendations for individual organizations, encompassing both CI operators and AI\r\ndevelopers and deployers.\r\nCross-Cutting Recommendations\r\nThe following recommendations apply to all stakeholders within the critical\r\ninfrastructure and AI ecosystems:\r\n• Participate in information sharing. The sharing of best practices, threat\r\ninformation, and incidents is critical to maintaining the safety and security of AI\r\nsystems employed in CI. While the specific channels for sharing AI security\r\nversus AI safety information are unclear, the need for information sharing across\r\nboth domains is paramount.\r\n○ SRMAs should leverage existing venues for AI security information\r\nsharing. Current ISACs provide a natural forum for additional\r\ncollaboration on AI-enabled threats and security vulnerabilities in AI\r\nsystems. The JCDC could potentially aid in these efforts as well. Less\r\nclear are the mechanisms for sharing information on AI safety risks that\r\ndo not pertain to security. Channels for sharing AI safety information—\r\nsuch as cases of incorrect output, bias, or failures discovered in a given AI\r\nmodel—could be incorporated into existing ISACs or instituted\r\nseparately. Integrating AI safety communication into the existing ISACs\r\ncould reduce overhead, prevent redundancy, provide more holistic insight\r\nfor managing risk, and alleviate the ambiguity between AI safety and\r\nsecurity discussed previously. On the other hand, creating separate\r\ninformation-sharing centers for AI safety could provide more tailored\r\nintel, help reduce the volume of information to process, and maintain the\r\nsecurity-focused mission of the ISACs*. An example of a sector-specific\r\n* Separate information sharing channels for AI safety could potentially fit into or complement the AI\r\nSafety Institute as it continues to develop and gains capacity.\r\nCenter for Security and Emerging Technology | 22\r\nsafety center (not focused on AI) is the Aviation Safety Information\r\nAnalysis and Sharing operated by MITRE.\r\n○ The CI sectors should consider establishing a centralized analysis\r\ncenter for AI safety and security. High-level visibility into AI use across\r\nthe CI sectors is vital to managing overarching risk. This includes\r\nidentifying where and how AI is being used, developing best practices,\r\nand assessing AI safety and security information—whether shared\r\nthrough the same or different channels. To promote cross-sector\r\ninformation sharing and analysis that spans both AI safety and security,\r\nwe recommend the creation of a centralized AI safety and security\r\nanalysis center. The establishment of a National Critical Infrastructure\r\nObservatory, as recommended in a recent report from the President’s\r\nCouncil of Advisors on Science and Technology, would create one\r\npotential home for this cross-sector center.19\r\n○ CI operators and providers should share information on AI-related\r\nincidents, threats, vulnerabilities, and best practices. Defining AI\r\nincidents and sharing relevant information when they occur, whether\r\nthere are cybersecurity implications or not, will be critically important to\r\nidentify new vulnerabilities and harms. For this information to be useful,\r\nproviders need to ensure that they are collecting relevant data and audit\r\nlogs to assess what led up to the incident occurring, how the incident\r\nunfolded, and what efforts were undertaken afterward to identify the\r\nsource of the issue and remedy it going forward. We note that there is\r\ncurrently little guidance on communicating AI incidents, and the sooner\r\nguidance can be released the better. As discussed above, determining the\r\ncommunication channels to use for information sharing and to whom that\r\ninformation is sent is an important prerequisite.\r\nCI providers should also take proactive steps to share information on\r\nobserved threats, vulnerabilities discovered, and industry best practices\r\nrelated to AI use and deployment. Furthermore, the sharing of sectorspecific\r\ndata, including training data for AI systems, could help CI\r\nproviders. While there may be a tendency to retain data for proprietary\r\nreasons or risk of liability, a collaborative approach would help benefit\r\norganizations within each sector, particularly smaller providers who may\r\nnot generate the requisite volume of data for AI use cases. An initial step\r\ncould be prioritizing efforts to share data for AI applications that facilitate\r\nor protect critical services such as predictive maintenance and cyber\r\nCenter for Security and Emerging Technology | 23\r\ndefense. Data sharing in these areas is likely more feasible, as incentives\r\nalign across organizations, and is potentially very impactful.\r\n• Develop the workforce. Participants universally agreed that hiring and\r\ndeveloping AI talent is a crucial prerequisite for effectively adopting AI in critical\r\ninfrastructure systems.\r\n○ Federal and state government organizations should fund training\r\nprograms and other workforce development initiatives. As mentioned\r\nabove, workforce capacity—and the lack thereof—was a theme\r\nthroughout the entire discussion. Some participants recommended that\r\npolicymakers consider funding workforce development initiatives\r\nexplicitly aimed at improving capacities within the CI sectors.\r\n○ CI sectors should coordinate workforce development efforts and\r\ndevelop sector-specific requirements. The sectors should play an\r\nimportant intermediary role in the design and implementation of AI\r\ntraining programs. This starts with identifying the specific AI talent needs\r\nwithin their sector and developing requirements that help inform the\r\ndesign of the training programs. In addition, the CI sectors should take a\r\nleading role in coordinating the implementation of these programs and\r\nprioritizing where resources for workforce development are needed most.\r\n○ CI operators and providers should actively upskill their current\r\nworkforces. Developing requisite AI talent will remain a large\r\nundertaking, and one way to partially address the demand is to upskill\r\nexisting staff. One aspect of this upskilling may be training individual\r\nworkers capable of deploying and managing AI systems for organizations\r\nthat operate them. Another may include promoting general AI literacy\r\namong staff on the proper use of AI-enabled tools as well as risks posed\r\nby AI-enabled threats, such as sophisticated spear-phishing attacks. Of\r\nparticular note, CI providers should ensure that their staff are aware of\r\nthe risk of including proprietary or sensitive information in prompts sent\r\nto third-party AI services.\r\nResponsible Government Departments and Agencies\r\nSpecific recommendations for relevant government actors include:\r\n• Harmonize regulation relevant to CI. Participants expressed confusion and\r\nSome participants also expressed a desire for harmonization efforts to apply to\r\nany future AI-specific legislation, both at the state and federal levels.\r\n• Work with sector partners to tailor and operationalize guidance for each\r\nsector. Government guidance aimed at the CI sectors can be difficult to\r\noperationalize because of its generality. Inherently, developing guidance that\r\napplies to everyone runs the risk of fitting no one. This is particularly salient\r\nwithin the CI sectors, where providers often operate bespoke and specialized\r\nsystems. Guidance tailored to specific sectors and additional guidance on\r\noperationalization would benefit many operators. For example, prior to the\r\nrelease of the NIST CSF, NIST had released a version of the cybersecurity\r\nframework specifically targeted at improving CI cybersecurity.20 Similar tailoring\r\nof guidance related to AI—at a level more specific than existing resources such\r\nas the NIST AI RMF Playbook—may be helpful to CI operators, especially those\r\nwho are under-resourced.21 The NIST “Generative AI Profile” and the Secure\r\nSoftware Development Practices for Generative AI and Dual-Use Foundation\r\nModels are examples of such tailored guidance.22 Developing AI profiles specific\r\nto CI sectors, similar to existing cybersecurity ones, would also help advance\r\nsafe and secure adoption.\r\n• Support relevant infrastructure to test and evaluate AI systems. As\r\nmentioned above, the practice of AI model evaluation remains immature. Many\r\nevaluations are conducted by model developers without a mechanism to\r\nindependently evaluate the results. Importantly, however, the role third parties\r\nwill play in evaluations remains unclear. Organizations such as the NIST AI\r\nSafety Institute could play a leading role in future model evaluations but will\r\nneed additional resourcing in the form of funding and personnel. Third-party\r\nauditing of models and assessments against defined benchmarks could provide\r\nCI operators additional confidence in the safety and security of these models.\r\nIdeas discussed at the workshop included using existing test beds for CI or\r\ndesigning test beds exclusively for AI testing and evaluation, which could allow\r\nfor continued research on how models behave in deployed environments.\r\nAdditionally, further research into risk evaluation metrics for AI is needed, as\r\nwell as a shared understanding of how cybersecurity test and evaluation\r\npractices can be used to test network infrastructures deploying AI technologies.\r\nIdeally, these resources should be accessible to all CI sectors.\r\nCenter for Security and Emerging Technology | 25\r\n• Expand ecosystem monitoring efforts. A continuation and expansion of efforts\r\nto identify AI use cases being deployed across CI sectors is critical for\r\nmaintaining ecosystem-wide visibility and assessing overall risk. In conducting\r\nour background research on how sectors are using current AI applications, we\r\nfound that many reported use cases lacked important details needed to assess\r\nrisk, such as how the organization used the AI system (e.g., experiments in a\r\nlaboratory or deployed in production) and what type of model they used. Future\r\nvisibility efforts, such as the annual cross-sector risk assessments conducted by\r\nCISA, should collect and report these details.\r\nSectors\r\nRecommendations for the CI sectors as a whole include:\r\n• Develop best practices. Establishing and sharing best practices around the\r\nimplementation and use of AI within a given sector is critical to operationalizing\r\nAI safety and security guidance. The CI sectors should facilitate the\r\ndevelopment and coordination of these tailored best practices, ensuring that\r\nproviders both small and large can provide input into the process.\r\n• Expand and support mutual assistance. To help address the disparities\r\nbetween and within sectors, workshop participants recommend expanding both\r\ninformal and formal means of mutual assistance. These initiatives help share\r\nresources, talent, and knowledge across organizations in an effort to improve\r\nthe security and resiliency of the sector as a whole. An example of formal\r\nmutual assistance is the Electricity Subsector Coordinating Council’s Cyber\r\nMutual Assistance Program, which connects a network of cybersecurity\r\nprofessionals who provide support to participating providers in the case of a\r\ncyber emergency. Informal mutual assistance often results from the efforts of\r\nlarge providers that have spillover or secondary benefits for smaller providers.\r\nSome examples could include the development of industry standards and the\r\nvetting of products and service providers. To address the issue of smaller\r\nproviders not having a voice in some of these informal practices, larger\r\norganizations and sector-coordinating bodies should work to gather and\r\nincorporate input from smaller providers as a part of these processes.\r\nOrganizations\r\nWe break down the recommendations for individual organizations into those directed\r\ntoward CI providers and those for AI developers.\r\nCenter for Security and Emerging Technology | 26\r\nCritical Infrastructure Operators\r\nRecommendations for providers and organizations within CI sectors include:\r\n• Integrate AI risk management into enterprise risk management. To address AI\r\nrisk properly, it must be fully integrated into existing enterprise risk\r\nmanagement practices. Organizations should develop these practices based on\r\nNIST’s AI RMF and utilize tools such as NIST’s recently released Dioptra\r\nassessment platform.23 However, as noted previously, further tailored guidance\r\nis needed on how to integrate the AI RMF recommendations into existing\r\npractices, which are often based on the CSF and Privacy Framework.\r\n• Designate clear ownership over AI risk management. While perspectives\r\ndiffered on who within the corporate structure should own AI risk, it is clear that\r\nintegrating AI risk into enterprise risk management is dependent on defining\r\nownership clearly. Since issues related to AI risk span many of the\r\nresponsibilities of the standard corporate leadership positions, one option could\r\nbe establishing a new chief AI officer role. If organizations are reluctant to create\r\nnew positions, they may need to consider leveraging an existing internal forum\r\nor creating a new one that brings together the relevant organizational leaders to\r\nassess AI risk. However, for many smaller providers or organizations looking to\r\ndeploy AI in a very narrow scope, assigning ownership of AI risk to an existing\r\nofficer—or specific board member, for local providers—is likely preferable.\r\n• Remain cautious and informed before adopting newer AI technologies,\r\nparticularly for sensitive or mission-critical tasks. On the positive side, older\r\nmachine learning techniques have been extremely beneficial in cybersecurity,\r\nparticularly anomaly and threat detection. However, for newer AI technologies\r\nsuch as generative AI, participants were in favor of sectors adopting a cautious\r\nand measured approach to adoption. Tools like MITRE’s AI Maturity Model can\r\nbe helpful for providers to assess their ability and readiness to adopt AI\r\nsystems.24\r\nAI Developers\r\nAI developers are producing new generative AI capabilities almost daily. These\r\nproducts have the potential to assist CI providers in the operation of their systems and\r\ntheir sector-specific use cases. However, many CI operators do not have the AI\r\nexpertise to make informed risk management decisions. To assist CI operators, AI\r\ndevelopers should:\r\nCenter for Security and Emerging Technology | 27\r\n• Engage in transparency best practices. This includes publishing information\r\nabout models in the form of model cards or “nutrition labels,” similar to what\r\nhas been proposed for Internet of Things devices.25 Participants also noted that\r\nincreased information on training data provenance, which most AI developers\r\ncurrently do not provide, would be beneficial to evaluate risk associated with an\r\nAI system. Transparency on the results of model evaluations (for safety,\r\nsecurity, or otherwise) and model vulnerabilities would also be valuable.\r\n• Improve trust by developing methods for AI interpretability and\r\nexplainability. While the two terms are sometimes used interchangeably,\r\ninterpretability generally refers to the ability to mechanistically analyze the\r\ninner workings of a model’s decision-making process, while explainability refers\r\nto providing post hoc explanations for a model’s behavior. While methodologies\r\nfor both interpretability and explainability would help improve trust in AI\r\nsystems, interpretability may be particularly important for logging and\r\nverification. Meanwhile, a lack of explainability is a major deterrent for CI\r\noperators considering adopting AI systems, especially for OT or customer-facing\r\nuse cases. While these are evolving fields of research and participants\r\nacknowledged that there is currently no magic bullet for explainable or\r\ninterpretable AI, continued investment in these fields could be beneficial for\r\nimproving operators’ trust in AI systems.", "metadata": {"original_filename": "item093_US_Securing CriticalSecuring Critical Infrastructure in the Age of AI.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:51:54.464874", "updated_at": "2025-08-28T21:51:54.464874", "word_count": 41842}