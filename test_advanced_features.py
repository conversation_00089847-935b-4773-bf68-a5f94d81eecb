#!/usr/bin/env python3
"""
🚀 PANTOS高级功能测试脚本
测试AI模型编排器和新分析模块
"""

import asyncio
import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"

def print_section(title):
    """打印分节标题"""
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print(f"{'='*60}")

def print_subsection(subtitle):
    """打印子标题"""
    print(f"\n📋 {subtitle}")
    print("-" * 40)

async def test_system_status():
    """测试系统状态"""
    print_section("系统状态检查")
    
    try:
        # 1. 基础健康检查
        print("🏥 检查基础健康状态...")
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 系统健康检查通过")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
        
        # 2. PANTOS系统状态
        print("\n🎯 检查PANTOS系统状态...")
        response = requests.get(f"{BASE_URL}/api/v1/system/status", timeout=10)
        if response.status_code == 200:
            status = response.json()
            print("✅ PANTOS系统状态获取成功")
            
            print(f"  📊 系统框架: {status['system']['framework']}")
            print(f"  📊 API版本: {status['api_versions']}")
            print(f"  📊 核心功能: {sum(status['core_features'].values())}/4 可用")
            print(f"  📊 高级功能: {sum(status['advanced_features'].values())}/6 可用")
            
            if "infrastructure" in status:
                infra = status["infrastructure"]
                print(f"  🏗️  基础设施: {infra.get('available_components', 0)}/{infra.get('total_components', 0)} 可用")
            
            if "services" in status:
                services = status["services"] 
                print(f"  🎯 微服务: {services.get('available_services', 0)}/{services.get('total_services', 0)} 可用")
            
            return True
        else:
            print(f"❌ PANTOS系统状态获取失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 系统状态检查异常: {e}")
        return False

async def test_analysis_capabilities():
    """测试分析能力"""
    print_section("分析能力检查")
    
    try:
        # 检查v2 API能力
        print("🔍 检查v2 API分析能力...")
        response = requests.get(f"{BASE_URL}/api/v2/analysis/capabilities", timeout=10)
        
        if response.status_code == 200:
            capabilities = response.json()
            print("✅ 分析能力获取成功")
            
            print(f"  📊 总任务数: {len(capabilities)}")
            
            for cap in capabilities:
                status = "✅" if cap["is_available"] else "🚧"
                print(f"  {status} {cap['task_id']}: {cap['task_name']} (v{cap['version']})")
                print(f"      {cap['description']}")
                print(f"      预计时间: {cap['estimated_time']}s, 支持模型: {len(cap['supported_models'])}")
            
            return True
        else:
            print(f"❌ 分析能力检查失败: {response.status_code}")
            if response.status_code == 404:
                print("  ℹ️  可能v2 API未正确集成")
            return False
            
    except Exception as e:
        print(f"❌ 分析能力检查异常: {e}")
        return False

async def test_orchestrator_status():
    """测试AI模型编排器状态"""
    print_section("AI模型编排器检查")
    
    try:
        print("🤖 检查AI模型编排器状态...")
        response = requests.get(f"{BASE_URL}/api/v2/analysis/orchestrator/status", timeout=10)
        
        if response.status_code == 200:
            status = response.json()["status"]
            print("✅ AI模型编排器状态获取成功")
            
            print(f"  📊 总模型数: {status['total_models']}")
            print(f"  📊 可用模型: {status['available_models']}")
            
            print("\n  🤖 模型详情:")
            for model_id, model_info in status["models"].items():
                circuit_status = "🔴" if model_info["is_circuit_breaker_open"] else "🟢"
                load_status = f"{model_info['current_load']}/{model_info['max_concurrent']}"
                print(f"    {circuit_status} {model_id} ({model_info['type']})")
                print(f"        负载: {load_status}, 可用性: {model_info['availability']:.1%}")
                print(f"        响应时间: {model_info['avg_response_time']}s")
                print(f"        支持任务: {len(model_info['supported_tasks'])}")
            
            return True
        else:
            print(f"❌ AI模型编排器状态获取失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ AI模型编排器检查异常: {e}")
        return False

async def test_temporal_framing_analysis():
    """测试时间框架分析功能"""
    print_section("时间框架分析测试")
    
    try:
        # 1. 先创建一个测试文档
        print("📝 创建测试文档...")
        doc_data = {
            "title": "时间框架分析测试文档",
            "text": """
            政府将在未来三年内全面实施新的人工智能发展战略。
            历史经验告诉我们，技术革命往往伴随着深刻的社会变革。
            当前，我们正处于人工智能发展的关键时刻，必须抓住机遇。
            过去的失败教训提醒我们，不能重蹈覆辙。
            预计到2030年，人工智能将彻底改变我们的生活方式。
            现在就要开始行动，时不我待，刻不容缓。
            从长远来看，这项政策将为国家发展奠定基础。
            """,
            "metadata": {"test_type": "temporal_framing"}
        }
        
        response = requests.post(f"{BASE_URL}/api/v1/documents/", json=doc_data, timeout=10)
        if response.status_code != 201:
            print(f"❌ 文档创建失败: {response.status_code}")
            return False
        
        doc_result = response.json()
        doc_id = doc_result["doc_id"]
        print(f"✅ 测试文档创建成功: {doc_id}")
        
        # 2. 测试时间框架分析
        print("\n🕐 执行时间框架分析...")
        response = requests.get(f"{BASE_URL}/api/v2/analysis/test/temporal-framing/{doc_id}", timeout=30)
        
        if response.status_code == 200:
            result = response.json()["result"]
            print("✅ 时间框架分析成功")
            
            # 显示分析结果摘要
            temporal_dist = result["temporal_distribution"]
            print(f"\n  📊 时间分布分析:")
            print(f"    主导时间方向: {temporal_dist['dominant_direction']}")
            print(f"    时间多样性: {temporal_dist['temporal_diversity']}")
            
            for direction, proportion in temporal_dist["temporal_proportions"].items():
                if proportion > 0:
                    print(f"    {direction}: {proportion:.1%}")
            
            urgency = result["urgency_construction"]
            print(f"\n  ⚡ 紧迫性构建:")
            print(f"    主导紧迫性: {urgency['dominant_urgency_level']}")
            print(f"    紧迫性频率: {urgency['urgency_frequency']}")
            
            coherence = result["temporal_coherence"]
            print(f"\n  🔗 时间连贯性:")
            print(f"    连贯性评分: {coherence['coherence_score']:.2f}")
            print(f"    主导策略: {coherence['dominant_temporal_strategy']}")
            print(f"    叙事流畅度: {coherence['narrative_flow_quality']}")
            
            strategies = result["narrative_strategies"]
            print(f"\n  🎯 叙事时间策略:")
            for strategy in strategies:
                print(f"    📈 {strategy['strategy_type']}: {strategy['strength']:.2f}")
                print(f"        {strategy['description']}")
            
            print(f"\n  🎖️  置信度: {result['analysis_metadata']['confidence_score']:.2f}")
            
            return True
        else:
            print(f"❌ 时间框架分析失败: {response.status_code}")
            if response.text:
                print(f"  错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 时间框架分析测试异常: {e}")
        return False

async def test_advanced_analysis_api():
    """测试高级分析API"""
    print_section("高级分析API测试")
    
    try:
        # 使用之前的文档进行高级分析
        print("🔍 获取现有文档进行高级分析...")
        response = requests.get(f"{BASE_URL}/api/v1/documents/", timeout=10)
        
        if response.status_code != 200:
            print("❌ 无法获取文档列表")
            return False
        
        documents = response.json()["documents"]
        if not documents:
            print("❌ 没有可用的文档")
            return False
        
        # 使用第一个文档
        doc_id = documents[0]["doc_id"]
        print(f"✅ 使用文档: {doc_id}")
        
        # 2. 测试高级分析API
        print("\n🚀 执行高级分析...")
        analysis_request = {
            "doc_id": doc_id,
            "tasks": ["actor_relation", "temporal_framing"],
            "priority": 1,
            "use_ai_orchestrator": True,
            "enable_new_tasks": True,
            "analysis_config": {
                "timeout": 30,
                "detailed_output": True
            }
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v2/analysis/advanced",
            json=analysis_request,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 高级分析执行成功")
            
            print(f"\n  📊 分析概览:")
            print(f"    分析ID: {result['analysis_id']}")
            print(f"    执行时间: {result['performance_metrics']['total_execution_time']:.2f}s")
            print(f"    完成任务: {result['performance_metrics']['tasks_completed']}")
            print(f"    传统任务: {result['performance_metrics']['traditional_tasks']}")
            print(f"    新任务: {result['performance_metrics']['new_tasks']}")
            print(f"    使用编排器: {result['performance_metrics']['orchestrator_used']}")
            
            if result.get("orchestrator_info"):
                orch_info = result["orchestrator_info"]
                print(f"\n  🤖 编排器信息:")
                print(f"    总成本: ${orch_info.get('total_cost', 0):.4f}")
                print(f"    平均执行时间: {orch_info.get('avg_execution_time', 0):.2f}s")
            
            print(f"\n  📈 分析结果:")
            for task, task_result in result["results"].items():
                if "error" not in task_result:
                    print(f"    ✅ {task}: 分析成功")
                else:
                    print(f"    ❌ {task}: {task_result['error']}")
            
            return True
        else:
            print(f"❌ 高级分析失败: {response.status_code}")
            if response.text:
                try:
                    error_detail = response.json()
                    print(f"  错误信息: {error_detail}")
                except:
                    print(f"  错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 高级分析API测试异常: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始PANTOS高级功能测试...")
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试计数
    total_tests = 5
    passed_tests = 0
    
    # 执行测试
    tests = [
        ("系统状态检查", test_system_status),
        ("分析能力检查", test_analysis_capabilities),
        ("AI模型编排器检查", test_orchestrator_status),
        ("时间框架分析测试", test_temporal_framing_analysis),
        ("高级分析API测试", test_advanced_analysis_api),
    ]
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed_tests += 1
                print(f"\n✅ {test_name} - 通过")
            else:
                print(f"\n❌ {test_name} - 失败")
        except Exception as e:
            print(f"\n❌ {test_name} - 异常: {e}")
    
    # 测试总结
    print_section("测试总结")
    print(f"📊 测试统计:")
    print(f"  总测试数: {total_tests}")
    print(f"  通过测试: {passed_tests}")
    print(f"  失败测试: {total_tests - passed_tests}")
    print(f"  通过率: {passed_tests/total_tests:.1%}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有PANTOS高级功能测试通过！")
        print("💡 系统已准备好进行高级政策叙事分析")
    elif passed_tests > 0:
        print(f"\n⚠️  部分功能可用 ({passed_tests}/{total_tests})")
        print("💡 可以使用可用的功能，其他功能可能需要进一步配置")
    else:
        print("\n❌ 所有测试失败")
        print("💡 请检查系统配置和服务状态")
    
    print(f"\n⏰ 测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    asyncio.run(main())
