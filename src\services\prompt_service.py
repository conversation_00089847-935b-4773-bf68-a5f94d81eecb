import logging
import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path
import difflib
import re

from src.models.schemas import (
    TaskType, PromptTemplate, PromptTemplateCreate, PromptTemplateUpdate,
    PromptComparison, PromptComparisonRequest, PromptTestResult
)
from src.core.config import settings
from src.services.zhipu_client import ZhipuAIClient

logger = logging.getLogger(__name__)


class PromptManagementService:
    """提示词管理服务"""

    def __init__(self, zhipu_client: ZhipuAIClient):
        self.zhipu_client = zhipu_client
        # 创建提示词存储目录
        self.prompts_dir = Path(settings.BASE_DIR) / "data" / "prompts"
        self.prompts_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建提示词版本历史目录
        self.history_dir = self.prompts_dir / "history"
        self.history_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建测试结果目录
        self.test_results_dir = self.prompts_dir / "test_results"
        self.test_results_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化内存存储
        self.prompts: Dict[str, PromptTemplate] = {}
        self._load_prompts()
        logger.info("提示词管理服务初始化完成")

    def _load_prompts(self):
        """加载所有提示词"""
        try:
            # 从文件加载提示词
            prompt_files = list(self.prompts_dir.glob("*.json"))
            for file_path in prompt_files:
                if file_path.name.startswith("prompt_"):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            # 处理时间字段
                            if isinstance(data.get("created_at"), str):
                                data["created_at"] = datetime.fromisoformat(data["created_at"])
                            if isinstance(data.get("updated_at"), str):
                                data["updated_at"] = datetime.fromisoformat(data["updated_at"])
                            prompt = PromptTemplate(**data)
                            self.prompts[prompt.id] = prompt
                    except Exception as e:
                        logger.error(f"加载提示词失败 {file_path}: {e}")

            # 检查是否需要创建默认提示词
            self._ensure_default_prompts()

            logger.info(f"加载了 {len(self.prompts)} 个提示词")
        except Exception as e:
            logger.error(f"加载提示词失败: {e}")
            self._create_default_prompts()

    def _ensure_default_prompts(self):
        """确保所有任务类型都有默认提示词"""
        required_task_types = [TaskType.ACTOR_RELATION, TaskType.ROLE_FRAMING,
                              TaskType.PROBLEM_SCOPE, TaskType.CAUSAL_MECHANISM]

        missing_defaults = []
        for task_type in required_task_types:
            # 检查是否存在该任务类型的默认提示词
            existing_defaults = [p for p in self.prompts.values()
                               if p.task_type == task_type and p.is_default]

            if not existing_defaults:
                missing_defaults.append(task_type)

        if missing_defaults:
            logger.info(f"缺少默认提示词: {missing_defaults}，将创建")
            self._create_default_prompts()

    def _create_default_prompts(self):
        """创建默认提示词"""
        default_prompts = [
            {
                "task_type": TaskType.ACTOR_RELATION,
                "name": "默认行为者关系分析",
                "description": "分析政策文档中的行为者与关系",
                "template": """请分析以下政策文档中的行为者与关系。

文档内容：
{document}

请执行以下分析任务：
1. 识别文档中的主要行为者（人物、组织、机构等）
2. 分析行为者之间的关系和互动
3. 提取行为者的主要行动和立场
4. 总结关键发现

请以严格的JSON格式返回分析结果，结构如下（不要包含注释）：
{
    "actors": [
        {
            "name": "行为者名称",
            "type": "行为者类型",
            "description": "行为者描述",
            "actions": ["行为者的主要行动"],
            "stance": "行为者的立场或态度"
        }
    ],
    "relations": [
        {
            "source": "行为者1名称",
            "target": "行为者2名称",
            "type": "关系类型",
            "description": "关系描述"
        }
    ],
    "key_findings": ["关键发现"]
}

重要：只返回纯JSON格式的分析结果，不要包含任何额外的解释、注释或说明文字。""",
                "tags": ["默认", "行为者", "关系", "系统"]
            },
            {
                "task_type": TaskType.ROLE_FRAMING,
                "name": "默认角色塑造分析",
                "description": "分析政策文档中的角色塑造和身份建构",
                "template": """请分析以下政策文档中的角色塑造和身份建构。

文档内容：
{document}

请执行以下分析任务：
1. 识别文档中被塑造的角色和身份
2. 分析角色的特征和属性描述
3. 分析角色之间的对比和关系
4. 总结关键发现

请以严格的JSON格式返回分析结果，结构如下（不要包含注释）：
{
    "roles": [
        {
            "name": "角色名称",
            "type": "角色类型",
            "characteristics": ["特征1", "特征2"],
            "framing_strategy": "塑造策略",
            "evidence": ["支持证据1", "支持证据2"]
        }
    ],
    "identity_construction": [
        {
            "identity": "身份类型",
            "construction_method": "建构方法",
            "purpose": "建构目的",
            "examples": ["具体例子1", "具体例子2"]
        }
    ],
    "role_contrasts": [
        {
            "positive_role": "正面角色",
            "negative_role": "负面角色",
            "contrast_dimension": "对比维度",
            "description": "对比描述"
        }
    ],
    "key_findings": ["关键发现"]
}

重要：只返回纯JSON格式的分析结果，不要包含任何额外的解释、注释或说明文字。""",
                "tags": ["默认", "角色", "身份", "系统"]
            },
            {
                "task_type": TaskType.PROBLEM_SCOPE,
                "name": "默认问题范围分析",
                "description": "分析政策文档中的问题定义和范围界定",
                "template": """请分析以下政策文档中的问题定义和范围界定。

文档内容：
{document}

请执行以下分析任务：
1. 识别文档中定义的主要问题
2. 分析问题的范围和边界
3. 分析问题的严重程度和紧迫性
4. 总结关键发现

请以严格的JSON格式返回分析结果，结构如下（不要包含注释）：
{
    "problems": [
        {
            "name": "问题名称",
            "definition": "问题定义",
            "scope": "问题范围",
            "severity": "严重程度",
            "urgency": "紧迫性",
            "affected_groups": ["受影响群体1", "受影响群体2"]
        }
    ],
    "scope_boundaries": [
        {
            "dimension": "界定维度",
            "included": ["包含内容1", "包含内容2"],
            "excluded": ["排除内容1", "排除内容2"],
            "rationale": "界定理由"
        }
    ],
    "problem_hierarchy": [
        {
            "main_problem": "主要问题",
            "sub_problems": ["子问题1", "子问题2"],
            "relationships": ["关系描述1", "关系描述2"]
        }
    ],
    "key_findings": ["关键发现"]
}

重要：只返回纯JSON格式的分析结果，不要包含任何额外的解释、注释或说明文字。""",
                "tags": ["默认", "问题", "范围", "系统"]
            },
            {
                "task_type": TaskType.CAUSAL_MECHANISM,
                "name": "默认因果机制分析",
                "description": "分析政策文档中的因果关系和归因模式",
                "template": """请分析以下政策文档中的因果关系和归因模式。

文档内容：
{document}

请执行以下分析任务：
1. 识别文档中描述的因果链（事件A导致事件B导致事件C...）
2. 分析归因模式（问题被归因于什么因素）
3. 分析责任框架（谁被认为应对问题负责）
4. 总结关键发现

请以严格的JSON格式返回分析结果，结构如下（不要包含注释）：
{
    "causal_chains": [
        {
            "sequence": ["原因1", "结果1/原因2", "结果2"],
            "description": "因果链描述"
        }
    ],
    "attribution_patterns": [
        {
            "target": "归因对象",
            "factors": ["归因因素1", "归因因素2"],
            "evidence": ["支持证据1", "支持证据2"]
        }
    ],
    "responsibility_framing": {
        "responsible_actors": ["负责任的行为者1", "负责任的行为者2"],
        "absolved_actors": ["被免责的行为者1", "被免责的行为者2"],
        "framing_strategy": "责任框架策略描述"
    },
    "key_findings": ["关键发现"]
}

重要：只返回纯JSON格式的分析结果，不要包含任何额外的解释、注释或说明文字。""",
                "tags": ["默认", "因果", "归因", "系统"]
            }
        ]

        for prompt_data in default_prompts:
            # 检查是否已存在相同任务类型的默认提示词
            existing_defaults = [p for p in self.prompts.values()
                               if p.task_type == prompt_data["task_type"] and p.is_default]

            if not existing_defaults:
                prompt = self.create_prompt(PromptTemplateCreate(**prompt_data))
                prompt.is_default = True  # 标记为默认提示词
                self._save_prompt(prompt)
                logger.info(f"创建默认提示词: {prompt_data['name']}")

    def create_prompt(self, prompt_create: PromptTemplateCreate) -> PromptTemplate:
        """创建新的提示词模板"""
        prompt_id = str(uuid.uuid4())
        version = "1.0.0"
        
        prompt = PromptTemplate(
            id=prompt_id,
            task_type=prompt_create.task_type,
            name=prompt_create.name,
            description=prompt_create.description,
            template=prompt_create.template,
            version=version,
            is_active=True,
            is_default=False,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            created_by="user",
            tags=prompt_create.tags
        )
        
        self.prompts[prompt_id] = prompt
        self._save_prompt(prompt)
        
        logger.info(f"创建提示词成功: {prompt_id}")
        return prompt

    def update_prompt(self, prompt_id: str, prompt_update: PromptTemplateUpdate) -> Optional[PromptTemplate]:
        """更新提示词模板"""
        if prompt_id not in self.prompts:
            return None
            
        prompt = self.prompts[prompt_id]
        
        # 保存历史版本
        self._save_prompt_history(prompt)
        
        # 更新版本号
        old_version = prompt.version
        version_parts = old_version.split('.')
        version_parts[-1] = str(int(version_parts[-1]) + 1)
        new_version = '.'.join(version_parts)
        
        # 更新字段
        if prompt_update.name is not None:
            prompt.name = prompt_update.name
        if prompt_update.description is not None:
            prompt.description = prompt_update.description
        if prompt_update.template is not None:
            prompt.template = prompt_update.template
        if prompt_update.tags is not None:
            prompt.tags = prompt_update.tags
        if prompt_update.is_active is not None:
            prompt.is_active = prompt_update.is_active
            
        prompt.version = new_version
        prompt.updated_at = datetime.now()
        
        self._save_prompt(prompt)
        
        logger.info(f"更新提示词成功: {prompt_id} {old_version} -> {new_version}")
        return prompt

    def get_prompt(self, prompt_id: str) -> Optional[PromptTemplate]:
        """获取提示词"""
        return self.prompts.get(prompt_id)

    def get_prompts_by_task_type(self, task_type: TaskType) -> List[PromptTemplate]:
        """根据任务类型获取提示词"""
        return [p for p in self.prompts.values() if p.task_type == task_type]

    def get_active_prompts(self) -> List[PromptTemplate]:
        """获取所有激活的提示词"""
        return [p for p in self.prompts.values() if p.is_active]

    def delete_prompt(self, prompt_id: str) -> bool:
        """删除提示词"""
        if prompt_id not in self.prompts:
            return False
            
        prompt = self.prompts[prompt_id]
        prompt.is_active = False
        prompt.updated_at = datetime.now()
        
        self._save_prompt(prompt)
        logger.info(f"删除提示词成功: {prompt_id}")
        return True

    def _save_prompt(self, prompt: PromptTemplate):
        """保存提示词到文件"""
        file_path = self.prompts_dir / f"prompt_{prompt.id}.json"
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(prompt.dict(), f, ensure_ascii=False, indent=2, default=str)

    def _save_prompt_history(self, prompt: PromptTemplate):
        """保存提示词历史版本"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = self.history_dir / f"prompt_{prompt.id}_v{prompt.version}_{timestamp}.json"
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(prompt.dict(), f, ensure_ascii=False, indent=2, default=str)

    async def compare_prompts(self, request: PromptComparisonRequest) -> PromptComparison:
        """比较两个提示词"""
        prompt1 = self.get_prompt(request.prompt1_id)
        prompt2 = self.get_prompt(request.prompt2_id)
        
        if not prompt1 or not prompt2:
            raise ValueError("提示词不存在")
        
        # 文本比较
        differences = self._analyze_differences(prompt1.template, prompt2.template)
        
        # 结构比较
        structure_diff = self._compare_structure(prompt1.template, prompt2.template)
        
        # 测试比较
        test_results = await self._test_prompts_comparison(
            prompt1, prompt2, request.test_document
        )
        
        # 计算相似度
        similarity_score = self._calculate_similarity(prompt1.template, prompt2.template)
        
        # 生成推荐
        recommendation = self._generate_recommendation(
            prompt1, prompt2, differences, test_results, similarity_score
        )
        
        comparison = PromptComparison(
            prompt1_id=request.prompt1_id,
            prompt2_id=request.prompt2_id,
            comparison_criteria=request.comparison_criteria,
            differences={
                "text_differences": differences,
                "structure_differences": structure_diff,
                "test_results": test_results
            },
            similarity_score=similarity_score,
            recommendation=recommendation,
            compared_at=datetime.now()
        )
        
        return comparison

    def _analyze_differences(self, text1: str, text2: str) -> Dict[str, Any]:
        """分析文本差异"""
        diff = list(difflib.unified_diff(
            text1.splitlines(keepends=True),
            text2.splitlines(keepends=True),
            fromfile="提示词1",
            tofile="提示词2"
        ))
        
        # 统计差异
        additions = len([line for line in diff if line.startswith('+')])
        deletions = len([line for line in diff if line.startswith('-')])
        
        return {
            "has_differences": len(diff) > 0,
            "additions": additions,
            "deletions": deletions,
            "total_changes": additions + deletions,
            "diff_preview": ''.join(diff[:20]) + ('...' if len(diff) > 20 else '')
        }

    def _compare_structure(self, template1: str, template2: str) -> Dict[str, Any]:
        """比较模板结构"""
        # 提取JSON结构
        json_pattern = r'\{[^}]*\}'
        json_blocks1 = re.findall(json_pattern, template1, re.DOTALL)
        json_blocks2 = re.findall(json_pattern, template2, re.DOTALL)
        
        return {
            "json_blocks_count_1": len(json_blocks1),
            "json_blocks_count_2": len(json_blocks2),
            "structure_similarity": len(set(json_blocks1) & set(json_blocks2)) / max(len(set(json_blocks1) | set(json_blocks2)), 1)
        }

    async def _test_prompts_comparison(self, prompt1: PromptTemplate, prompt2: PromptTemplate, test_document: str) -> Dict[str, Any]:
        """测试提示词比较"""
        results = {}
        
        for i, prompt in enumerate([prompt1, prompt2], 1):
            try:
                start_time = datetime.now()
                
                # 使用安全的字符串替换
                formatted_prompt = prompt.template.replace("{document}", test_document)
                
                response = await self.zhipu_client.analyze_document(
                    text=test_document,
                    task_type=prompt.task_type.value,
                    prompt_template=formatted_prompt
                )
                
                end_time = datetime.now()
                response_time = (end_time - start_time).total_seconds()
                
                # 分析结果质量
                result_quality = self._evaluate_result_quality(response)
                
                results[f"prompt{i}"] = {
                    "success": response.get("success", False),
                    "response_time": response_time,
                    "result_quality": result_quality,
                    "error": response.get("error")
                }
                
            except Exception as e:
                results[f"prompt{i}"] = {
                    "success": False,
                    "error": str(e),
                    "response_time": 0,
                    "result_quality": 0
                }
        
        return results

    def _evaluate_result_quality(self, response: Dict[str, Any]) -> float:
        """评估结果质量"""
        if not response.get("success", False):
            return 0.0
            
        result = response.get("result", {})
        
        # 检查结果完整性
        quality_score = 0.0
        
        # 基础分数：成功响应
        quality_score += 0.3
        
        # 结构完整性
        if isinstance(result, dict) and len(result) > 0:
            quality_score += 0.3
            
        # 内容丰富度
        if len(str(result)) > 100:
            quality_score += 0.2
            
        # 格式正确性
        try:
            json.dumps(result)
            quality_score += 0.2
        except:
            pass
            
        return min(quality_score, 1.0)

    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度"""
        # 使用简单的序列匹配
        matcher = difflib.SequenceMatcher(None, text1, text2)
        return matcher.ratio()

    def _generate_recommendation(self, prompt1: PromptTemplate, prompt2: PromptTemplate, 
                               differences: Dict[str, Any], test_results: Dict[str, Any], 
                               similarity_score: float) -> str:
        """生成推荐建议"""
        recommendations = []
        
        # 基于测试结果
        if test_results.get("prompt1", {}).get("success", False) and test_results.get("prompt2", {}).get("success", False):
            time1 = test_results["prompt1"]["response_time"]
            time2 = test_results["prompt2"]["response_time"]
            quality1 = test_results["prompt1"]["result_quality"]
            quality2 = test_results["prompt2"]["result_quality"]
            
            if quality1 > quality2:
                recommendations.append(f"提示词1的质量评分更高({quality1:.2f} vs {quality2:.2f})")
            elif quality2 > quality1:
                recommendations.append(f"提示词2的质量评分更高({quality2:.2f} vs {quality1:.2f})")
                
            if time1 < time2:
                recommendations.append(f"提示词1的响应速度更快({time1:.2f}s vs {time2:.2f}s)")
            elif time2 < time1:
                recommendations.append(f"提示词2的响应速度更快({time2:.2f}s vs {time1:.2f}s)")
        
        # 基于相似度
        if similarity_score > 0.9:
            recommendations.append("两个提示词非常相似，建议合并或选择一个更清晰的版本")
        elif similarity_score < 0.3:
            recommendations.append("两个提示词差异很大，建议分别测试效果")
            
        # 基于文本差异
        if differences["text_differences"]["total_changes"] > 50:
            recommendations.append("提示词文本差异较大，建议仔细检查修改的影响")
            
        return "; ".join(recommendations) if recommendations else "两个提示词表现相似，建议进一步测试"

    async def test_prompt(self, prompt_id: str, test_document: str, document_id: str) -> PromptTestResult:
        """测试单个提示词"""
        prompt = self.get_prompt(prompt_id)
        if not prompt:
            raise ValueError("提示词不存在")
        
        try:
            start_time = datetime.now()
            
            # 使用安全的字符串替换
            formatted_prompt = prompt.template.replace("{document}", test_document)
            
            response = await self.zhipu_client.analyze_document(
                text=test_document,
                task_type=prompt.task_type.value,
                prompt_template=formatted_prompt
            )
            
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds()
            
            # 评估结果
            result_quality = self._evaluate_result_quality(response)
            json_validity = self._check_json_validity(response)
            completeness_score = self._calculate_completeness(response)
            
            test_result = PromptTestResult(
                prompt_id=prompt_id,
                document_id=document_id,
                response_time=response_time,
                result_quality=result_quality,
                json_validity=json_validity,
                completeness_score=completeness_score,
                error_message=response.get("error") if not response.get("success") else None,
                tested_at=datetime.now()
            )
            
            # 保存测试结果
            self._save_test_result(test_result)
            
            return test_result
            
        except Exception as e:
            test_result = PromptTestResult(
                prompt_id=prompt_id,
                document_id=document_id,
                response_time=0,
                result_quality=0,
                json_validity=False,
                completeness_score=0,
                error_message=str(e),
                tested_at=datetime.now()
            )
            
            self._save_test_result(test_result)
            return test_result

    def _check_json_validity(self, response: Dict[str, Any]) -> bool:
        """检查JSON格式有效性"""
        try:
            result = response.get("result", {})
            json.dumps(result)
            return True
        except:
            return False

    def _calculate_completeness(self, response: Dict[str, Any]) -> float:
        """计算完整性评分"""
        if not response.get("success", False):
            return 0.0
            
        result = response.get("result", {})
        if not isinstance(result, dict):
            return 0.0
            
        # 检查必填字段
        required_fields = ["key_findings"]
        completeness = 0.0
        
        for field in required_fields:
            if field in result and result[field]:
                completeness += 0.5
                
        return min(completeness, 1.0)

    def _save_test_result(self, test_result: PromptTestResult):
        """保存测试结果"""
        timestamp = test_result.tested_at.strftime("%Y%m%d_%H%M%S")
        file_path = self.test_results_dir / f"test_{test_result.prompt_id}_{timestamp}.json"
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(test_result.dict(), f, ensure_ascii=False, indent=2, default=str)