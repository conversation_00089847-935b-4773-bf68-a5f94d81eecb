{"doc_id": "81889923-d444-4bd8-9373-76d72407b1ad", "title": "item254_US_<PERSON>, <PERSON>.D., Senior Fellow, Center for Strategic and International Studies", "text": "  STATEMENT OF <PERSON><PERSON><PERSON><PERSON><PERSON>, PhD, SENIOR FELLOW, CENTER FOR \r\n  STRATEGIC AND INTERNATIONAL STUDIES, AND PROFESSOR, MARINE \r\n        CORPS UNIVERSITY, SCHOOL OF ADVANCED WARFIGHTING\r\n\r\n    <PERSON><PERSON> <PERSON><PERSON> straw, Senator.\r\n    Chairman <PERSON>, Vice Chairman <PERSON><PERSON><PERSON>, distinguished Members \r\nof the Committee, I really am honored today to sit with you and \r\nshare my thoughts on what I think you all agree, from reading \r\nall the work that you've done on it, is probably the most \r\nimportant question facing our Nation from a technological \r\nperspective.\r\n    The magnitude of the moment is clear, right? Both the \r\nSenate and the House are very much cultivating a national \r\ndialogue, and I just want to open as a citizen by thanking you \r\nfor that. You have a powerful role in that, and so doing this \r\nhere right now is key. And so, I have to be very blunt and \r\nclear with you that I'm going to talk to you less about the \r\nthreat outside, Sir. I'm going to talk to you more about how I \r\nthink we could get it wrong.\r\n    Today, as part of that ongoing dialogue. I really want to \r\nlook at the often-invisible center of gravity behind any \r\ntechnology: people, bureaucracy, and data, and in particular, \r\ndata infrastructure and architecture. Put simply, you get the \r\nright people in place, with permissive policies and \r\ncomputational power at scale, and you gain a position of \r\nadvantage in modern competition. I'll just put it bluntly. In \r\nthe twenty-first century, the general or spy who doesn't have a \r\nmodel by their side is basically as helpless as a blind man in \r\na bar fight.\r\n    So, let's start with people. Imagine a future analyst \r\nworking alongside a generative AI model to monitor enemy cyber \r\ncapabilities. The model shows the analyst signs of new \r\nadversary malware in targeting U.S. critical infrastructure. \r\nThe analyst disagrees. The challenge we have is today our \r\nanalysts can't explain why they disagree because they haven't \r\nbeen trained in basic data science and statistics. They don't \r\nknow how to balance causal inference and decades of thinking \r\nabout human judgment and intuition. And sadly, I'll be honest, \r\nour modern analytical tradecraft and even something close to \r\nme, professional military education, tends to focus on discrete \r\ncase studies more than statistical patterns or trend analysis. \r\nIn other words, if we unleash a new suite of machine learning \r\ncapabilities without the requisite workforce to understand how \r\nto use them, we're throwing good money after bad. And we really \r\nhave to be careful about this. I can't stress this enough. If \r\nyou don't actually make sure that people understand how to use \r\nthe technology, it's just a magic box.\r\n    Let's turn to the bureaucracy.\r\n    Now, I want you to imagine what the Cuban Missile Crisis \r\nwould look like in 2030: all sides with a wide range of machine \r\nlearning capabilities. There would be an utter tension as \r\nmachines wanted to speed up decision-making in the crisis. But \r\nsenior decision-makers needed to slow it down to the pace of \r\ninteragency collaboration. Even worse, you would be overwhelmed \r\nby deep fakes and computational propaganda pressuring you as \r\nelected officials and any senior leader to act. And pressure to \r\nact at a moment of crisis doesn't necessarily lead for sound \r\ndecision-making. Unfortunately, neither our modern national \r\nsecurity enterprise nor the bureaucracy surrounding government \r\ninnovation/experimentation are ready for this world. If the \r\nanalyst and military planner struggles to understand \r\nprediction, inference, and judgment through algorithms, the \r\nchallenge is even more acute with senior decision-makers.\r\n    At this level, most international relations and diplomatic \r\nhistory tells us that the essence of decision is as much \r\nemotion, flawed analogies, and bias as it is rational \r\ninterests. What happens when irrational humans collide with \r\nrational algorithms during a crisis? Confusion could easily \r\neclipse certainty, unleashing escalation and chaos.\r\n    There are even larger challenges associated with creating a \r\nbureaucracy capable of adapting algorithms during a crisis. \r\nBecause of complexity and uncertainty, all models require a \r\nconstant stream of data to the moment at hand, not just the \r\nmoment of the past. But crises are different than necessarily \r\nwhat preceded them. Unfortunately, slow adapters will succumb \r\nto quick deaths on that future battlefield. As a result, a \r\nculture of experimentation and constant model refinement will \r\nbe the key to gaining and sustaining relative advantage. Now, \r\nask yourself, do we have that bureaucracy?\r\n    Last, consider data, architecture, and infrastructure, how \r\nwe put the pieces of the puzzle together. I want you to imagine \r\nwe're almost back to the old days of the SCUD hunt, right? \r\nImagine the hunt for a mobile missile launcher in a future \r\ncrisis. A clever adversary, knowing they were being watched, \r\ncould easily poison the data used to support intelligence \r\nanalysis and targeting. They could trick every computer model \r\ninto thinking a school bus was a missile launcher, causing \r\ndecision-makers to quickly lose confidence in otherwise \r\naccurate data. Even when you were right 99 percent of the time, \r\nthe consequences of being wrong once are still adding unique \r\nhuman elements to crisis decision-making. Artificial \r\nintelligence and machine learning, therefore, are only as \r\npowerful as the underlying data. Yet to collect, process, and \r\nstore that data is going to produce significant costs going \r\nforward. This is not going to be cheap. Furthermore, bad \r\nbureaucracy and policy can kill great models if they limit the \r\nflow of data.\r\n    Last, let's talk about the fact that Prometheus has already \r\nshared the fire, and I think you all know that even from your \r\nopening comments, Chairman. Adversaries now into the \r\nforeseeable future can attack us at machine speed through a \r\nconstant barrage of cyber operations and more disconcerting, \r\nmis-, dis- and mal-information, alongside entirely new forms of \r\nswarming attacks that could hold not just our military, but our \r\ncivilian population at risk. Unless the United States is able \r\nto get the right mix of people, bureaucratic reform, and data \r\ninfrastructure in place, those attacks could test the very \r\nfoundation of our Republic.\r\n    Now, I'm an optimist, so I'm going to be honest with you. \r\nI'm confident the United States can get it right. In fact, the \r\nfuture is ours to lose. Authoritarian regimes are subject to \r\ncontradictions that make them rigid, brittle, and closed to new \r\ninformation. Look no further than regulations about adherence \r\nto socialist thought in data sets. These regimes are afraid to \r\nhave the type of open, honest dialogue this Committee is \r\npromoting. And that fear is our opportunity.\r\n    Thank you for the opportunity to testify.\r\n    [The prepared statement of the witness follows:]\r\n    [GRAPHICS NOT AVAILABLE IN TIFF FORMAT]\r\n    \r\n   STATEMENT OF YANN LeCUN, PhD, VICE PRESIDENT AND CHIEF AI \r\n  SCIENTIST, META PLATFORMS, AND SILVER PROFESSOR OF COMPUTER \r\n         SCIENCE AND DATA SCIENCE, NEW YORK UNIVERSITY\r\n\r\n    Dr. LeCun. Chairman Warner, Vice Chairman Rubio, and \r\ndistinguished Members of the Committee. Thank you for the \r\nopportunity to appear before you today to discuss important \r\nissues regarding AI.\r\n    My name is Yann LeCun. I'm currently the Silver Professor \r\nof Computer Science and Data Science at New York University. \r\nI'm also Meta's Chief AI Scientist and co-founder of Meta's \r\nFundamental AI Research Lab. At Meta, I focus on AI research, \r\ndevelopment strategy, and scientific leadership.\r\n    AI has progressed leaps and bounds since I began my \r\nresearch career in the 1980s. Today, we are witnessing the \r\ndevelopment of generative AI, and in particular, large language \r\nmodels. These systems are trained through self-supervised \r\nlearning. Or more simply, they are trained to fill in the \r\nblanks. In the process of doing so, those AI models learn to \r\nrepresent text or images--including the meaning, style, and \r\nsyntax--in multiple languages. The internal representation can \r\nthen be applied to downstream tasks such as translation, topic \r\nclassification, et cetera. It can also be used to predict the \r\nnext words in a text, which allow LLMs to answer questions or \r\nwrite essays, and write code as well. It is important not to \r\nundervalue the far-reaching potential opportunities they \r\npresent. The development of AI is as foundational as the \r\ncreation of the microprocessor, the personal computer, the \r\nInternet, and the mobile device. Like all foundational \r\ntechnologies, there will be a multitude of uses of AI. And like \r\nevery technology, AI will be used by people for good and bad \r\nends.\r\n    As AI systems continue to develop, I'd like to highlight \r\ntwo defining issues. The first one is safety, and the second \r\none is access. One way to start to address both of these issues \r\nis through the open sharing of current technology and \r\nscientific information. The free exchange of scientific papers, \r\ncode, and trained models in the case of AI has enabled American \r\nleadership in science and technology. This concept is not new. \r\nIt started a long time ago. Open sourcing technology has \r\nspurred rapid progress in systems we now consider basic \r\ninfrastructure, such as the Internet and mobile communication \r\nnetworks.\r\n    This doesn't mean that every model can or should be open. \r\nThere is a role for both proprietary and open-source AI models. \r\nBut an open-source basic model should be the foundation on \r\nwhich industry can build a vibrant ecosystem. An open-source \r\nmodel creates an industry standard, much like the model of the \r\nInternet in the mid '90s. Through this collaborative effort, AI \r\ntechnology will progress faster, more reliably, and more \r\nsecurely.\r\n    Open sourcing also gives businesses and researchers access \r\nto tools that they could not otherwise build by themselves, \r\nwhich helps create a vast social and economic set of \r\nopportunities. In other words, open sourcing democratizes \r\naccess. It gives more people and businesses the power to build \r\nupon state-of-the-art technology and to remedy potential \r\nweaknesses. This also helps promote democratic values and \r\ninstitutions, minimize social disparities, and improve \r\ncompetition. We want to ensure that the United States and \r\nAmerican companies, together with other democracies, lead in AI \r\ndevelopment ahead of our adversaries, so that the foundational \r\nmodels are developed here and represent and share our values. \r\nBy open sourcing current AI tools, we can develop our research \r\nand development ecosystem faster than our adversary.\r\n    As AI technology progresses, there is an urgent need for \r\ngovernments to work together, especially democracies, to set \r\ncommon AI standards and governance models. This is another \r\nvaluable area where we welcome working with regulators to set \r\nappropriate transparency requirements, red teaming standards, \r\nand safety mitigations to help ensure those codes of practice, \r\nstandards, and guardrails are consistent across the world. The \r\nWhite House's voluntary commitment is a critical step in \r\nensuring responsible guardrails, and they create a model for \r\nother governments to follow. Continued U.S. leadership by \r\nCongress and the White House is important in ensuring that \r\nsociety can benefit from innovation in AI while striking the \r\nright balance with protecting rights and freedom, preserving \r\nnational security interests, and mitigating risks where those \r\narise.\r\n    I'd like to close by thanking Chairman Warner, Vice \r\nChairman Rubio, and the other Members of the Committee for your \r\nleadership. At the end of the day, our job is to work \r\ncollaboratively with you, with Congress, with other nations, \r\nand with other companies in order to drive innovation and \r\nprogress in a manner that is safe and secure and consistent \r\nwith our national security interests.\r\n    Thank you. I look forward to your questions.\r\n    [The prepared statement of the witness follows:]\r\n    [GRAPHICS NOT AVAILABLE IN TIFF FORMAT]\r\n    \r\n    STATEMENT OF JEFFREY DING, PhD, ASSISTANT PROFESSOR OF \r\n        POLITICAL SCIENCE, GEORGE WASHINGTON UNIVERSITY\r\n\r\n    Dr. Ding. Chairman Warner, Vice Chairman Rubio, and Members \r\nof the Committee. I am honored by the opportunity to brief this \r\nCommittee on the National Security Implications of AI. In all \r\nhonesty, I also have a selfish reason for attending today. I \r\nteach political science at GW, and my students all really look \r\nup to the Committee Members in this room and also all the staff \r\nwho are working behind the scenes to put this hearing together. \r\nSo, when I got to tell the class this morning that I was doing \r\nthis testimony, they all got the most excited I've ever seen \r\nthem get excited this semester. And so, hopefully, that will \r\ncause them to do more of the required readings in class. In all \r\nseriousness, I have great students, I'm very grateful to be \r\nhere.\r\n    Today, in my opening remarks, I want to make three main \r\npoints from my written testimony. The first is when it comes to \r\nthe national security of implications of AI, the main driver \r\nand the main vector is which country will be able to sustain \r\nproductivity growth at higher levels than their rivals. And for \r\nthis vector, the distinction between innovation capacity and \r\ndiffusion capacity is central to thinking about technological \r\nleadership in AI. Today, when various groups--whether that be \r\nexperts, policymakers, the Intelligence Community--when they \r\ntry to assess technological leadership, they are overly \r\npreoccupied with innovation capacity. Which state is going to \r\nbe the first to generate new-to-the-world breakthroughs, the \r\nfirst to generate that next leap in large language models. They \r\nneglect diffusion capacity. A state's ability to spread and \r\nadopt innovations after their initial introduction across \r\nproductive processes.\r\n    And that process of diffusion throughout the entire economy \r\nis really important for technologies like AI. If we were \r\ntalking about a sector like automobiles, or even a sector like \r\nclean energy, we might not be talking as much about the effects \r\nof spreading technologies across all different productive \r\nprocesses throughout the entire economy. AI is a general-\r\npurpose technology, like electricity, like the computer, like \r\nmy fellow panelists just mentioned in his testimony. And \r\ngeneral-purpose technologies historically precede waves of \r\nproductivity growth because they can have pervasive effects \r\nthroughout the entire economy. So, the U.S. in the late 19th \r\ncentury became the leading economic power before it translated \r\nthat influence into military and geopolitical leadership, \r\nbecause it was better at adopting general purpose technologies \r\nat scale, like electricity, like the American system of \r\ninterchangeable manufacture, at a better and a more effective \r\nrate than its rivals.\r\n    Point number two is when we assess China's technological \r\nleadership and use this framework of innovation capacity versus \r\ndiffusion capacity, my research finds that China faces a \r\ndiffusion deficit. Its ability to diffuse innovations like AI \r\nacross the entire economy lags far behind its ability to \r\npioneer initial innovations or make fundamental breakthroughs.\r\n    And so, when you've heard from other people in the past or \r\nin the briefing memos you are reading, you are probably getting \r\na lot of innovation-centric indicators of China's scientific \r\nand technological prowess: its advances in R&D spending, \r\nheadline numbers on patents and publications. In my research, \r\nI've presented evidence about China's diffusion deficit by \r\nlooking at how is China actually adopting other information and \r\ncommunications technologies at scale? What are its adoption \r\nrates in cloud computing, industrial software, related \r\ntechnologies that would all be in a similar category to AI? And \r\nthose rates lag far behind the U.S.\r\n    Another indicator would be how is China's ability to widen \r\nthe pool of average AI engineers? I'm not talking about Nobel \r\nPrize of computing winners like my fellow panelists here, but \r\njust average AI engineers who can take existing models and \r\nadapt them in particular sectors or industries or specific \r\napplications. And based on my data, China has only 29 \r\nuniversities that meet a baseline quality metric for AI \r\nengineering, whereas the U.S. has 159. So, there's a large gap \r\nin terms of China's diffusion capacity compared to its \r\ninnovation capacity in AI.\r\n    I'll close with the third point, which is some recent \r\ntrends in Chinese labs' large language models. China has built \r\nlarge language models similar to OpenAI's ChatGPT, as well as \r\nOpenAI's text-to-image models like DALL-E. But there's still a \r\nlarge gap in terms of Chinese performance on these models. And, \r\nin fact, on benchmarks and leaderboards where U.S. models are \r\ncompared to Chinese models on Chinese language prompts, models \r\nlike ChatGPT still perform better than Chinese counterparts. \r\nSome of these bottlenecks relate to a reliance on Western \r\ncompanies to open up new paradigms, China's censorship regime, \r\nwhich Dr. Jensen talked about, and computing power bottlenecks, \r\nwhich I'm happy to expand on further.\r\n    I'll close by saying I submitted three specific policy \r\nrecommendations to the Committee. But I want to emphasize one, \r\nwhich is keep calm and avoid overhyping China's AI \r\ncapabilities. In the paper that forms the basis for this \r\ntestimony, I called attention to a 1969 CIA assessment of the \r\nSoviet Union's technological capabilities. It was remarkable \r\nbecause it went against the dominant narrative of the time of a \r\nSoviet Union close to overtaking the U.S. in technological \r\nleadership. The report concluded that the technological gap was \r\nactually widening between the U.S. as the leader and the Soviet \r\nUnion because of the U.S.'s superior mechanisms to spread \r\ntechnologies and diffuse technologies. Fifty years later, we \r\nknow why this assessment was right, and we know we have to \r\nfocus on diffusion capacity when it comes to scientific and \r\ntechnological leadership.\r\n    Thanks for your time.", "metadata": {"original_filename": "item254_US_<PERSON>, <PERSON>.<PERSON>., Senior Fellow, Center for Strategic and International Studies.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:51:56.230680", "updated_at": "2025-08-28T21:51:56.230680", "word_count": 18417}