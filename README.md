# 文档分析系统

基于智谱AI API的智能文档分析系统，专注于政策文档的深度分析和叙事结构识别。

## 功能特性

### 核心分析任务
1. **行为者与关系提取** - 识别文档中的行为者及其相互关系
2. **角色塑造检测** - 检测英雄/受害者/反派的角色塑造模式
3. **问题范围策略检测** - 识别问题扩大化或缩小化策略
4. **因果机制检测** - 分析问题的因果归因方式

### 技术特性
- 🚀 基于FastAPI的高性能API
- 🤖 集成智谱AI大语言模型
- 📊 结构化JSON输出
- 🔧 模块化设计，易于扩展
- 🧪 完整的测试覆盖
- 📝 详细的API文档

## 快速开始

### 环境要求
- Python 3.8+
- 智谱AI API密钥

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd document-analysis-zhipu
```

2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，添加你的智谱AI API密钥
```

5. 启动服务
```bash
python -m uvicorn src.main:app --reload
```

### 使用示例

```python
import requests

# 分析文档
response = requests.post(
    "http://localhost:8000/analyze",
    json={
        "doc_id": "doc_001",
        "title": "AI政策分析",
        "text": "文档内容...",
        "tasks": ["task1", "task2", "task3", "task4"]
    }
)
result = response.json()
```

## API文档

启动服务后，访问 `http://localhost:8000/docs` 查看完整的API文档。

### 主要接口

- `POST /analyze` - 分析文档
- `GET /results/{doc_id}` - 获取分析结果
- `POST /batch-analyze` - 批量分析
- `GET /health` - 健康检查

## 项目结构

```
document-analysis-zhipu/
├── src/                    # 源代码
│   ├── api/               # API路由
│   ├── core/              # 核心逻辑
│   ├── models/            # 数据模型
│   ├── services/          # 业务服务
│   └── utils/             # 工具函数
├── config/                # 配置文件
├── prompts/               # 提示词模板
├── tests/                 # 测试文件
├── docs/                  # 文档
├── examples/              # 示例数据
└── requirements.txt       # 依赖包
```

## 开发指南

### 运行测试
```bash
pytest tests/
```

### 代码格式化
```bash
black src/
isort src/
```

### 类型检查
```bash
mypy src/
```

## 配置说明

### 环境变量
- `ZHIPUAI_API_KEY`: 智谱AI API密钥
- `DATABASE_URL`: 数据库连接字符串
- `LOG_LEVEL`: 日志级别
- `MAX_WORKERS`: 最大工作线程数

### 配置文件
详见 `config/` 目录下的配置文件。

## 部署指南

### Docker部署
```bash
docker build -t document-analysis .
docker run -p 8000:8000 document-analysis
```

### 生产环境
建议使用Nginx + Gunicorn部署：

```bash
gunicorn src.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系项目维护者。# document-analysis-zhipu
