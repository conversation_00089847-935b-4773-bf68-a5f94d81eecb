{"doc_id": "6cce5f00-eb07-4271-b453-5b4ed22eea10", "title": "item124_US_Artificial Intelligence Impacts on Copyright Law", "text": "Artificial Intelligence Impacts on Copyright Law\r\n\r\nIs the Training of AI Models on Copyrighted Works Allowed Under U.S. Law and in Other Jurisdictions?\r\nRegulation of AI Training in the United States\r\nMany AI models — including the machine learning (ML) models and the new generation of LLMs, such as the popular ChatGPT, DALL·E, Midjourney, and Stable Diffusion — are trained on millions of available online materials, many of which are protected by copyright.[27] For AI models to work, they need to engage in text data mining (TDM), which is a computational process that allows AI to learn from data using statistical methods, structure the texts that it ingests, and reveal patterns.[28] When AI scrapes, downloads, and processes works, it might be infringing on the right of reproduction protected by copyright law — i.e., that one does not copy the work of another.[29] This right, however, is subject to limitation by the doctrine of fair use.[30]\r\n\r\nFair use allows for copying, distribution, display, or performance without permission based on a four-factor test.[31] The fair use test is complicated, but it essentially asks about the following:\r\n\r\nthe purpose of the use of a copyrighted work (e.g., is it commercial or nonprofit)\r\nthe nature of the work\r\nthe amount and substantiality of what is taken, and whether the use is \"transformative\" (i.e., whether it transforms the prior work into something with a new, different meaning or message)[32]\r\na more economic consideration of what effect the use will have on the potential market for or value of the copyrighted works (e.g., whether the new work will substitute for the original on the market).[33]\r\nAlthough litigation on this issue is pending, it is unclear whether simple legal answers that are broadly applicable can be expected.\r\n\r\nGenerally, if AI copying and processing of works are found to fall under the umbrella of fair use, the technology companies can use the material as they wish; if they do not fall under that umbrella, then permission must be sought and payment made. How the fair use question is answered will affect the future of both technology and creative industries, whether authors and publishers will be able to profit or refuse to allow their works to be used for AI training, and ultimately, how much and what kind of generative AI innovation appears in the United States.\r\n\r\nOne example of fair use that could allow for AI training is described by the principle of non-expressive use. Many scholars agree that TDM — which comes down to AI learning on the \"non-expressive\" elements of works (that is, extracting facts and statistical patterns rather than retaining the original, creative parts of works) — should be considered fair use and allowed under the law.[34] For example, ML (which is an older form of AI) works in the following way: When AI ingests images of beaches, it learns to identify the concept of a beach, and distinguish it from, say, a classroom. Such non-expressive technical elements of works, just like fact or ideas, are not copyrightable and thus cannot be infringed, but their processing is useful for AI to learn about the works it ingests.[35] In other words, the use is deemed transformative, because the photographer of a beach and the AI owner use the photographs for entirely different purposes.[36] This principle of non-expressive use can be seen in several cases predating generative AI. One example is a plagiarism detection tool that copied original works to compare them with new ones.[37] A more important example is searchable databases, such as HathiTrust or Google Books, which allowed for TDM and browsing of snippets.[38] At the same time, it is not entirely clear whether rulings will go the same way for currently pending, generative AI-specific litigation, for several reasons.[39] First, generative AI does not merely analyze training data as information; it is able to produce digital artifacts in the same form as its training data. Second, many generative AI outputs are direct competitors to the works on which AI was trained. Third, generative AI is able to reproduce particular works with a high degree of similarity (for example, fictional characters, such as Mickey Mouse).[40] These issues are addressed further below.\r\n\r\nThe development of the fair use doctrine in the area of generative AI involves not only legal analysis but also policy choices that could affect the shape of the AI industry and thus influence the direction of innovation and the distribution of costs and benefits across groups such as rights owners, technology companies, and users. Some argue that a broad interpretation of fair use is advantageous because that would allow for TDM in such sectors as the life sciences, linguistics, ML, and internet search engines (which rely on TDM heavily), thus supporting innovation and research and, ultimately, benefiting society.[41] Some argue that allowing TDM is crucial for generative AI models to exist because they could not otherwise be trained. Furthermore, some participants in the legal and policy debate claim that licensing of the works on which the AI is trained is an unrealistic proposal, given the size of datasets ingested by AI and the fact that one would need to obtain a license to both the database and the individual works contained in it.[42] Technology industry representatives have also argued that licensing entails a risk of disincentivizing innovation on the one hand and providing an incentive to sue for infringement on the other.[43] In other words, for generative AI to continue developing at a rapid pace, and for innovation and the technology sector to flourish, it might be important to consider TDM fair use.\r\n\r\nNonetheless, there are competing interests, values, and perspectives. Authors' rights advocates emphasize that fairness of a particular use must be decided on the facts of a particular case, claiming that TDM is not presumptively fair.[44] They further argue that, under the existing law, scraping of existing works should not be free, especially when done for commercial purposes.[45] Artists and creative professionals have also voiced concerns about the lack of their \"consent, compensation or control,\" when it comes to AI model training.[46] They argue that a loss of a licensing market is an important fair use consideration. In their view, large, for-profit companies are naturally suited to bear such costs, and licensing markets have already begun developing.[47] Some companies have chosen to enter into licensing agreements with rights holders, others are litigating,[48] and still others have decided to train their AI on the data they already possess.[49] Moreover, data obtained illegally online weigh against a fair use finding. Finally, some argue that the analysis should consider not just the intermediary purpose of training AI but also the ultimate purpose of the creation of new works.[50] The challenge of balancing different fair use considerations makes the standard difficult to apply generally — especially to the novel types of AI, including generative LLMs, as addressed below.\r\n\r\nRegulation of AI Training Abroad\r\nGiven the ongoing global AI race and the transnational nature of digital economy, the development of U.S. copyright policy cannot be considered in isolation. Even where the legal frameworks or objectives differ, it is important to understand the requirements that U.S. companies need to comply with to enter foreign markets; this is why scholars of regulatory competition often speak of the \"Brussels effect,\" highlighting the influence of EU regulations abroad.[51] For example, Japan and Singapore provide TDM exceptions to the general protection of rights holders provided by copyright law.[52] In the United Kingdom, similar ambitions were scrapped on account of potential harm to the creative industries and a decision not to incentivize AI development at all costs, with the law explicitly allowing TDM only for research purposes, although this could be revisited.[53] Similarly, legal frameworks in Latin America and China are still being developed.[54] Scholars have argued that, on the one hand, developers of AI models could choose to locate their investments in the most friendly jurisdiction, such as the United States. On the other hand, they also call attention to the possibility that a greater international convergence of standards (or \"regulatory race to the middle\") is likely to develop, striking a global compromise between different economic interests at play.[55]\r\n\r\nThe recently adopted EU AI Act (together with an earlier Directive) created a framework for dealing with TDM for AI. [56] The EU legislation contains two exceptions that allow TDM.[57] First, research organizations and cultural heritage institutions are free to use reproductions and extractions for the purposes of scientific research, provided they have lawful access to the works.[58] Copyright holders cannot opt out or prevent such practices; nonetheless, they do have the right to apply measures ensuring security and integrity of the networks and databases and to develop codes of practices. Second, when TDM is undertaken for nonresearch or commercial use, owners of copyrighted works can prevent the mining of those works by making an express reservation of right in an appropriate manner.[59] Additionally, the EU AI Act imposes an obligation to implement technologies enabling providers of AI models to honor copyright holders' decisions to opt out and prevent data mining of their work.[60] Companies have already started using opt-out notices pursuant to the EU AI Act,[61] while AI providers have implemented opt-out processes.[62] The opt-out model is widely seen as a rights holder–friendly compromise, especially contrasted with the currently developing shape of fair use in the United States.[63] Others maintain that an opt-in solution should be pursued instead,[64] arguing that opt-outs pose an undue burden on rights holders or are unworkable in practice.[65]\r\n\r\nAlthough the European Union allows for TDM, it also puts an explicit obligation on providers of general-purpose AI models to implement policies complying with the law and any reservation of rights by copyright holders.[66] Importantly, the EU legislation further imposes significant transparency obligations, including the requirement to publish a detailed, comprehensive summary of content used for model training; this summary could include the main data collections or sets that went into training the model, such as large private or public databases or data archives, and a narrative explanation about other data sources used.[67] Compliance with those objectives will be monitored by the EU AI Office,[68] and public authorities will be able to impose fines and orders to withdraw AI models from the European market.[69] Importantly, these obligations apply to all models that are placed on the EU market, regardless of where the training took place; thus, they could apply extraterritorially, including to U.S. companies.[70] While this is widely seen as a victory for rights holders, others argue that it might disincentivize AI models from entering the EU market at all.[71] Importantly, no such requirement exists so far in the United States, although the Generative AI Copyright Disclosure Act proposed by Democratic California Congressman Adam Schiff would impose a series of disclosure requirements.[72] Furthermore, content transparency and provenance concerns are emphasized in an AI Roadmap presented by Democratic New York Senator Chuck Schumer and the Bipartisan Senate AI Working Group and are featured in the newly proposed Content Origin Protection and Integrity from Edited and Deepfaked Media (COPIED) Act of 2024.[73] These concerns have also been emphasized by commentators in public consultations in the United States.[74]\r\n\r\nLLMs Training and Outputs in Light of Copyright Law\r\nFair use is often interpreted by legal commentators as allowing for non-expressive use to train AI models.[75] LLMs, the newest kind of generative AI, pose a distinct problem, however. Such models as ChatGPT, DALL·E, Midjourney, and Stable Diffusion can produce text, images, and music that are indistinguishable from the works on which they are trained.[76] According to some legal scholars, the fact that LLMs create such works could undermine the claim that the use is fair.[77] Rights holders argue that LLMs fall afoul of the fourth fair use factor, effectively competing with the artists' works and publishers' websites on the markets, or outright substituting for the authors' works.[78] Creative industry advocates argue that training of a model should not be considered as the end purpose of TDM; rather, the ultimate purpose is the generation of output that serves the same purpose as the ingested works, which weighs against a finding of fair use.[79] Although some artists compare LLMs to plagiarists or robbers,[80] other stakeholders highlight many social, economic, and consumer benefits that the technology seems to bring in such industries as art, medical research, and autonomous vehicles.[81] Perhaps, as some scholars note, the shape of the doctrine should depend on whether the licensing solutions and data markets are developed sufficiently, justifying rights holders' claims.[82]\r\n\r\nIn addition to the question of whether training AI models on unlicensed works and databases is infringing copyright, there is the related question of whether the outputs of such generative models are infringing.[83] For both of these issues, one of the technical questions concerns how much AI models retain the actual, expressive content of works they were trained on, apparently being able to re-create nearly exact copies of substantial portions of particular works on which they were trained.[84] Seemingly, LLMs do, at least sometimes, \"memorize\" works in their training data, as recent lawsuits allege;[85] in such cases, AI communicates the original expression from the works it was trained on, which is suspect under the fair use framework.[86] There are already several highly publicized cases in which AI seemingly re-created whole articles;[87] images with a painter's signature or watermark;[88] or copyrightable characters, such as Snoopy or Mickey Mouse.[89] Although experts caution that these are rare instances and AI providers are taking steps to prevent them,[90] these cases might nonetheless undermine the claim to fair use if the outputs are substantially similar to the works on which AI was trained.[91] It is not difficult to re-create copyrightable characters if the users provide detailed prompts.[92] Finally, generative AI could also be used to mimic the style of artists, such as singers, illustrators, or writers. This makes the fair use argument less persuasive because the output is closer to substitution for the copyrighted material used in the training data than transformation.[93] At the same time, style has always been difficult for copyright doctrine to address,[94] sometimes being called unprotectable, making many cases of algorithmic reproduction allowable under the law — or, at least, difficult to analyze legally.[95] The USCO recently issued a report concluding that although artistic style is and should remain unprotectable under copyright, \"there may be situations where the use of an artist's own works to train AI systems to produce material imitating their style can support an infringement claim.\"[96]\r\n\r\nIn deciding AI fair use cases, courts might be swayed by a host of legal and policy arguments regarding generative AI model training — the supposed spread of misinformation, perpetuation of biases, and replacing of artists; increasing productivity; enabling new forms of creativity; and accelerating research.[97] Further arguments involve job displacement resulting from AI and questions of antimonopoly policy.[98] Some of these concerns might be too broad for copyright doctrine to address.[99] More generally, if the courts reject fair use for generative AI, they could halt innovation or push it offshore; if they accept fair use, they might divert economic gain from individual creators.[100] Applications of the fair use doctrine in this context are yet to be decided; it is possible that cases could point in divergent directions.\r\n\r\nSummary\r\nCopyright law protects original works of human expression. It does not protect AI-generated works where a human makes little to no creative impact, such as by typing a simple prompt, but it does protect works created with the use or assistance of AI. It is not yet clear how much creative input will be required to render AI-assisted work protectable under copyright. Training of AI models is likely to be deemed legal if the AI model does not retain protectable expression about works. Generative AI, such as LLMs, presents more-complex considerations, leading to a fact-specific inquiry into the source of training data, the purpose of the model, and the effect on the licensing markets, whether existing or potential. These questions will be settled in litigation and might not yield uniform answers initially, though the issue likely will be resolved by either legislation or the Supreme Court. It is unclear whether legislative solutions pursued in other jurisdictions, such as the European Union, will influence domestic U.S. development; they might, however, exert an effect on which policies U.S. companies implement. Finally, if global copyright standards continue to diverge, an expansive doctrine of fair use might allow the United States to remain a leader in international technological competition and attract investment in AI — at the price of domestic rights holders.", "metadata": {"original_filename": "item124_US_Artificial Intelligence Impacts on Copyright Law.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T22:21:33.603479", "updated_at": "2025-08-28T22:21:33.603479", "word_count": 17739}