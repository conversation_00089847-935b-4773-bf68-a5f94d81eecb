{"doc_id": "146ecbf7-423f-437d-9111-532e6066b000", "title": "item096_US_Enabling Principles for AI Governance", "text": "Introduction\r\nThe question of how to govern artificial intelligence (AI) is rightfully top of mind for\r\nU.S. lawmakers and policymakers alike. Strides in the development of high-powered\r\nlarge language models (LLMs) like ChatGPT/GPT-4o, <PERSON>, <PERSON>, and Microsoft\r\nCopilot have demonstrated the potentially transformative impact that AI could have on\r\nsociety, replete with opportunities and risks. At the same time, international partners in\r\nEurope and competitors like China are taking their own steps toward AI governance.1\r\nIn\r\nthe United States and abroad, public analyses and speculation about AI’s potential\r\nimpact generally lie along a spectrum ranging from utopian at one end—AI as\r\nenormously beneficial for society—to dystopian on the other—an existential risk that\r\ncould lead to the end of humanity—and many nuanced positions in between.\r\nLLMs grabbed public attention in 2023 and sparked concern about AI risks, but other\r\nmodels and applications, such as prediction models, natural language processing\r\n(NLP) tools, and autonomous navigation systems, could also lead to myriad harms and\r\nbenefits today. Challenges include discriminatory model outputs based on bad or\r\nskewed input data, risks from AI-enabled military weapon systems, as well as\r\naccidents with AI-enabled autonomous systems.\r\nGiven AI’s multifaceted potential, in the United States, a flexible approach to AI\r\ngovernance offers the most likely path to success. The different development\r\ntrajectories, risks, and harms from various AI systems make the prospect of a one-sizefits-all regulatory approach implausible, if not impossible. Regulators should begin to\r\nbuild strength through the heavy lifting of addressing today’s challenges. Even if early\r\nregulatory efforts need to be revised regularly, the cycle of repetition and feedback will\r\nlead to improved muscle memory, crucial to governing more advanced future systems\r\nwhose risks are not yet well understood.\r\nPresident Biden’s October 2023 Executive Order on the Safe, Secure, and Trustworthy\r\nDevelopment and Use of Artificial Intelligence, as well as proposed bipartisan AI\r\nregulatory frameworks, have provided useful starting points for establishing a\r\ncomprehensive approach to AI governance in the United States.2 These stand atop\r\nexisting statements and policies by federal agencies like the U.S. Department of\r\nJustice, the Federal Trade Commission, as well as the U.S. Equal Employment\r\nOpportunity Commission, among others.3\r\nIn order for future AI governance efforts to prove most effective, we offer three\r\nprinciples for U.S. policymakers to follow. We have drawn these thematic principles\r\nCenter for Security and Emerging Technology | 2\r\nfrom across CSET’s wide body of original, in-depth research, as well as granular\r\nfindings and specific recommendations on different aspects of AI, which we cite\r\nthroughout this report. They are:\r\n1. Know the terrain of AI risk and harm: Use incident tracking and horizonscanning across industry, academia, and the government to understand the\r\nextent of AI risks and harms; gather supporting data to inform governance\r\nefforts and manage risk.\r\n2. Prepare humans to capitalize on AI: Develop AI literacy among policymakers\r\nand the public to be aware of AI opportunities, risks, and harms while employing\r\nAI applications effectively, responsibly, and lawfully.\r\n3. Preserve adaptability and agility: Develop policies that can be updated and\r\nadapted as AI evolves, avoiding onerous regulations or regulations that become\r\nobsolete with technological progress; ensure that legislation does not allow\r\nincumbent AI firms to crowd out new competitors through regulatory capture.\r\nThese principles are interlinked and self-reinforcing: continually updating the\r\nunderstanding of the AI landscape will help lawmakers remain agile and responsive to\r\nthe latest advancements, and inform evolving risk calculations and consensus.\r\n1. Know the terrain of AI risk and harm\r\nAs AI adoption progresses, supporting data will be necessary to better understand the\r\ntypes, and extent of, various public and societal risks and harms. U.S. regulators should\r\nprioritize collecting information on AI incidents to inform policymaking and take\r\nnecessary corrective measures, while preserving the technology’s benefits and not\r\nstifling innovation. Ideally, an effective, multipronged approach to AI governance\r\nwould mix incident reporting, evaluation science, and intelligence collection.\r\nCapture data on AI harms through incident reporting. AI systems should be tested\r\nrigorously before deployment, including with each update, but they may be prone to\r\ndrift or failure in environments dissimilar to their testing conditions and can behave in\r\nways unforeseen by system developers.4Malicious actors can also use AI to cause\r\nintentional harm, for instance using generative AI to perpetuate fraud by creating\r\ndeepfake images or videos.\r\n5\r\nIn conceptualizing harm on the spectrum of minimal to\r\nexistential risk, lawmakers can consider harm exposure in four buckets: 1)\r\ndemonstrated harms; 2) probable harms involving known risks in deployed AI systems;\r\n3) implied harms, where studies could uncover new weaknesses in deployed systems;\r\nCenter for Security and Emerging Technology | 3\r\nand 4) speculative harms, including existential risks.\r\n6 These four risk-based buckets\r\nprovide structure to different harms that regulators can use in AI governance.\r\nIncident collection would entail collecting data from accidents and events where AI\r\nsystems caused harm, relying on mandatory, voluntary, and citizen reporting of risks\r\nand harms.\r\n7 A public incident reporting system would not cover military or intelligence\r\nAI incidents, and there could be a separate channel for reporting sensitive AI incidents,\r\nprotected within secure enclaves. Mandatory and voluntary reporting would likely\r\nneed to be overseen by federal agencies with clear regulatory roles and distance from\r\nAI developers, such as the Federal Aviation Administration or the Securities and\r\nExchange Commission.\r\n8 Citizen reporting could be collected either as part of a\r\ngovernmental complaint reporting system or for public consumption by\r\nnongovernmental organizations like the UL Research Institutes, the Organization for\r\nEconomic Cooperation and Development, or even a news media outlet. Initially,\r\nincident reporting could prioritize incidents that generate tangible harms and shift\r\npolitical will, including fatalities, major property damage, or child safety. CSET research\r\nhas explored the pros and cons of these risk collection approaches.9\r\nKnowledge garnered through incident reporting would help achieve several goals.\r\nFirst, it could help improve public awareness around existing real-world AI risks and\r\nharms. With clearer insights into today’s most pressing AI challenges, regulators and\r\nlegislators can better shape laws and address liability issues of public interest.\r\nSecond, as patterns of AI incidents develop across different industries, regulators may\r\nbe able to prioritize certain AI governance actions based on the prevalence of certain\r\nharms. For example, regulators might create risk-based requirements for certain AI\r\nsystems to undergo retesting and recertification if and when iterative improvements\r\nare made to models, similar to how the U.S. Food and Drug Administration subjects\r\nhigh-risk medical devices like pacemakers to continuous evaluation.\r\n10 Incident\r\ncollection would provide regulators with more granular data to better identify new or\r\nmore serious harms and to rapidly devise robust responses.11\r\nThird, developing an incident reporting system is a concrete bureaucratic step that\r\ncould beget more government action to address AI harms. It would require\r\ndetermining where a mandatory and voluntary reporting incident collection body\r\nwould sit within the U.S. government, along with the criteria for different reporting\r\nrequirements. It would also require an action plan and implementation process to\r\nstand it up, and the establishment of a decision-making process for budgeting and\r\nCenter for Security and Emerging Technology | 4\r\nresource allocation. The process of establishing this body would generate momentum\r\nand build muscle memory that carries over to work on thornier AI governance\r\nquestions.\r\nFinally, incident reporting could help build U.S. leadership in AI governance globally.\r\nBuilding a strong exemplar of an incident monitoring and reporting center could\r\nfacilitate collaboration, exchanges, and best-practice sharing with other nations.\r\nIncubating international cooperation could make the United States more aware and\r\nbetter prepared to address AI harms that may be more prevalent in other parts of the\r\nworld, and help build a common foundation with other countries to monitor and spread\r\nawareness of shared AI risks.\r\nInvest in evaluation and measurement methods to strengthen our understanding of\r\ncutting-edge AI systems. The science of measuring the properties of AI systems,\r\nespecially the capabilities of foundation models that can be adapted for many different\r\ndownstream tasks, is currently in early development. Investment is needed to advance\r\nbasic research into how to evaluate AI models and systems, and to develop\r\nstandardized methods and tool kits that AI developers and regulators can use.\r\nPolicymakers’ creation of appropriate governance mechanisms for AI depends on their\r\nability to understand what AI systems can and cannot do, and how these systems rate\r\non trustworthiness properties such as robustness, fairness, and security. The\r\nestablishment of the U.S. Artificial Intelligence Safety Institute within the National\r\nInstitute of Standards and Technology is a promising step in this direction, though it\r\nmay currently lack sufficient resourcing to accomplish the tasks it has been set under\r\nthe 2023 AI executive order and other policy guidance.\r\nBuild a robust horizon scanning capability to monitor new and emerging AI\r\ndevelopments, both domestically and internationally. Alongside incident collection,\r\nmaintaining information awareness and avoiding technological surprise (unexpectedly\r\ndiscovering that competitors have developed advanced capabilities) will allow U.S.\r\nlegislators and regulators to be adaptive in addressing risks and potential harms.12\r\nHorizon scanning capabilities would be relevant for a range of agencies and bodies,\r\nand could take on unique relevant focus areas.\r\nFor instance, an open-source technical monitoring center would be instrumental for the\r\nUnited States. It could help the U.S. intelligence community and other federal agencies\r\nby establishing a core capability to track progress in various AI fields throughout\r\ncommercial industry, academia, and government. This would not only keep the\r\ncommunity well-informed but also enhance the integration of open-source knowledge\r\nCenter for Security and Emerging Technology | 5\r\nwith classified sources, thereby improving the overall intelligence gathering and\r\ninterpretation process––particularly focused outside of the United States.13 For\r\nintelligence community agencies, this monitoring would likely focus on specific\r\ntechnology that augments military systems; agencies outside the intelligence\r\ncommunity might focus their horizon scanning on AI applications that could have a\r\nsignificant (though less clearly defined) impact on the economic competitiveness and\r\nsocietal well-being of the United States. Scanning the horizon for new and emerging\r\ncapabilities can help to ensure that regulators are prepared to handle emerging\r\nchallenges from abroad. This could be valuable amid competition with China or other\r\nauthoritarian states that develop capabilities with negative implications for democratic\r\nsocieties, such as AI for mass surveillance or for generating and spreading political\r\ndisinformation. Robust U.S. horizon-scanning capabilities could improve policymakers’\r\nresponsiveness to the latest threats across AI fields and applications.14\r\n2. Prepare humans to capitalize on AI\r\nAI is ultimately a tool, and like other tools, familiarity with its strengths and limitations\r\nis critical to its effective use. Without adequately educated and trained human users,\r\nsociety will struggle to realize AI’s potential safely and securely. This section presents\r\nseveral points for how regulators and policymakers can prepare the human side of the\r\nequation for emerging AI policy challenges.\r\nDevelop AI literacy among policymakers. AI literacy for policymakers is key to\r\neffectively understanding and governing risks from AI. At a minimum, policymakers\r\nshould understand different types of AI models at a basic level. They should also grasp\r\nAI’s present strengths and limitations for certain tasks, recognize AI models’ outputs,\r\nand acknowledge the technical and societal risks from factors like bias or data issues.\r\nPolicymakers should be keenly aware of the ways that AI systems can be imperfect\r\nand prone to unexpected, sometimes strange failures, often with limited transparency\r\nor explainability. They will need to understand in what contexts using certain AI\r\nmodels is suitable and how machine inputs may bias human decision-making.\r\nGrounding in these and other details of AI systems will be important for understanding\r\nhow new AI differs from current models and for anticipating new regulatory\r\nchallenges.15 Developing training and curricula for those in policy positions could help\r\nbuild AI literacy today, while investing in AI education would benefit the policymakers\r\nof tomorrow and society in general.\r\n16\r\nDevelop AI literacy among the public. Building public AI literacy, beginning as early\r\nas possible and continuing throughout adulthood, can help citizens grasp the\r\nCenter for Security and Emerging Technology | 6\r\nopportunities, risks, and harms posed by AI to society. For instance, AI literacy can help\r\nworkers across fields where intelligent systems are already starting to be applied––\r\nranging from industrial manufacturing to healthcare and finance––to better understand\r\nthe limitations of systems that help them perform their jobs. Knowing when to rely on\r\nthe outputs of AI systems or to exercise skepticism, particularly in decision-making\r\ncontexts, will be important. Alerting workers in other fields to the possibility of\r\nupskilling programs and accreditations could create employment opportunities beyond\r\nthe cutting-edge of AI in competencies like computer and information science. AI\r\nliteracy will be key to participation in the economy of the future for both workers and\r\nconsumers. Promoting AI literacy could also help the public use outputs from systems\r\nlike LLMs appropriately to boost productivity and grasp where risks of plagiarism or\r\ncopyright infringement might exist. The United States could look to countries that have\r\nattempted to implement their own public AI literacy programs, such as Finland, for\r\nbest practices and lessons learned in trying to provide citizens with digital skills.17\r\nMore broadly, alerting the public to the risks of convincing AI-generated\r\ndisinformation, including text, images, videos, and other multimedia that could\r\nmanipulate public opinion, could help citizens remain alert to risks from artificial\r\ncontent.18 This could be a first line of defense against nefarious attempts by malicious\r\nactors to use AI to harm democratic processes and societies. AI developers should also\r\nbe alert to and versed in the risks of harm that integrating their models into different\r\nproducts could create.\r\n3. Preserve adaptability and agility\r\nFinally, given the dynamic nature of AI research, development, deployment, and\r\nadoption, policymakers must be able to incorporate new knowledge into governance\r\nefforts. Allowing space to iteratively build and update policies as technology changes\r\nand incorporating learning into policy formulation could make AI governance more\r\nflexible and effective.\r\nConsider where existing processes and authorities can already help govern AI if\r\ncertain implementation gaps are addressed. AI is likely to require some new types of\r\nregulations and novel policy solutions, but not all regulations for AI will need to be cut\r\nfrom whole cloth. Using existing regulations offers the benefits of speed and familiarity\r\nto lawmakers, as well as the ability to fall back on previously delineated authorities\r\namong federal agencies (compared to the need to litigate overlapping authorities\r\nbetween existing agencies and newly created AI governance agencies). Policymakers\r\nwill need to differentiate between truly novel and comparatively familiar questions\r\nCenter for Security and Emerging Technology | 7\r\nthat AI systems may raise. There are harms that existing protections, such as the\r\nFederal Trade Commission Act and the Civil Rights Act of 1964, might already cover\r\nwhen it comes to issues like copyright infringement or discrimination. Other AI\r\napplications mix corporate activity, product development, and commercialization in\r\nfamiliar ways that are already covered by protections by bodies like the Federal Trade\r\nCommission or the U.S. Food and Drug Administration.19\r\nFor effective AI governance, policymakers must identify where gaps exist in legal\r\nstructures and authorities, as well as areas where implementation infrastructure could\r\nbe lacking. Where applicable legislation does already exist, it will be important to\r\nconsider where agencies require new resources for analyzing AI systems and\r\napplications, such as relevant expertise, sandboxes, and other assessment tools. Given\r\nAI’s wide-ranging applications and their tendency to get at points of tension in current\r\npractices and procedures, new guidance and implementing statutes may be necessary\r\nto ensure that existing laws are effective. In some cases, the regulators that enforce\r\nthese laws may be able to address some of the challenges posed by AI, but they may\r\nbe reluctant to do so based on resource constraints, lack of precedent with a new\r\ntechnology, or the need to overcome procedural hurdles. Examining where procedural\r\nchanges or additional resources can unlock the potential for existing laws to be applied\r\nto AI may allow lawmakers to move more quickly in addressing harms with regulation,\r\nrather than tailoring bespoke solutions to AI problems.\r\nWhere it is less clear that existing regulatory or legal frameworks apply, regulators\r\nshould consider how to develop frameworks that are flexible and can be adapted to\r\nincorporate new information. The National Institute of Science and Technology’s\r\nArtificial Intelligence Risk Management Framework (AI RMF 1.0) is a compelling\r\nexample of a policy document designed to be adapted based on new information and\r\nknowledge.20 The United States can also draw on its mix of state and federal\r\nregulations to aggregate data and information and explore the suitability of flexible,\r\nexperimental governance approaches.21\r\nRemain open to future AI capabilities that may evolve in new, unanticipated, and\r\nunexpected ways. AI models and applications are diverse, and not all technological\r\nprogress will be identical. Policymakers should remain open to the possibility that\r\nfuture AI advancements will not rely on the same factors that enabled recent progress.\r\nFor example, much of the progress in LLM development was driven by a mix of\r\nalgorithmic improvements and increases in computing power, achieved at great cost,\r\nover roughly the past decade.22 Companies may use more compute to fill the increasing\r\ndemand for LLM-based products and to continue to innovate in the near term, at an\r\nCenter for Security and Emerging Technology | 8\r\nincreasingly high cost. That said, it is possible that meaningful future advancement\r\nmay come not just from research achieved with massive compute, but also from\r\nalgorithmic innovation or improvements in data processing that require smaller\r\namounts to advance the state of the art.\r\n23 Indeed, CSET research suggests that growth\r\nin the amount of compute used to train large models appears to be slowing.24\r\nPolicymakers should be aware of new trends—through connection to information\r\nsources like open-source collection, incident reporting, and horizon scanning—and be\r\nprepared to effectively regulate to mitigate the risks and capitalize on the opportunities\r\ninherent in new AI models.\r\nLawmakers should consider the costs and tradeoffs involved when planning AI\r\ngovernance approaches. Estimating the labor and resourcing required to implement\r\nvarious governance regimes is an essential step in selecting a feasible strategy. For\r\nexample, consider regulatory capture, which occurs when a regulatory agency, created\r\nto act in the public's interest, instead advances the commercial or special interests of\r\nthe industry it is charged with regulating, often resulting in policies and decisions that\r\nfavor the regulated entities rather than the public. Congress should welcome not only\r\ninput from AI companies as legislators develop regulatory policy, but also their\r\ncooperation in regulatory enforcement. Industry can help identify the latest trends in AI\r\ndevelopment, including nascent risks and harms, and it has a large, highly-skilled\r\nworkforce whose knowledge the government can draw on.25 However, lawmakers\r\nshould keep in mind that companies are not disinterested parties and have their own\r\nvisions for how to gain and cement advantageous market positions.26 Regulatory\r\ncapture presents similar risks in AI as in other industries.27 However, avoiding it is likely\r\nto require the maintenance of a large, skilled government workforce capable of tasks\r\nlike assessing risks and harms from AI models, and performing analysis and testing.\r\nThis is likely to be both difficult to attain and costly. While the government could limit\r\nsuch costs by adopting governance models that shift responsibility for testing and risk\r\nmitigation onto firms, allowing major AI firms to entrench regulatory positions could\r\npermit firms to develop standards that benefit their development models at the\r\nexpense of others.\r\nDepending on the scope of effort involved, if lawmakers seek to eliminate certain AI\r\nrisks, they may be more willing to devote costly resources to develop a high-intensity,\r\ngovernment-first approach that avoids regulatory capture. If risk minimization is\r\nsufficient, avoiding regulatory capture may be less of a priority. Keeping these tradeoffs in mind will be key going forward.\r\nCenter for Security and Emerging Technology | 9\r\nConclusion\r\nAI governance shapes how humans develop and use AI in ways that reflect their\r\nsocietal values. By adhering to the principles outlined in this brief—understanding AI\r\nincidents, closely monitoring tech advancement, fostering AI literacy, and maintaining\r\nregulatory flexibility—the United States can lead in responsible AI development. This\r\napproach will help safeguard important societal values, promote innovation, and\r\nnavigate the dynamic landscape of AI advancements. These enabling principles offer a\r\nroadmap for crafting agile, informed policies that can keep pace with technological\r\nprogress and ensure AI benefits society as a whole. The next step is for leaders,\r\npolicymakers, and regulators to craft governance oversight that allows innovation to\r\nprogress under watchful supervision and in an atmosphere of accountability. ", "metadata": {"original_filename": "item096_US_Enabling Principles for AI Governance.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T22:21:33.010202", "updated_at": "2025-08-28T22:21:33.010202", "word_count": 23399}