"""
身份验证相关路由
"""
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPBearer
from pydantic import BaseModel, EmailStr
from typing import Optional, Dict, Any
from datetime import timedelta, datetime
from src.core.security import security_service
from src.core.config import settings
from src.core.auth import auth_deps
from src.models.schemas import Token, UserCreate, UserLogin, UserResponse
import logging

logger = logging.getLogger(__name__)

router = APIRouter()
security = HTTPBearer()

class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str
    password: str

class LoginResponse(BaseModel):
    """登录响应模型"""
    access_token: str
    token_type: str
    expires_in: int
    user: Dict[str, Any]

class APIKeyResponse(BaseModel):
    """API密钥响应模型"""
    api_key: str
    description: str

@router.post("/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    """
    用户登录
    
    Args:
        request: 登录请求
        
    Returns:
        登录响应
    """
    try:
        # 这里应该添加实际的用户验证逻辑
        # 暂时使用简单的验证方式
        if request.username == "admin" and request.password == "admin":
            user_data = {
                "sub": "admin",
                "username": "admin",
                "role": "admin",
                "permissions": ["read", "write", "delete"],
                "is_active": True
            }
        else:
            # 简单的用户验证（实际应该查询数据库）
            user_data = {
                "sub": request.username,
                "username": request.username,
                "role": "user",
                "permissions": ["read"],
                "is_active": True
            }
        
        # 创建访问令牌
        access_token = security_service.create_access_token(
            data=user_data,
            expires_delta=timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        )
        
        logger.info(f"用户 {request.username} 登录成功")
        
        return LoginResponse(
            access_token=access_token,
            token_type="Bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=user_data
        )
        
    except Exception as e:
        logger.error(f"登录失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )

@router.post("/api-key", response_model=APIKeyResponse)
async def generate_api_key(
    current_user: Dict[str, Any] = Depends(auth_deps.require_admin)
):
    """
    生成API密钥（仅管理员）
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        API密钥响应
    """
    try:
        api_key = security_service.generate_api_key()
        
        logger.info(f"管理员 {current_user['username']} 生成了新的API密钥")
        
        return APIKeyResponse(
            api_key=api_key,
            description="请妥善保管API密钥，不要泄露"
        )
        
    except Exception as e:
        logger.error(f"生成API密钥失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="生成API密钥失败"
        )

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: Dict[str, Any] = Depends(auth_deps.get_current_user)
):
    """
    获取当前用户信息
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        用户信息
    """
    return UserResponse(
        user_id=current_user["user_id"],
        username=current_user["username"],
        role=current_user["role"],
        permissions=current_user["permissions"],
        created_at=datetime.now(),
        updated_at=datetime.now()
    )

@router.post("/refresh")
async def refresh_token(
    current_user: Dict[str, Any] = Depends(auth_deps.get_current_user)
):
    """
    刷新访问令牌
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        新的访问令牌
    """
    try:
        # 创建新的访问令牌
        access_token = security_service.create_access_token(
            data=current_user,
            expires_delta=timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        )
        
        return {
            "access_token": access_token,
            "token_type": "Bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        }
        
    except Exception as e:
        logger.error(f"刷新令牌失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="刷新令牌失败"
        )

@router.post("/logout")
async def logout(
    current_user: Dict[str, Any] = Depends(auth_deps.get_current_user)
):
    """
    用户登出
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        登出结果
    """
    try:
        logger.info(f"用户 {current_user['username']} 登出")
        
        return {
            "message": "登出成功",
            "success": True
        }
        
    except Exception as e:
        logger.error(f"登出失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出失败"
        )