from fastapi import FastAPI, Depends, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
import uvicorn
from typing import List, Dict, Any
import os
import logging
from pathlib import Path

from src.api.router import api_router
from src.core.config import settings
from src.core.logging import setup_logging
from src.core.rate_limiter import RateLimitMiddleware

# 设置模块路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

# 导入新的高级分析路由
ADVANCED_ANALYSIS_AVAILABLE = False
INFRASTRUCTURE_AVAILABLE = False

try:
    from src.api.routes.advanced_analysis import router as advanced_router
    ADVANCED_ANALYSIS_AVAILABLE = True
    print("✅ 高级分析模块加载成功")
except ImportError as e:
    print(f"⚠️  高级分析模块加载失败: {e}")

# 导入基础设施模块
try:
    from infrastructure import get_infrastructure_status
    from services import get_service_status
    INFRASTRUCTURE_AVAILABLE = True
    print("✅ 基础设施模块加载成功")
except ImportError as e:
    print(f"⚠️  基础设施模块加载失败: {e}")

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Create FastAPI application
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="智能文档分析系统，专注于政策文档的深度分析和叙事结构识别",
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=[
        "Authorization",
        "Content-Type",
        "Accept",
        "X-Requested-With",
        "X-API-Key",
    ],
    max_age=600,  # Cache preflight requests for 10 minutes
)

# Configure rate limiting (暂时禁用用于测试)
# app.add_middleware(RateLimitMiddleware)

# Include API routes
app.include_router(api_router, prefix="/api/v1")

# Include advanced analysis routes (v2 API)
if ADVANCED_ANALYSIS_AVAILABLE:
    app.include_router(advanced_router)
    logger.info("✅ 高级分析API路由已集成")

# Test endpoint
@app.get("/api/v1/test")
async def test_api():
    return {"message": "API正常工作", "status": "ok"}

# Debug endpoint to show all routes
@app.get("/api/v1/debug/routes")
async def debug_routes():
    routes = []
    for route in app.routes:
        routes.append({
            "path": route.path,
            "name": route.name,
            "methods": list(route.methods) if hasattr(route, 'methods') else []
        })
    return {"routes": routes}

# Mount static files
static_path = Path(__file__).parent.parent / "static"
if static_path.exists():
    app.mount("/static", StaticFiles(directory=str(static_path)), name="static")
    logger.info(f"Static files mounted from {static_path}")

# Root endpoint
@app.get("/", tags=["Root"])
async def root():
    """返回Web UI首页"""
    index_path = Path(__file__).parent.parent / "static" / "index.html"
    if index_path.exists():
        return FileResponse(str(index_path))
    return {"message": "欢迎使用文档分析系统", "version": settings.APP_VERSION}

# Health check endpoint
@app.get("/health", tags=["Health"])
async def health_check():
    return {"status": "healthy", "version": settings.APP_VERSION}

# System status endpoint with PANTOS capabilities
@app.get("/api/v1/system/status", tags=["System"])
async def system_status():
    """获取系统完整状态，包括PANTOS升级功能"""
    
    status = {
        "system": {
            "name": settings.APP_NAME,
            "version": settings.APP_VERSION,
            "status": "healthy",
            "framework": "PANTOS v1.0",
            "description": "Policy Actors Narratives Topics Outcomes Strategies"
        },
        "api_versions": {
            "v1": "stable",
            "v2": "available" if ADVANCED_ANALYSIS_AVAILABLE else "unavailable"
        },
        "core_features": {
            "document_management": True,
            "traditional_analysis": True,
            "visualization": True,
            "batch_processing": True
        },
        "advanced_features": {
            "ai_model_orchestrator": ADVANCED_ANALYSIS_AVAILABLE and INFRASTRUCTURE_AVAILABLE,
            "temporal_framing": ADVANCED_ANALYSIS_AVAILABLE,
            "spatial_framing": False,  # 开发中
            "discourse_coalition": False,  # 开发中
            "emotion_analysis": False,  # 开发中
            "network_analysis": False,  # 开发中
        }
    }
    
    # 添加基础设施状态
    if INFRASTRUCTURE_AVAILABLE:
        try:
            infrastructure_status = get_infrastructure_status()
            status["infrastructure"] = infrastructure_status
        except Exception as e:
            status["infrastructure"] = {"error": str(e)}
    
    # 添加服务状态
    if INFRASTRUCTURE_AVAILABLE:
        try:
            service_status = get_service_status()
            status["services"] = service_status
        except Exception as e:
            status["services"] = {"error": str(e)}
    
    return status

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    logger.error(f"HTTP error: {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail},
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    logger.error(f"Unexpected error: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"error": "内部服务器错误，请联系管理员"},
    )

if __name__ == "__main__":
    logger.info("启动文档分析API服务...")
    uvicorn.run(
        "src.main:app",  # 修正模块路径 
        host="127.0.0.1",  # 使用localhost避免防火墙问题
        port=8000, 
        reload=settings.DEBUG,
        log_level="info"
    )
