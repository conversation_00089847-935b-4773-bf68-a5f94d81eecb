from fastapi import APIRouter, HTTPException, Depends, status
from typing import List, Dict, Any, Optional
import logging
import uuid
import asyncio
from datetime import datetime
import time

from src.models.schemas import BatchAnalysisRequest, BatchAnalysisResponse, TaskType
from src.services.document_service import DocumentService, get_document_service
from src.services.analysis_service import AnalysisService, get_analysis_service

router = APIRouter()
logger = logging.getLogger(__name__)

# 存储批处理作业状态
batch_jobs = {}

# 并发控制配置
DEFAULT_CONCURRENCY = 3  # 默认并发数
MAX_CONCURRENCY = 10     # 最大并发数


@router.post("/analyze", response_model=BatchAnalysisResponse)
async def batch_analyze(
    request: BatchAnalysisRequest,
    document_service: DocumentService = Depends(get_document_service),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """批量分析文档"""
    try:
        # 验证所有文档是否存在
        for doc_id in request.document_ids:
            doc = await document_service.get_document(doc_id)
            if not doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"文档 {doc_id} 不存在"
                )
        
        # 创建批处理作业
        job_id = str(uuid.uuid4())
        concurrency = min(request.concurrency or DEFAULT_CONCURRENCY, MAX_CONCURRENCY)

        batch_jobs[job_id] = {
            "status": "pending",
            "total": len(request.document_ids),
            "completed": 0,
            "failed": 0,
            "processing": 0,
            "progress_percentage": 0.0,
            "concurrency": concurrency,
            "document_ids": request.document_ids,
            "tasks": [task.value for task in request.tasks],
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "start_time": None,
            "current_processing_files": [],
            "results": {}
        }
        
        # 返回作业信息
        response = BatchAnalysisResponse(
            job_id=job_id,
            status="pending",
            total=len(request.document_ids),
            completed=0,
            failed=0,
            processing=0,
            progress_percentage=0.0,
            concurrency=concurrency,
            current_processing_files=[],
            created_at=batch_jobs[job_id]["created_at"],
            updated_at=batch_jobs[job_id]["updated_at"]
        )
        
        logger.info(f"创建批处理作业: {job_id}，共 {len(request.document_ids)} 个文档，{len(request.tasks)} 个任务")
        
        # 异步处理批量任务（实际项目应使用后台任务队列如Celery）
        # 这里只是简单实现，不推荐在生产环境使用
        # 在后台启动批处理任务
        asyncio.create_task(process_batch_job(job_id, request, document_service, analysis_service))
        
        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建批处理作业失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建批处理作业失败: {str(e)}"
        )


@router.get("/status/{job_id}", response_model=BatchAnalysisResponse)
async def get_batch_status(job_id: str):
    """获取批处理作业状态"""
    try:
        if job_id not in batch_jobs:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"批处理作业 {job_id} 不存在"
            )

        job = batch_jobs[job_id]

        # 计算预估剩余时间
        estimated_remaining_time = None
        if job["start_time"] and job["completed"] > 0:
            elapsed_time = time.time() - job["start_time"]
            avg_time_per_doc = elapsed_time / job["completed"]
            remaining_docs = job["total"] - job["completed"]
            estimated_remaining_time = int(avg_time_per_doc * remaining_docs)

        response = BatchAnalysisResponse(
            job_id=job_id,
            status=job["status"],
            total=job["total"],
            completed=job["completed"],
            failed=job.get("failed", 0),
            processing=job.get("processing", 0),
            progress_percentage=job.get("progress_percentage", 0.0),
            concurrency=job.get("concurrency", DEFAULT_CONCURRENCY),
            estimated_remaining_time=estimated_remaining_time,
            current_processing_files=job.get("current_processing_files", []),
            created_at=job["created_at"],
            updated_at=job.get("updated_at", job["created_at"])
        )

        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取批处理作业状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取批处理作业状态失败: {str(e)}"
        )


@router.get("/results/{job_id}")
async def get_batch_results(job_id: str):
    """获取批处理作业结果"""
    try:
        if job_id not in batch_jobs:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"批处理作业 {job_id} 不存在"
            )

        job = batch_jobs[job_id]

        # 检查作业是否完成
        if job["status"] not in ["completed", "failed"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"批处理作业 {job_id} 尚未完成，当前状态: {job['status']}"
            )

        # 格式化结果数据
        results = []
        for doc_id, result_data in job["results"].items():
            result_item = {
                "document_id": doc_id,
                "document_title": result_data.get("document_title", f"文档 {doc_id}"),
                "status": result_data.get("status", "unknown"),
            }

            if result_data.get("status") == "completed":
                result_item["results"] = result_data.get("results", {})
            elif result_data.get("status") == "error":
                result_item["error"] = result_data.get("error", "未知错误")

            results.append(result_item)

        return {
            "job_id": job_id,
            "status": job["status"],
            "total": job["total"],
            "completed": job["completed"],
            "created_at": job["created_at"],
            "results": results
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取批处理作业结果失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取批处理作业结果失败: {str(e)}"
        )


# 注意：此函数在实际项目中应改用后台任务队列如Celery实现
async def process_batch_job(
    job_id: str,
    request: BatchAnalysisRequest,
    document_service: DocumentService,
    analysis_service: AnalysisService
):
    """处理批处理作业（支持并发）"""
    try:
        logger.info(f"开始处理批处理作业: {job_id}，并发数: {request.concurrency}")
        batch_jobs[job_id]["status"] = "processing"
        batch_jobs[job_id]["start_time"] = time.time()

        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(request.concurrency or DEFAULT_CONCURRENCY)

        # 创建所有任务
        tasks = []
        for doc_id in request.document_ids:
            task = process_single_document(
                semaphore, job_id, doc_id, request.tasks,
                document_service, analysis_service
            )
            tasks.append(task)

        # 并发执行所有任务
        await asyncio.gather(*tasks, return_exceptions=True)

        # 更新作业状态
        batch_jobs[job_id]["status"] = "completed"
        batch_jobs[job_id]["updated_at"] = datetime.now()
        batch_jobs[job_id]["processing"] = 0
        batch_jobs[job_id]["current_processing_files"] = []
        batch_jobs[job_id]["progress_percentage"] = 100.0
        logger.info(f"批处理作业 {job_id} 已完成")

    except Exception as e:
        logger.error(f"处理批处理作业 {job_id} 失败: {str(e)}")
        batch_jobs[job_id]["status"] = "failed"
        batch_jobs[job_id]["updated_at"] = datetime.now()
        batch_jobs[job_id]["processing"] = 0
        batch_jobs[job_id]["current_processing_files"] = []
        batch_jobs[job_id]["error"] = str(e)


async def process_single_document(
    semaphore: asyncio.Semaphore,
    job_id: str,
    doc_id: str,
    tasks: List,
    document_service: DocumentService,
    analysis_service: AnalysisService
):
    """处理单个文档（并发安全）"""
    async with semaphore:
        try:
            # 更新正在处理的文件列表
            batch_jobs[job_id]["processing"] += 1
            batch_jobs[job_id]["current_processing_files"].append(doc_id)
            batch_jobs[job_id]["updated_at"] = datetime.now()

            logger.info(f"开始处理文档 {doc_id}")

            # 获取文档
            document = await document_service.get_document(doc_id)
            if not document:
                logger.warning(f"文档 {doc_id} 不存在，跳过")
                batch_jobs[job_id]["results"][doc_id] = {
                    "status": "error",
                    "error": "文档不存在"
                }
                batch_jobs[job_id]["failed"] += 1
                return

            # 执行分析任务
            results = {}
            for task in tasks:
                try:
                    # 模拟处理时间
                    await asyncio.sleep(1)  # 模拟分析耗时

                    # 这里需要调用实际的分析服务
                    # 暂时返回模拟结果
                    results[task.value] = {
                        "status": "completed",
                        "result": f"模拟分析结果 - 任务: {task.value}, 文档: {document.title}"
                    }
                except Exception as task_error:
                    logger.error(f"任务 {task.value} 执行失败: {task_error}")
                    results[task.value] = {
                        "status": "error",
                        "error": str(task_error)
                    }

            # 保存结果到批处理状态
            batch_jobs[job_id]["results"][doc_id] = {
                "status": "completed",
                "document_title": document.title,
                "results": results
            }

            # 更新作业状态
            batch_jobs[job_id]["completed"] += 1
            batch_jobs[job_id]["progress_percentage"] = (batch_jobs[job_id]["completed"] / batch_jobs[job_id]["total"]) * 100

            logger.info(f"文档 {doc_id} 处理完成 ({batch_jobs[job_id]['completed']}/{batch_jobs[job_id]['total']})")

        except Exception as e:
            logger.error(f"处理文档 {doc_id} 失败: {str(e)}")
            batch_jobs[job_id]["results"][doc_id] = {
                "status": "error",
                "document_title": f"文档 {doc_id}",
                "error": str(e)
            }
            batch_jobs[job_id]["failed"] += 1

        finally:
            # 从正在处理的文件列表中移除
            batch_jobs[job_id]["processing"] -= 1
            if doc_id in batch_jobs[job_id]["current_processing_files"]:
                batch_jobs[job_id]["current_processing_files"].remove(doc_id)
            batch_jobs[job_id]["updated_at"] = datetime.now()
