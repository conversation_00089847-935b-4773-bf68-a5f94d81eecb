"""
🎯 PANTOS微服务模块
政策叙事分析的专业化服务组件
"""

__version__ = "1.0.0"
__author__ = "PANTOS Analysis Team"

# 服务模块导入
try:
    from .narrative_analyzer.temporal_framing import analyze_temporal_framing
    TEMPORAL_FRAMING_AVAILABLE = True
except ImportError:
    TEMPORAL_FRAMING_AVAILABLE = False

# 可用服务列表
AVAILABLE_SERVICES = {
    "temporal_framing": TEMPORAL_FRAMING_AVAILABLE,
    "spatial_framing": False,  # 开发中
    "discourse_coalition": False,  # 开发中
    "emotion_analyzer": False,  # 开发中
    "network_analyzer": False,  # 开发中
}

def get_service_status():
    """获取服务状态"""
    return {
        "services": AVAILABLE_SERVICES,
        "total_services": len(AVAILABLE_SERVICES),
        "available_services": sum(AVAILABLE_SERVICES.values()),
        "version": __version__
    }
