{"doc_id": "68159dfd-432e-433f-8c87-965306c23b8c", "title": "item163_US_Artificial Intelligence Wasn't Born Yesterday", "text": "Artificial Intelligence Wasn't Born Yesterday\r\n\r\nIt's great to see money pouring into industrial artificial-intelligence research, but many investors don't seem to know much about it or where the field is headed. Some investors know just as little about the history of software. The topic is rarely taught in today's STEM-focused colleges.\r\n\r\nAs AI improves, new ethical problems develop -- though in some cases they're merely old problems in new forms. The rise of ChatGPT, for example, has sometimes required teachers to distinguish software-written from student-written prose. A teacher who accepts ChatGPT-type prose from his students isn't teaching much -- certainly not how to write. But ChatGPT is a great gift for cheaters, and cheating has been widespread at elite American colleges for decades. Students today blend 21st-century technical knowledge with Iron Age moral sophistication.\r\n\r\nChatGPT was nonetheless a major milestone in AI, as every serious American news source has reported. Some uninformed observers believe that AI actually began with ChatGPT. In fact AI research began, slowly, in the 1950s.\r\n\r\nIn 1950, the great English mathematician <PERSON> predicted that artificial intelligence would soon emerge, supported by the new digital computers he helped invent. By AI, he meant software that would make computers behave as if they had actual human minds.\r\n\r\nBy 1957 an AI software project called the Geometry Machine rediscovered a surprising and beautiful little proof of a simple theorem in high-school geometry. The proof had been well-known in antiquity but had been forgotten by many teachers and textbook writers. The rediscovery of this proof was a milestone: Suddenly, artificial intelligence seemed possible.\r\n\r\nThis machine was built by <PERSON> -- my father -- at IBM Research Labs. Journalists and historians have often unfairly overlooked IBM Research's contributions to AI. But IBM arrived at another significant AI breakthrough in 1997, when its Deep Blue software (and purpose-built hardware) beat the world's reigning (human) chess champion. Still more impressive was IBM's Watson, an AI system that beat two \"Jeopardy!\" world champions in 2011. The Watson system was able to answer successfully a range of unpredictable, strangely phrased questions on the TV game show.\r\n\r\nThe IBM software I've mentioned was built using different tools from those used to create ChatGPT. IBM used programming languages; ChatGPT is based on a neural network. Both methods will play significant roles in the future of AI.\r\n\r\nThe biggest challenge AI faces today is to understand the human mind. The mind is capable of maneuvers that no AI system I know of has ever achieved. Future AI must learn to understand real human speech and writing (Watson made a strong start), which requires a thorough understanding of humor, irony, metaphor and many other twists on speaking and writing. Most important, AI must learn to understand emotion and emotion's central role in human thought.\r\n\r\nSome thinkers hold that software will never actually understand anything, because it will never be conscious. They're right. Consciousness is a product of certain organic systems, not of electronic circuits. But software can act as if it understands -- and that's what matters in practice.\r\n\r\nIn time, AI will be able to chat with the lonely and sick around the clock. Will it replace humans who care? Of course not. But it will be better than nothing. Many of us will come to rely on AI-based digital assistants, small devices that murmur schedule notes or parking tips in our ears, patch in phone calls, read us emails and otherwise act like superhuman secretaries. Our understanding of the mind will be the basis of software models that will be widely (sometimes dangerously) used to predict a person's future behavior.\r\n\r\nThe U.S. has a lead in AI, which helps and hurts. AI will push our standard of living higher. It already has. But it will also weaken the human mind by taking over ordinary tasks. The greatest danger isn't the usual nonsense about out-of-control AI rampaging over the American landscape. The danger today is that shortsighted politicians will use AI as an excuse to cut defense while our enemies continue to arm, frantically. Are we Britain on the brink of World War II? No. At least they had Churchill.\r\n\r\nMr. Gelernter is a professor of computer science at Yale. His forthcoming books are \"Mind: A User's Manual\" and, with Rabbi Benjamin Scolnic, \"Saul and David: A New Translation and Commentary.\"", "metadata": {"original_filename": "item163_US_Artificial Intelligence Wasn't Born Yesterday.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:35:12.912316", "updated_at": "2025-08-28T21:35:12.912316", "word_count": 4549}