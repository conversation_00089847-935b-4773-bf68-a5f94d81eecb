{"doc_id": "3f347d1f-098e-4f2c-98ad-8fb8e95972d3", "title": "item073_US_Defense Priorities in the Open-Source AI Debate", "text": "Defense Priorities in the Open-Source AI Debate\r\nA Preliminary Assessment\r\n\r\nTerms of the Debate\r\nOpen-source software and standards are already widespread in U.S. national security applications.16 Army smartphones, Navy warships, and Space Force missile-warning satellites run on Linux-derived operating systems.17 AI-powered F-16s run on open-source orchestration frameworks like Kubernetes, which is regularly updated, maintained, and tested by industry and the broader public.18 Open-source software is ubiquitous, permeating over 96 percent of civil and military codebases, and will remain a core piece of defense infrastructure for years to come.19\r\n\r\nWhat constitutes an “open” foundation model is less well defined. Developers can distribute foundation models at different levels of “openness”—from publishing white papers and basic technical information to releasing models entirely, including their underlying weights, training data, and the code used to run them.20 By contrast, developers of closed models, including GPT-4 or Claude, release fewer details or data, only allowing user access through proprietary application programming interfaces.21 In general, this brief defines “open” models as those with widely available weights, consistent with relevant categories in the 2023 AI executive order.22 Many of the risks and benefits discussed here flow from these definitions.\r\n\r\nClaims of extraordinary risk have motivated several recent proposals surrounding open-source AI. Analysts have expressed concern that malicious users might modify open foundation models to discover cybersecurity vulnerabilities or instruct users in the creation of chemical and biological weapons.23 Others have argued that public distribution of model weights could aid adversaries in advancing their AI capabilities.24 Given these apprehensions, some observers have proposed export controls, licensing rules, and liability regimes that would limit the distribution of open foundation models.25\r\n\r\nA competing school of thought has emphasized the societal benefits of open foundation models.26 Open distribution of weights, some argue, accelerates innovation and adoption: indeed, the key frameworks and innovations underpinning today’s large language models (LLMs), like PyTorch and the transformer architecture itself, were distributed openly.27 Others contend that the public scrutiny of model weights enables rapid discovery and repair of vulnerabilities, improves public transparency, and reduces the concentration of political and economic power as AI systems increase in importance.28\r\n\r\nWhat is most clear, however, is that this risk-benefit assessment remains incomplete. The U.S. Department of Commerce’s initial assessment is inconclusive, and AI safety literature has thus far lacked clear frameworks for identifying relative risk and benefit and whether they are unique to open models.29 Despite concerns over AI models instructing untrained users in biological weapon development, for instance, recent red-teaming exercises concluded that LLM-equipped teams performed similarly to those without.30 Similar concerns over AI-assisted cyber vulnerability discovery remain unclear, with some arguing that enhanced vulnerability detection may benefit cyber defenders over attackers, or that the balance of advantage would be case-dependent.31 Malicious use, meanwhile, continues to take place with closed models.32 In brief, more research remains necessary to unpack where the relative risks and benefits lie.33 The purportedly catastrophic harms of tomorrow’s foundation models have not yet come into clear view.34\r\n\r\nSecond, the pace of technical change has been so uncertain that evaluating future benefits, harms, and policy interventions can be challenging.35 Whether a licensing regime is effective, for example, depends on how readily foundation model technologies will diffuse.36 And whether export controls benefit national security hinges on which analogy becomes relevant: Is restricting open models like restricting nuclear weapons exports, or is it akin to Cold War bans (now repealed) on public-key cryptography, a technology which now underpins online banking, e-commerce, and a multi-trillion-dollar digital economy?37 In the absence of a U.S. market presence, will Chinese open models take their place?38\r\n\r\nFinally, questions remain on how to implement AI policy. Definitional challenges abound; early AI policy approaches, including the EU AI Act, AI executive order, and California SB 1047, apply thresholds for “systemic risk” to models exceeding a certain amount of computing power or cost used in their development.39 However, such thresholds for triggering government review, such as the 1026 floating-point-operation threshold in the AI executive order, may incompletely capture the capabilities they aim to regulate.40 How to balance resourcing for AI policy implementation against other cyber and biological threat mitigations, such as for material monitoring or new cyberdefense capabilities, remains another open question.41\r\n\r\nRemote Visualization\r\n\r\n\r\nOpen Source and the Joint Force\r\nA defense industrial assessment could thus contribute a valuable perspective to the AI risk debate. With AI industry trends favoring consolidation, the open foundation model ecosystem may become an increasingly important source of competition in the industrial base.43 Because end users can modify and run open models directly, they have become increasingly relevant for developing local, secure applications and embedded systems—needed by military users demanding low power usage, security, and reliability. And because open models can be publicly inspected, red teamed, and verified, they may present defense-related cybersecurity advantages.44\r\n\r\nTo date, however, the DOD has largely focused on AI adoption.45 In its flagship data, responsible AI, and adoption strategies, the DOD has focused on harnessing private sector innovations for national security end uses.46 It has embedded chief data officers in combatant commands; tested AI use cases in major experimentation initiatives, like the Global Information Dominance Experiment, Project Convergence, and others; and developed the Responsible AI Framework, emphasizing the use of traceable, transparent AI systems.47\r\n\r\nIn August 2023, the DOD established Task Force Lima, an element within the CDAO tasked with “responsibly pursu[ing] the adoption” of generalist models.48 Alongside the CDAO and Responsible AI Working Council, Lima was chartered to “accelerate” AI initiatives, “federate disparate developmental and research efforts,” and engage across the interagency on the “responsible development and use of generative AI,” with a final strategy due for release in early 2025.49\r\n\r\nClarifying potential AI use cases within the DOD is a valuable first step in “mak[ing] life easier for program offices who want to do AI or add AI.”50 A valuable second step would be to identify the broader trajectory of the AI industrial base. The DOD will often rely on industry expertise to develop and identify more generative AI use cases; a broad ecosystem of model and application developers will be critical for this process.51\r\n\r\nIn short, an assessment of defense industrial impacts is conspicuously missing from the broader debate on open foundation models. Arguments over model regulation are couched in national security language but should involve a broader swath of national security practitioners, including defense acquisition professionals.52 Accordingly, DOD elements, including the CDAO, should independently assess the national capacity to develop AI-powered systems and the impact of open foundation models.\r\n\r\nArguments over model regulation are couched in national security language but should involve a broader swath of national security practitioners.\r\n\r\nFrom Adoption Strategies to Industrial Strategies\r\nA defense industrial accounting is needed because of preliminary evidence that open foundation models—and their supporting ecosystem—could be useful for the DOD. AI adoption remains a DOD priority, and open release has historically accelerated the rate of technology adoption.53 Open-source development may have positive competitive implications for defense acquisition. And open-source communities are accelerating developments in on-premises deployment, fine-tuning for specialized applications, model reliability, and other desirable characteristics for defense end users.54\r\n\r\nSupplier diversity. In its 2023 National Defense Industrial Strategy (NDIS), the DOD prioritized the diversification of national component supplier bases.55 To improve competition for new entrants, for instance, it has developed new funding mechanisms, like Strategic Funding Increase contracts, and innovation organizations, like the Defense Innovation Unit.56 A robust ecosystem of open foundation model developers might improve defense supplier diversity and prevent market consolidation.\r\n\r\nThere are strong incentives for consolidation in the foundation model industry.57 Unlike other software products, large foundation models are particularly capital-intensive to develop, rivaling the supply chain complexity of major defense hardware. Training a foundation model, open or closed, demands extremely large datasets, costly graphics processing units and accelerator chips, and talent, advantaging larger players.58 Operating them—a process termed inference—also imposes high energy costs.59 Due to these factors, industry leaders have spent tens of billions of dollars to develop and deploy so-called frontier foundation models.60 This high cost of entry increases the risk of industry consolidation and, consequently, capacity pressure on future DOD acquisition programs.\r\n\r\nWould a robust open AI ecosystem relieve these pressures, creating competition for proprietary vendors and accelerating innovation?61 Open foundation models have begun to show competitive performance with closed, proprietary alternatives.62 To date, many competitive open foundation models have been developed by well-resourced actors.63 But community modification and experimentation have sped the development of new architectures and reductions in training and inference costs.64 Modified open models have also demonstrated high performance in highly specialized tasks; they might be similarly performant for those anticipated for defense applications, like flight control, business automation, or intelligence fusion.65\r\n \r\nCompetitive sustainment. More critically, the existence of open foundation models mitigate dependence on single vendors when sustaining AI-powered defense systems.66 Foundation models used in defense applications may need specialized retuning, including with government data, as the operating environment changes.67 High-performance closed models have also shown performance drifts over time; access to model weights and other information would help vendors diagnose emergent problems.68 In short, by building on open foundation models, the DOD could reduce barriers for vendors to compete on model integration, retuning, and sustainment.\r\n\r\nThese imperatives resemble those motivating the Pentagon’s Modular Open Systems Approach (MOSA) for major hardware purchases.69 Lack of access to technical data has historically challenged the DOD’s ability to reliably and affordably sustain military hardware.70 Given these barriers, military services have increasingly demanded access to the data needed to service new helicopters, trucks, and ships. For its nearly $70 billion Future Long-Range Assault Aircraft program, for example, the U.S. Army rejected a $3.6 billion lower bid over incomplete MOSA requirements, opting for a vendor that provided full access to aircraft technical data packages (TDPs) required for maintenance.71 Just as it is challenging to competitively source aircraft maintenance without access to TDPs, competitively sustaining AI-powered software may be difficult without access to model weights.72\r\n\r\nIt would be challenging, however, to adopt such a modular approach with closed-model-powered software. Closed model developers are unlikely to relinquish access to model weights for other vendors to modify while performing their sustainment contracts.73 Moreover, to securely implement model-level changes to a defense application, national security customers would potentially need to certify both the application vendor and closed model vendor separately, adding additional friction to an already difficult acquisition process.74 Evaluating appropriate data rights for closed models could demand a further institutional lift.75\r\n\r\nA world with competitive open foundation models would allow the Pentagon to sidestep these sustainment challenges. While vendors would tune and adapt open models for proprietary applications, visibility into their underlying weights and architectures would make it easier for others to maintain and integrate them, potentially reducing the cost of sustainment.76 The presence of open options, moreover, could improve taxpayer bargaining power in negotiations with closed providers on complex data-rights matters. The DOD has already invested heavily in competitively sustaining military hardware. Those same lessons apply when sustaining AI-powered software.\r\n \r\nSecurity and reliability. Concerns over the confidentiality and reliability of foundation models remain key barriers to DOD generative AI adoption.77 The potential to locally operate open foundation models on DOD infrastructure therefore becomes a key advantage. Open models might present useful options for building AI-powered systems without needing to certify an external foundation model developer for sandboxed deployments.78 Like with Linux, Kubernetes, or other open software libraries, these models might become a secure baseline for AI that vendors modify with classified or specialized information.79\r\n\r\nA robust open research community could also drive advancements in AI model reliability and interpretability, reducing the number of hallucinations—nonuniform responses that do not reflect the information a user needs.80 Access to model weights is often crucial for diagnosing failure and evaluating model reliability in detail.81 Insofar as hallucinations remain a “showstopper” for defense AI adoption, innovations in the open foundation model ecosystem are worth considering.82\r\n\r\nFinally, much like with open-source software, open access to weights might enable a greater possibility of detecting vulnerabilities.83 Indeed, the AI executive order places special emphasis on red teaming future foundation models; more open approaches to model publication allow a wider peer review of model performance.84 As Cybersecurity and Infrastructure Security Agency analysts have recently emphasized, there is “significant value in open foundation models to help strengthen cybersecurity, increase competition, and promote innovation.”85 Accordingly, future assessments might review the advantages and disadvantages open models present for hardened, defense-critical AI systems.\r\n \r\nSpecialized use cases. Lastly, a robust open foundation model ecosystem might enable AI use cases that receive less attention from closed-source providers. Because open models can be retrained, fine-tuned, and broadly customized, they can serve as a basis for national-security-specific applications.86 Further, open-source initiatives can drive innovation in national-security-relevant topics, such as for document and data search with semantic retrieval.87 These search algorithms leverage embedding models—a form of language model—to compare the semantic meaning of stored documents and return relevant results; open models largely dominate performance metrics for embedding generation.88 Domains overlooked by major commercial players could benefit from the dynamism of open development.89 Further investigation should assess use cases where open model performance meaningfully exceeds closed alternatives.", "metadata": {"original_filename": "item073_US_Defense Priorities in the Open-Source AI Debate.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:51:54.220701", "updated_at": "2025-08-28T21:51:54.220701", "word_count": 16009}