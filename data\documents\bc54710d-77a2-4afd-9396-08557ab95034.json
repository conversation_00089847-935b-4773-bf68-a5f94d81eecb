{"doc_id": "bc54710d-77a2-4afd-9396-08557ab95034", "title": "item176_US_Regulating Artificial Intelligence in a World  of Uncertainty", "text": "Regulating Artificial Intelligence in a World  of Uncertainty\r\nEU and US Approaches to AI Governance Given the discussion of the preceding two sections, I will now evaluate the content of the EU and US regulatory governance regimes concerning AI. All countries have laws governing competition, fair trading, consumer protection, content censorship, copyright protection, and similar subjects to protect consumers, even in the event of there being no specific AI regulations or policies. These apply equally to firms providing AI applications as to any other commercial or other offerings. And the f irms developing AI applications have also engaged in extensive voluntary industry self-governance (i.e., self-regulation). At an international level, large developer firms and other stakeholders have actively engaged in civil society groups to develop and share standards, best practices, and other learnings. Examples include the AI Alliance (comprising Dell, IBM, Meta, Oracle, and many universities) and the AI Governance Alliance (including Amazon, ByteDance, Cisco, Google, Meta, Microsoft, and OpenAI). Given the high degree of technical knowledge required to develop AI systems, these entities are arguably better placed than government regulatory bodies to develop effective codes and standards and monitor and enforce compliance with them. These industry bodies have proved highly influential over history in developing and testing governance rules in emerging industries. Competition among such collectives surfaces information about which rules work best, thereby facilitating the subsequent incorporation of those proven successful into legislation (e.g., financial market regulation).53 Although people often express concerns about the use of industry self-regulation to protect incumbent members from competition provided by new entrants (membership of the “club” being a prerequisite for 14REGULATING ARTIFICIAL INTELLIGENCE IN A WORLD OF UNCERTAINTY                            BRONWYN HOWELL     industry participation), these risks are mitigated in new industries by the presence of multiple collectives among which new entrants can choose. Arguably, the risks to competition and innovation are greater when a single regulatory agency imposes a single, high-cost set of rules that incumbents have already met but that become a barrier to new entrants. And while some express concerns that industry self-governance may be used to exploit consumers for commercial success, the interests of end users must be mostly aligned with those of the developers; what is good for consumers is profitable for deployers. Again, allowing consumer choice among developers and governance regimes mitigates this risk. The EU AI Act and EU AI Liability Directive. The EU AI Act was first proposed in 2022 and signed into law in February 2024. It draws heavily on a long EU history of product-safety regulation and the objective of creating a single, harmonized market across the European Union, in which products and services can be freely traded. It builds on the foundation created by the General Data Protection Regulation (GDPR)54 and the EU Digital Markets Act (DMA),55 which focus strongly on the control and use of data. A key presumption of the DMA is that data are a key source of market power for large technology firms; the GDPR aims to empower consumers’ control over how and when their data can be used.56 Content. The EU AI Act defines and binds developers, deployers, importers, distributors, and operators of AI applications available for use in the European Union, regardless of where the application is developed or hosted. This is intended to ensure protection from harm for all application users within the EU. The starting assumption is that while “most AI systems pose limited to no risk and can contribute to solving many societal challenges, certain AI systems create risks that we must address to avoid undesirable outcomes.”57 Risk is defined as “the combination of the probability of an occurrence of harm and the severity of that harm.”58 “AI system” means a machine-based system designed to operate with varying levels of autonomy and that may exhibit adaptiveness after deployment and that, for explicit or implicit objectives, infers, from the input it receives, how to generate outputs such as predictions, content, recommendations, or decisions that can influence physical or virtual environments.59 The EU claims its regulatory framework is risk based. It identifies four levels of risk (Figure 4). Systems deemed to pose unacceptable risk are banned. Banned systems include subliminal techniques to impair decision-making, exploiting vulnerabilities to cause users to harm others, social scoring, predicting criminal behavior, facial recognition from internet scraping, inferring emotions in the workplace or an educational setting, and real-time biometric identif ication in public places.60 Systems posing high risk are subject to strict obligations and must be approved by the EU Commission’s AI Office (for general- purpose AI applications—namely, GAIs) or national regulatory bodies (for other applications) before being made available in the EU. Limited risk refers to risks associated with a lack of transparency in AI usage. Providers of all AI applications are required to ensure that AI-generated content is identifiable. The transparency requirement is intended to allow users to make an informed decision about using the application or content.61 Unrestricted use of minimal-risk applications is permitted so long as they meet the transparency obligation. It is likely the majority of AI applications will be in  this category. The definition of high-risk AI systems is complex and determined predominantly by the use case. These include the following: • Critical infrastructures (e.g., transport), which could put citizens’ lives and health at risk; • Educational or vocational training, which may determine access to education and the professional course of someone’s life (e.g., scoring of exams); 15REGULATING ARTIFICIAL INTELLIGENCE IN A WORLD OF UNCERTAINTY                            BRONWYN HOWELL     • Safety components of products (e.g., AI applications in robot-assisted surgery); • Employment, management of workers, and access to self-employment (e.g., resume-sorting software for recruitment procedures); • Essential private and public services (e.g., credit scoring that could deny citizens the opportunity to obtain  a loan); • Law enforcement, which may interfere with people’s fundamental rights (e.g., the evaluation of the reliability of evidence); Figure 4. The EU Risk-Based Approach Source: European Commission, “AI Act,” August 8, 2024, https://digital-strategy.ec.europa. eu/en/policies/regulatory-framework-ai. • Migration, asylum, and border control management (e.g., the automated examination of visa applications); and • Administration of justice and democratic  processes (e.g., AI solutions to search for  court rulings).62 Providers considering offering an AI system referred to in the act’s Annex III that does not pose high risk (e.g., it performs only a narrow procedural task or does not replace human decision-making) must document the assessment of the system and register themselves and the system in the EU database before making it available. All other high-risk systems are subject to strict obligations before being put on the market. The requirements for high-risk systems include the following: • The provider must have a comprehensive, continuous, and iterative risk management system that E Identifies and analyzes known and reasonably foreseeable risks to health, safety, and fundamental rights; E Estimates and evaluates the risks under the intended purpose and any reasonably foreseeable misuse; and E Includes measures for post-market evaluation and management. • The system should eliminate or reduce relevant risks as much as technically feasible, and when appropriate, the provider should implement adequate mitigation and control measures when they cannot be eliminated. • The provider must document 16REGULATING ARTIFICIAL INTELLIGENCE IN A WORLD OF UNCERTAINTY                            BRONWYN HOWELL     E Technical knowledge, experience, education, and training and the presumable context in which the system is intended to be used; and E Data governance, including quality criteria (e.g., design choice, collection, preparation, formulation of assumptions, availability, biases, and shortcomings) and representativeness. • The provider must detail technical specifications, including E Hardware and software specifications; E Documentation of the design process, development, oversight and testing, and monitoring and performance-metric records; and E Recordkeeping (e.g., event logs, data, and natural persons involved). • The provider must be transparent about contact persons, characteristics (including purpose, metrics, data specifications, and instructions on interpretation of outputs), and oversight measures. • The system must document human-oversight specifications. • The system must include accuracy, robustness, and cybersecurity provisions. Providers must also document and operate a quality management system, keep documentation for at least 10 years, immediately investigate problems and disable or withdraw the system as appropriate, and cooperate with EU authorities. All systems must have named representatives or authorities appointed for ensuring EU conformity; these individuals will be held liable in the event of breaches. The EU Liability Directive specifies strict liability for failure to comply with regulations in the event of harm. Ex ante certification of compliance from one of the member state authorities must be obtained before the AI system can be made available for use. All member states are required to have processes for overseeing and issuing this certification. They must also operate regulatory sandboxes for testing; priority access to sandboxes will be given to new entrants to facilitate competitive entry. Separate provisions apply for AI systems deemed to be GAI models with systemic risk.63 These models are defined by compute power used during training (greater than 1025 floating-point operations) or an EU Commission assessment. These provisions include additional model evaluation and testing obligations, documentation and reporting requirements, and requirements to abide by yet-to-be-determined codes of practice.64 The act excludes open-source AI models from some obligations, provided that the applications are not monetized. Open-source general-purpose models are not required to provide technical documentation to the AI Office unless they pose a systemic risk. This exemption recognizes the collaborative nature of open-source development and is aimed at avoiding stifling innovation in this sector.65 Systems developed for scientific research and development are also exempt from many requirements. Analysis. While the EU claims the AI Act is risk based, the risk classification is not based on ISO 31000 principles. Under the EU Act, most AI applications won’t be subject to any regulatory obligations other than disclosure. Not even basic risk management practices are required of those who develop and deploy them. Identifying unacceptable and high-risk applications is somewhat arbitrary, based on specific use cases, sectors, and application types. There is no systematic means of identifying risk based on either the magnitude of harm or probability of occurrence, as is expected in traditional risk management. Using compute power as a proxy for defining general- purpose and high-systemic-risk applications is also grossly imperfect. There is no reason to believe that systems trained with 1024 flops are any safer than those trained with 1025. Some large applications may 17REGULATING ARTIFICIAL INTELLIGENCE IN A WORLD OF UNCERTAINTY                            BRONWYN HOWELL     pose no real prospect of harm but will be caught up in costly compliance obligations. There is a real cost to end users, in that these applications will take longer to get to market (i.e., the opportunity cost of lost benefits), and the higher costs will translate into higher prices for use. The requirements for high-risk system documentation resemble those for ISO 31000, but it is not clear that these will help in either identifying or preventing harm arising from truly unexpected events. The adequacy of both the documentation and assessment of systems as high-risk by the certifying authorities relies on their (human) assessment and selection of the scenarios for all of risk identification, assessment, treatment, and monitoring. The selection of scenarios for risk management will be subject to the usual human biases, with overemphasis on managing the risks of high-cost, low- probability outcomes at the expense of lower-cost, higher-probability outcomes. This applies to both application developers and regulators. If a harmful outcome has not been evidenced in the past or has not been contemplated by these human actors in their anticipation of the future, then it will not be included. Thus, outcomes with aggregate material harm may not even form part of the active risk management processes. These scenarios can be included in the risk management processes only after the harm has been incurred. This highlights that, no matter how much it might be promulgated that the provisions will “keep end users safe,” real harms will occur because these scenarios have not been part of the initial and certified processes. The use of regulations to engender trust will also be compromised: Once information about these harms is communicated widely, confidence in both AI and the regulations will fall. To some extent, automated risk management systems canvas a wide range of possible outcomes, including the ones that occur. But if the future occurrence is in no way dependent on any past observations, then even these systems will not be able to assign an appropriate probability to the event. The regulatory processes cannot be relied on to protect end users from these harms eventuating. Red teaming (i.e., hiring individuals unconnected with a system’s development to test it in situations inside and outside its design parameters) and testing systems in sandboxes have been proposed as means of identifying harmful outcomes that developers may have overlooked. However, these too are constrained by limits to human experience. Red teams tend to be biased in their efforts toward testing for more recently experienced harms (e.g., a current focus on testing cybersecurity stresses appears to dominate red teaming). The requirement for national regulatory bodies to prioritize limited sandbox spaces to new entrants, rather than focusing on principled risk assessment, means resources are likely to be spent on applications with eventually small or insignificant reach. Meanwhile, high-impact applications with real benefits from existing providers could be delayed because regulators operating the sandboxes are not able to sufficiently demonstrate that certification is warranted. The exemption for open-source models is of particular interest. There is a long history of new, innovative, and highly successful applications coming from this community. In part, this may be because individuals in this community are not necessarily constrained by past uses in considering new applications. The unique qualities of GAIs (which count as EU general-purpose systems) mean they can be used in a wide range of ways that have not yet been contemplated. Complex AI systems, the outputs of which are unpredictable, are incorporated with complex human systems that are themselves not well understood. Given these dynamics, the exemption for open-source systems means that the probability of an unexpected event occurring is much higher in this exempted space than in the regulated space. On the one hand, exemption from regulation mitigates the risk of these applications’ developers being strictly liable for the harms caused, as is the case for their regulated counterparts (which will encourage more innovation, as the exemption intends). On the other hand, without a requirement to undertake at least some rudimentary risk management activities, it may prove difficult to determine whether the harms 18REGULATING ARTIFICIAL INTELLIGENCE IN A WORLD OF UNCERTAINTY                            BRONWYN HOWELL     arose from developer negligence or a truly random event. Moreover, assigning responsibility for compensation may be problematic. By way of illustration, consider Facebook. It was developed using open-source tools in a college dorm, and its initial use was not commercial. Its algorithms, prioritizing the distribution of content, were developed and tested in this context. Real harms associated with the use of the tool for bullying and sharing fake news undoubtedly took place during this stage. Yet the developers did not focus on them because they were not yet seen as meaningful risks to be managed in the context of a college dorm. (They might have been if the developer had been the university itself, but only if these were university governance priorities at the time.) If Facebook were commercialized today, then these risks would be addressed because of subsequent learning. Some new applications developed in similar circumstances today will almost certainly exacerbate negative effects of human behavior that will not necessarily be identified at the present because of lack of awareness or understanding among those responsible. Sometimes these effects become evident only as emergent behavior and new and different user communities engage with the application. It is difficult in these circumstances to determine exactly who is responsible for the harms that arise. Arguably, the fact that the EU AI Act explicitly exempts open-source developers could be seen as an implicit acceptance of some degree of regulatory responsibility for these truly unexpected outcomes. US Federal-Level AI Governance Initiatives. To date, no explicit legislation governing AI has been implemented at the federal level in the US. The White House issued its Executive Order on the Safe, Secure, and Trustworthy Development and Use of Artificial Intelligence in October 2023.66 This led to the OMB issuing instructions for government departments and agencies on March 28, 2024.67 The NIST, as part of the Department of Commerce, prepared the initial AI risk management standards in its January 2023 Risk Management Framework.68 This was followed on July 26, 2024, with a set of risk management standards for GAIs,69 partly in fulfillment of an obligation under the executive order. The National Telecommunications and Information Administration (NTIA), also part of the Department of Commerce, produced a set of accountability policy guidelines in March 2024, which address disclosure and auditing of AI applications to assure that extensive risk management processes are implemented and maintained to acceptable standards.70 The NIST and NTIA frameworks, though not mandatory, have become a de facto standard for US AI development and deployment. They have been formulated following a detailed and open consultation process among industry stakeholders.71 Extensive stakeholder participation in these processes has helped ensure the frameworks developed have wide support across both developer and end user communities. They largely reflect the risk management processes adopted by large developers as part of their own internal governance responsibilities (e.g., Microsoft).72 Voluntary compliance is widespread. The codes of practice developed by industry self-governance entities such as the AI Alliance and the AI Governance Alliance build on this base. Undoubtedly, the European standardization organizations will be informed by NIST standards when formulating and harmonizing their standards.73 Content. The NIST Risk Management Framework closely follows the ISO 31000 risk management standards, and indeed it derives its definition of risk management from that source. The framework is “voluntary, rights-preserving, non-sector-specific,  and use-case-agnostic” to enable flexibility. (Emphasis in original.) It is intended to be practical and “adapt to the AI landscape as AI technologies continue to develop, and to be operationalized by organizations in varying degrees and capacities so society can benefit from AI while being protected from its potential harms.”74 The framework recognizes that the risks posed by AI systems may differ from those encountered in traditional software and information-based systems. It 19REGULATING ARTIFICIAL INTELLIGENCE IN A WORLD OF UNCERTAINTY                            BRONWYN HOWELL     Figure 5. The NIST Risk Management Framework: A Broad Scope AI System Harms AI System Dimension and Life Cycle Source: US Department of Commerce, National Institute of Standards and Technology, Artificial Intelligence Risk Management Framework (AI RMF 1.0), January 2023, Figures 1 and 2, https://nvlpubs.nist.gov/nistpubs/ai/NIST.AI.100-1.pdf. acknowledges that they are “trained on data that can change over time, sometimes significantly and unexpectedly, affecting system functionality and trustworthiness in ways that are hard to explain.” The AI systems’ complexity and the contexts in which they are deployed are known to make it “difficult to detect and respond to failures when they occur.”75 The framework acknowledges that the inherent socio-technical nature of AI systems is influenced by social dynamics and human behavior. It clearly articulates that AI risks—and benefits—can emerge from the interplay of technical aspects combined with societal factors related to how a system is used, its interactions with other AI systems, who operates it, and the social context in which it is deployed.76 In other words, it expects unexpected emergent behavior. Yet despite these risks making AI technology challenging to deploy and use, what is proposed is still a classical risk management system, albeit one that endeavors to emphasize human centricity, social responsibility, sustainability, and an awareness of the need to “think more critically about context and potential or unexpected negative and positive impacts.”77 The framework recognizes the necessity of a wide view for assessing the potential harms arising from AIs. It recognizes the difficulty of measuring risks, the availability of reliable metrics, the need to track emergent risks at different stages of the AI life cycle, and the challenges posed by third-party software, hardware, and data (Figure 5). However, it does not distinguish between the management of risk and decision-making in the face of uncertainty. Rather, it focuses on the characteristics of AI systems that make them trustworthy (Figure 6). It recognizes that trustworthiness is a social concept and will necessitate trade-offs across a wide spectrum of factors. A system will be only as trustworthy as its weakest characteristics. The definitions of the characteristics “safe” and “secure and resilient” come from ISO standards; “explainability and interpretability” recognize the distinction between describing the AI system’s mechanisms and understanding the meaning of the system’s outputs in its intended purpose; and “fair—with harmful bias managed” is broader than simple demographic balance, with a view to systemic, computational, statistical, and human-cognitive biases. 20REGULATING ARTIFICIAL INTELLIGENCE IN A WORLD OF UNCERTAINTY                            BRONWYN HOWELL     Figure 6. NIST AI Risks and Trustworthiness Source: US Department of Commerce, National Institute of Standards and Technology, Artificial Intelligence Risk Management Framework (AI RMF 1.0), January 2023, Figure 4, https://nvlpubs.nist.gov/nistpubs/ai/NIST.AI.100-1.pdf. The NIST framework is operationalized around four core components, mapping directly onto ISO 31000 activities: govern, map, measure, and manage (Figure 7). These are cataloged into 19 activity categories and  72 subcategories. A fundamental component of the NIST framework lies in accountability and transparency. This includes matters such as the provenance of training data and the easy availability of all relevant documentation for audit and assurance purposes. The March 2024 NTIA Artificial Intelligence Accountability Policy Report provides a comprehensive set of processes for auditing AI systems and evaluating the activities of the developer and operator firms (Figure 8). These, too, rely on the development of standards and benchmarks for both internal and external evaluation, including AI risk hierarchies; acceptable risks and trade-offs; performance of AI models, including for fairness, accuracy, robustness, reproducibility, and explainability; data quality, provenance, and governance; internal governance controls; stakeholder participation; security; internal Figure 7. The NIST AI Risk Management Framework Core Activities Source: US Department of Commerce, National Institute of Standards and Technology, Artificial Intelligence Risk Management Framework (AI RMF 1.0), January 2023, Figure 5, https://nvlpubs.nist.gov/nistpubs/ai/NIST. AI.100-1.pdf. documentation and external transparency; and testing, monitoring, and risk management. The OMB’s specific requirements for government departments and agencies78 specify transparency and accountability obligations in alignment with the NIST framework. Similarly to the EU AI Act, these require each entity or department to have nominated individuals to be held accountable for AI system development and operation and ensure appropriate accountability processes are followed. NIST, NTIA, and OMB acknowledge that standards for model performance may not yet be available and 21REGULATING ARTIFICIAL INTELLIGENCE IN A WORLD OF UNCERTAINTY                            BRONWYN HOWELL     Figure 8. NTIA Accountability Framework Source: US Department of Commerce, National Telecommunications and Information Administration, Artificial Intelligence Accountability Policy Report, March 2024, 38, https://www.ntia.doc.gov/sites/default/files/publications/ntia-ai-report-final.pdf. will continue to be developed as new systems emerge. Academic research into these is critical, and the OMB recognizes the key role that government funding can have in facilitating such research. The Stanford Institute for Human-Centered Artificial Intelligence has been foundational in developing an array of tests and benchmarks, reported annually since 2017, to assist in this measurement process.79 These tests have become de facto reporting standards for many existing AI systems. The ability for such development to continue relies on AI firms allowing academic researchers access to their systems to develop and conduct tests; AI firms’ willingness to do so stands as an added signal of their transparency and trustworthiness, compared with those taking a more proprietary approach. NIST specifically addressed the particular challenges of GAIs in the US context, as part of the requirements of the executive order, in the July 2024 Generative Artificial Intelligence Profile. The document defines risks that are novel or exacerbated by the use of GAIs. The Executive Order 14110 definition of generative AI is used: “The class of AI models that emulate the structure and characteristics of input data in order to generate derived synthetic content. This can include images, videos, audio, text, and other digital content.” While not all GAI is derived from foundation models, GAI generally refers to generative foundation models. The foundation model subcategory of “dual-use foundation models” is defined by Executive Order 14110 as “an AI model that is trained 22REGULATING ARTIFICIAL INTELLIGENCE IN A WORLD OF UNCERTAINTY                            BRONWYN HOWELL     on broad data; generally uses self-supervision; contains at least tens of billions of parameters; [and] is applicable across a wide range of contexts.”80 The NIST GAI framework recognizes that some GAI risks are unknown, and are therefore diff icult to properly scope or evaluate given the uncertainty about potential GAI scale, complexity, and capabilities. Other risks may be known but difficult to estimate given the wide range of GAI stakeholders, uses, inputs, and outputs. Challenges with risk estimation are aggravated by a lack of visibility into GAI training data, and the generally immature state of the science of AI measurement and safety today.81 This document focuses on risks for which there is an existing empirical evidence base at the time this profile was written; for example, speculative risks that may potentially arise in more advanced, future GAI systems are not considered. Future updates may incorporate additional risks or provide further details on the risks identif ied below.82 It therefore knowingly limits the framework to the bounds of current knowledge, abstracting away from the consideration of true Knightian uncertainty. Nonetheless, the GAI framework identifies  214 specific action areas for managing GAI risks. (An earlier draft, in April 2024, identified 467.)83 This compares to 74 for standard AIs. Specific characteristics include confabulation; dangerous, violent, or hateful content; harmful bias and homogenization; ease of intellectual property infringement; obscene, degrading, or abusive content; and nontransparent or untraceable upstream third-party components in the value chain. Analysis. The US approach, unlike that in the EU, is truly risk management based. While compliance is voluntary, all AI developers and users are encouraged to develop awareness of the risks and their management processes, regardless of the size or characteristics of the application concerned. Providers that voluntarily comply and are open and transparent in their development, operation, and accountability processes will offer greater assurances of their systems’ trustworthiness. This approach encourages users to familiarize themselves with the assurance tools, so that they can make knowledgeable choices regarding the AI systems they use. However, this requires significant consumer education to be effective. The NTIA has also identified the pressing shortage of people with the requisite skills even for application development, risk management, and regulation and called for government scholarships, subsidies, and extensive in-house education and training to plug the gap.84 Just where consumer education sits within this call on resources is unclear. While there are no explicit regulations, the leadership exhibited by NIST, NTIA, and OMB should be commended. The wide stakeholder engagement follows best practices and has ensured that the resulting frameworks build on existing industry and civil society initiatives and have widespread support. However, the lack of a single set of standards has proved challenging for some developers, who report being required to meet multiple sets of subtly different obligations with attendant additional costs. The competition among different sets of standards, though, is arguably better given the technologies’ early stage of development and deployment, during which there is still much to learn about the applications and their possible uses.85 Clearly, US politicians have been under considerable pressure to implement legislative provisions. So far, this pressure has been averted, albeit with some intervention from the White House. However, this has favored a less intrusive and more collaborative industry-led approach. This has the advantage of being more flexible and capable of amendment and, if necessary, pivoting, should new information come to hand. The permissive approach is more likely to support an innovative environment in which new uses can be put through trials and deployed rapidly. While some have expressed concerns that this gives too much power to developers—and to Big Tech in particular—it must be recognized that the 23REGULATING ARTIFICIAL INTELLIGENCE IN A WORLD OF UNCERTAINTY                            BRONWYN HOWELL     resources required for GAI development are significant. These firms have invested billions of dollars in their applications and have much at risk in future earnings and reputation if they are found to be operating unethically. Creating a culture of transparency by design, as has occurred with the NIST and NTIA processes, has allowed specific companies to use their willingness to be open to scrutiny from independent evaluators as an additional signal of trustworthiness and governance quality. On the other hand, there are concerns about the apparent assurances these measures provide about the safety and trustworthiness of the applications, rather than the companies that develop and deploy them. The executive order claims in its title to assure “Safe, Secure, and Trustworthy Development and Use of Artificial Intelligence.” The risk management framework alone cannot deliver on this objective, especially regarding GAIs. While the governance of these applications seems to take a wide view of risk types, the NIST document expressly states that the risks addressed are only those of which there is currently knowledge and awareness. These systems cannot manage the risks of unknown, unexpected, and unanticipated outcomes that will arise from their use, especially because they are going to be used in the complex and uncertain areas of human interaction and are inherently unknowable and unexplainable in their operations. The extensive risk management activities undertaken will relate to and manage known risks only. They may succeed in averting some harms, but at high cost, as all activities must be observed and managed to avoid harms arising from a small subset of them. Additionally, all these monitored activities must be reviewed and audited to ensure the firms are compliant with the standards. And this will have no effect on reducing the probability of the harm arising from a truly unexpected event. The EU system is less costly, as only a handful of pre-certified systems will be required to undertake the detailed risk management processes incurred by all firms abiding by the US guidelines. More harms can be expected from the insufficient management of known risks by the majority of firms in the EU not subject to the risk management provisions; these harms will be less likely to arise in the US, but overall total administration costs of the processes will be higher. There will be a lot more information available from the US operations, allowing better refinement of what is known about the risks (of which humans are already aware and about which automated systems are already keeping data) and their subsequent management. The EU firms and regulators may gain some spillover benefits from this new knowledge. But neither system can claim that its governance arrangements will keep end users safe. The one certainty that exists is that we can expect unanticipated harms. This suggests that, in line with Ohlhausen’s prescription, a degree of humility is warranted for regulators and AI developers. It is dishonest of legislators and regulators to hold that AI regulations or even voluntary codes of practice will keep citizens safe, any more than road rules keep road users and pedestrians safe. Unexpected outcomes will occur in both contexts. The social biases monitored for in AI applications are those currently of interest. In the future, new biases will become important, but we do not know in advance what these will be, so AI systems programmed not to bias against people on demographic bases won’t have learned to avoid the newly preferred biases. Humans are unpredictable, and we don’t know all possible uses of a motor vehicle  (i.e., new ones continue to evolve as human ingenuity expands). The same can be expected with AI—and even more so with GAI, for which we can expect complex interactions of complex systems with emergent behaviors. At best, all that can be done is to manage the likelihood of events that we can anticipate. Developer firms have an obligation to be transparent about what they can reasonably foresee and, when possible, manage those risks responsibly. But there are limits to what they can do—both in terms of the costs of managing risks and the ability to actually prevent harms. Developers cannot prevent all harms; they should be held liable only for those that they could reasonably foresee and failed to manage adequately. There is little point in holding them accountable for outcomes they 24REGULATING ARTIFICIAL INTELLIGENCE IN A WORLD OF UNCERTAINTY                            BRONWYN HOWELL     could not reasonably foresee—as discussed above, doing so will have a chilling effect on innovation. While AI developers stand to benefit financially from their innovations, over the fullness of time, the majority of benefits arising from new technologies accrue to end users and society—in the form of higher standards of living from advances nearly impossible to imagine at the outset. Historically, society has shared the risks with developers as new technologies are deployed, learned about, and improved. Some of those learnings lead to better regulations, but for the most part, the gains have outweighed the costs and end users have managed their risks of harm sufficiently. Thus, end users must actively be part of the risk management activity, not just passive recipients (or victims) of its consequences. Other US State Legislation. For completeness, this section considers the state laws passed in Colorado and those passed in California except one—SB-1047— which was vetoed by the governor.86 In principle, both of these are constrained by the same limitations discussed for the EU AI Act and US federal arrangements. Both derive considerably from the EU AI Act. The Colorado act makes the same distinction between high-risk and other applications as the EU. Risk management provisions apply for high-risk applications, and disclosure applies for all applications. The obligations for deployers are more extensive, requiring them to notify end users when actual decisions are made using AI. The California acts and bills are also more extensive than the EU act. As for Colorado, more extensive disclosure is required when AI is used for automated decision-making, and individuals will be allowed to opt out of solely AI-based decisions when feasible. Companies would be required to watermark all AI-generated content and make decoders available to verify AI content. Algorithmic discrimination is specifically targeted: Those who develop and deploy algorithms would be prohibited from using or making available automated decision tools that result in algorithmic discrimination. Advanced AI applications would be tightly controlled, with a special regulatory division overseeing their development and training. The Colorado act has been passed, albeit with the governor expressing reservations upon signing, urging the legislature to “fine tune the provisions and ensure that the final product does not hamper development and expansion of new technologies in Colorado that can improve the lives of individuals.”87 Controversially, the California governor vetoed SB-1047, sending it back to the senate, because it failed to take into account “whether an Al system is deployed in high-risk environments, involves critical decision-making or the use of sensitive data.” He asserted that while a California-specific set of regulations might be warranted, “it must be based on empirical evidence and science.”88 Consistent with the PP and safety regulation principles, uncertainty associated with a new technology is not sufficient to justify regulation without an assessment of both the risks and the benefits. In both cases, some have expressed concern that the provisions make the states comparatively less attractive for AI developers and deployers compared with states without explicit regulations—and that f irms might migrate accordingly. This is of especial concern for some California legislators and firms, given the state economy’s reliance on innovation from Silicon Valley.89 Some advocates saw passing a state act as a means of putting pressure on the federal government to take explicit action. Neither appeared to be cognizant of the limitations of risk management in a context of uncertainty. A Way Forward? The preceding three sections have demonstrated that while risk management processes and regulations may be suitable for narrowly defined applications, constrained populations, and cases in which increased precision rather than greater variety is the objective, these conditions do not apply to the development and deployment of GAIs. Rather, the degree of uncertainty associated with these applications’ potential uses and the emergence of truly unexpected outcomes when complex GAIs interact with complex, poorly understood human systems suggests that, as 25REGULATING ARTIFICIAL INTELLIGENCE IN A WORLD OF UNCERTAINTY                            BRONWYN HOWELL     identified above, the one certainty that exists is that we can expect unanticipated harms. At best, one would hope that legislators and regulators will explicitly recognize their limitations when it comes to regulating these applications to “keep people safe” and “engender trust in AI.” This is not realistic for GAIs. Instead, legislators and regulators should avoid taking actions that engender a false sense of assurance or safety in end users. This would be an irresponsible use of regulation. Rather, some regulatory humility is required. At the least, legislators and regulators should ensure that the end user population is as educated as possible about the technologies and how to responsibly interact with them. Also, they should limit regulatory intervention to specific use cases in which there is some knowledge of the interaction of complex human and AI systems (e.g., in medical research for the generation of new protein types), which may be more helpful in limiting harm than overarching, generic AI rules. A Societal Problem. When such great uncertainty exists, there is a real risk that regulatory arrangements holding developers to account for outcomes that neither they nor anyone else could have foreseen will have a chilling effect on innovation. Society is the largest loser because when no one can predict whether an application will be benign or harmful, the risk is too great for the developer to take it to market. Extreme precaution is extremely harmful in terms of lost benefits; extreme caution by monitoring each and every step is also extremely costly, as costs are incurred in closely monitoring applications that did not need to be so closely observed, meaning the successful applications cost substantially more to take to market than was necessary. If society is a major beneficiary of the successful applications, then a proportionate or just system would efficiently share the risks arising from uncertainty among the parties concerned. As it stands in the EU, under the strict liability imposed in the EU AI Liability Directive, the presumption is that harms are the developer’s responsibility, unless it can be demonstrated that they have not been negligent. This applies to all high-, low-, and minimal-risk applications, even though the latter two face less rigorous regulatory obligations. In the US, tort law developed from product-safety cases would likely similarly hold the developer accountable in the first instance, though the process of determining whether negligence occurred would be highly contentious. In effect, the status quo ante is that in both jurisdictions, in the event of harm, developers will be held guilty until proven innocent. Given the levels of uncertainty involved, the likelihood of a developer being found liable will be something of a lottery. If it is known in advance that this will be the likely outcome of harm inevitably occurring, then the relevant question to address is not about apportioning liability or blame in the first instance but determining compensation for those harmed. I presume here that restitution will not likely be possible, because the harms caused will likely be irreversible. But that does not preclude the development of institutional arrangements to enable some form of redress. Historically, in such circumstances, society has looked to insurance arrangements to manage the cost incurred by harm from truly unpredictable outcomes. Rather than holding motor vehicle manufacturers accountable for the harms to others from motorists’ accidental or deliberate misuse of the vehicle (which we cannot predict precisely in advance), we require motorists to purchase public liability insurance to pay compensation for harm when it occurs. The premiums are paid by those in control of the vehicle. Motor vehicle manufacturers also purchase insurance (or are sufficiently large to self-insure) against the risks of unanticipated harm arising due to their design or manufacturing processes. In this way, third parties are assured that if they are unlucky enough to be harmed, there will be compensation available. In some arrangements, this compensation is made available to those harmed on a no-fault basis, ensuring harm can be compensated even though it may take time for legal proceedings to clarify which fund or individual should ultimately be liable. 26REGULATING ARTIFICIAL INTELLIGENCE IN A WORLD OF UNCERTAINTY                            BRONWYN HOWELL     An Institutional Solution? Why, then, could an insurance arrangement not be contemplated for compensating truly unexpected harms arising from the deployment of AI systems, particularly GAIs? The immediate problem is that, in the short to medium term, the information necessary to price such arrangements is not available. It is constrained by the same uncertainty as the AI systems themselves. Private markets will not develop to manage these risks (as they have for motor vehicles) until a sufficiently large number of incidents have occurred. We see that at play even now in embryonic cybersecurity insurance markets. Despite some 30 years of experience in the use of open internet systems, the requisite information for pricing such contracts is still not satisfactory. In part, this is due to the high costs and low probabilities associated. It is also complicated by the environment constantly changing as new software platforms bring new vulnerabilities into play and human actors are constantly responding to the changes with increasingly innovative responses. However, such uncertainties have not prevented the development of insurance funds to compensate for losses due to other high-cost and low-probability events, such as earthquakes. New Zealand, for example, has a state-owned and -managed insurance fund to address the risks of earthquake damage to land and other associated losses (over and above the damage to built infrastructure, which is expected to be covered by private insurance). Premiums paid into this fund are shared between the government and property owners. Property owners pay via a levy collected through insurance premiums; government pays via an annually budgeted sum. A process is determined ex ante as to how the fund will be disbursed in the event of an earthquake. While administrative processes to make the payments must be established after the event, as much certainty as possible is provided ex ante about access to the funds. This has the effect of reducing  uncertainty for individuals when making decisions about investing in businesses and homes in earthquake-prone locations. Without access to such insurance, it may prove too risky for investments to take place, and the New Zealand economy is the loser. The fund goes some way to manage the uncertainties about harm from low-probability, high-cost earthquakes. How might such a fund work in an AI environment? First, the fund’s costs should be shared between society and application developers, not borne by application developers alone. As the majority of benefits from successful AI systems will be nonmonetary benefits or benefits to society that are difficult to quantify, some of the risks of developing and deploying them must be borne by society. It is not just to impose the full cost onto developers. Second, such a fund should not reduce developers’ responsibilities for taking due care given the current state of knowledge when bringing a new application to market. The fund should pay out only when it is demonstrated that all reasonable steps were taken to manage known risks. Third, given the multinational application of AI systems, the fund’s administration must cross national boundaries. This suggests administration at an international rather than national level for the process of collecting premiums and making disbursements.  Importantly, such a fund would free GAI developers from the need to constrain downstream uses of their applications to manage liability for unanticipated uses, in the same manner as insurance frees motor vehicle manufacturers from responsibility for decisions taken by vehicle owners who cause harm to third parties. Under current arrangements, GAI developers must manage for uses not anticipated when allowing third parties to use and build on their applications. As long as they are likely to be held liable in the event of harm from these downstream uses, they are best protected by either maintaining development in-house or maintaining strict contractual control over future uses. This will have distinctly chilling effects on competition and innovation. But with an insurance provision in place, so long as all due care is taken, the risks of liability are reduced. New uses will then occur (albeit with their developers also being liable to contribute to the fund), but 27REGULATING ARTIFICIAL INTELLIGENCE IN A WORLD OF UNCERTAINTY                            BRONWYN HOWELL     with the costs of downstream monitoring and competition constraints reduced.90 I strongly recommend that such an insurance fund be investigated, if not in respect of all AIs collectively, then at least in respect of specific AIs in specific contexts. Transparency for Model Assessment. Given that it is impossible ex ante to know the outcomes of the intersection of GAIs and human systems, but also that expert knowledge is required to undertake assessment, then independent assessment of GAIs— for example, by expert university laboratories—must be considered. While regulatory sandboxes provide some information, as their outcomes form part of a regulatory process, they cannot be truly independent. However, allowing universities and similar third parties access to the models and the data on which they were trained will make it possible to test and assess the models and develop new tests and benchmarks for performance against safety and other objectives. On the one hand, in a voluntary compliance context, developers with little to fear will have no problem making such access available. Indeed, their doing so is an additional signal of the quality of their intentions and their confidence in the quality of their own processes. On the other hand, if such voluntary transparency is not forthcoming, then there may be some merit in considering whether regulatory provisions to make models and data available should be implemented. Conclusion This report has investigated how uncertainty affects the ability to effectively regulate AI systems and reduce potential harms. The first section explored the distinction between risk and uncertainty. Whereas classical risk management requires the ability to define and quantify both the probability and occurrence of harm, in situations of uncertainty, neither of these can be adequately defined or quantified, particularly in the case of GAIs. Management and decision-making under uncertainty differ from management and decision-making under risk. When faced with uncertainty, humans tend to make simplifying biases that can lead to unsatisfactory outcomes. This includes substituting a problem for which answers or solution methods are known for the problem to which the answer is unknown and substituting regulations used for situations for which outcomes are predictable for those for which they are not. The first section also identified complex and chaotic contexts in which we cannot assume that cause-and-effect relationships are known or understandable. In these contexts, regulation and management strategies must consider that it is impossible to rely on thinking based on cause-and-effect relationships. It is impossible to predict in advance what outcomes will occur; at best, it may be possible to explain ex post what has occurred. The second section explored the classic risk management systems historically used to govern product safety, assuming known and predictable scientific principles and a narrow range of identifiable subjects. These situations have been largely applicable in the development of computer systems based on predictable scientific and engineering principles. This includes mathematically tractable systems known as “good, old-fashioned AI,” including most big-data models. However, GAIs do not conform to the assumptions of classical risk management. Rather, they are characterized by the intersection of complex AI systems, which have unknown and unpredictable outcomes, with complex human systems, which have unknowable and unpredictable outcomes. Historic risk management systems are unlikely to safeguard end users and society from unexpected harms. The third section discussed the regulatory and governance arrangements for AI development and deployment in the EU and US and evaluated their ability to prevent the outcomes identified in the second section. The EU arrangements do not conform to classical risk management principles, in that only a handful of applications in high-risk use cases are required to undertake complete risk management activities. We should expect unexpected harms, especially in the 28REGULATING ARTIFICIAL INTELLIGENCE IN A WORLD OF UNCERTAINTY                            BRONWYN HOWELL     application of open-source models, which are exempt from most risk management obligations. The US arrangements, although not mandatory, do follow standard risk management processes. Firms following the US guidelines will provide greater assurances and harm reduction than those following the EU regulations. However, the costs of compliance will be higher. Neither set of arrangements is well suited to managing the unexpected outcomes arising from GAI deployment and use. Consequently, we should expect unexpected outcomes—and harms. The fourth section outlines some measures that could enable the development and deployment of GAI models in as competitive and beneficial an environment as possible. First, regulators need to be honest about their limitations in regulating to prevent harm and engender confidence in AI systems. They should focus on educating end users and society about the AI environment and their role in managing personal exposure. However, there may also be some benefit in considering the extent to which GAI developers make their models and training data available to independent third parties for evaluation. If voluntary cooperation is not sufficient, mandatory rules may be needed. However, given that we can expect unexpected harms, regulators should consider establishing an insurance fund or funds and  associated governance—potentially at an international level—to enable compensation when inevitable harms arise. Such a fund, contributed to by all stakeholders—developers, users, and governments— would spread the risks across all participants and ensure compensation is paid when harm arises. With appropriate adjudication, developers and deployers can ensure they are not held liable if they have taken all possible measures given the state of knowledge at the time of deployment to avoid known risks. Such an arrangement would contribute to a more vibrant and competitive development environment, as opposed to arrangements in which developers alone are held responsible for outcomes in which it may prove difficult to assign responsibility. It would also provide assurances to end users and society not possible under regulatory arrangements alone. Just as when the motor vehicle was first developed, we are on the cusp of a range of new technologies that will be equally or even more transformative. We can look to the past for guidance, but ultimately, we are going on a journey into the unknown. We need to know that we will face the unexpected. We must become more comfortable about knowing that human advancement comes from facing the unexpected when it occurs and learning from it. Not taking a journey because we cannot be assured that no harm will occur is to guarantee no progress is made.", "metadata": {"original_filename": "item176_US_Regulating Artificial Intelligence in a World  of Uncertainty.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T22:21:34.527288", "updated_at": "2025-08-28T22:21:34.527288", "word_count": 56994}