// 全局变量
let historyData = [];
let uploadedFiles = [];

// DOM元素
const analyzeBtn = document.getElementById('analyze-btn');
const docTitle = document.getElementById('doc-title');
const docContent = document.getElementById('doc-content');
const resultsSection = document.getElementById('results-section');
const resultsContainer = document.getElementById('results-container');
const loading = document.getElementById('loading');
const historyList = document.getElementById('history-list');
const fileUpload = document.getElementById('file-upload');
const folderUpload = document.getElementById('folder-upload');
const fileList = document.getElementById('file-list');
const apiKeyInput = document.getElementById('api-key');
const baseUrlInput = document.getElementById('base-url');
const modelSelect = document.getElementById('model-select');
const customModelInput = document.getElementById('custom-model');
const saveConfigBtn = document.getElementById('save-config-btn');
const resetConfigBtn = document.getElementById('reset-config-btn');
const batchConfig = document.getElementById('batch-config');
const concurrencySlider = document.getElementById('concurrency-slider');
const concurrencyValue = document.getElementById('concurrency-value');

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    loadHistory();
    analyzeBtn.addEventListener('click', analyzeDocument);
    fileUpload.addEventListener('change', handleFileUpload);
    folderUpload.addEventListener('change', handleFolderUpload);

    // 并发控制滑块事件
    if (concurrencySlider) {
        concurrencySlider.addEventListener('input', (e) => {
            concurrencyValue.textContent = e.target.value;
        });
    }

    // 模型选择事件
    if (modelSelect) {
        modelSelect.addEventListener('change', handleModelChange);
    }

    // 配置保存按钮事件
    if (saveConfigBtn) {
        saveConfigBtn.addEventListener('click', saveConfiguration);
    }

    // 配置重置按钮事件
    if (resetConfigBtn) {
        resetConfigBtn.addEventListener('click', resetConfiguration);
    }

    // 高级分析按钮事件
    const advancedAnalysisBtn = document.getElementById('advanced-analysis-btn');
    if (advancedAnalysisBtn) {
        advancedAnalysisBtn.addEventListener('click', runAdvancedAnalysis);
    }

    // 批量高级分析按钮事件
    const batchAdvancedBtn = document.getElementById('batch-advanced-btn');
    if (batchAdvancedBtn) {
        batchAdvancedBtn.addEventListener('click', runBatchAdvancedAnalysis);
    }

    // 加载保存的配置
    loadSavedConfiguration();
});

// 处理单文件上传
async function handleFileUpload(event) {
    const file = event.target.files[0];
    if (file) {
        await processFile(file);
    }
}

// 处理文件夹上传
async function handleFolderUpload(event) {
    const files = Array.from(event.target.files);
    uploadedFiles = [];
    fileList.innerHTML = '';
    
    for (const file of files) {
        // 处理支持的文件格式
        const supportedExtensions = ['.txt', '.md', '.doc', '.docx', '.pdf'];
        const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

        if (supportedExtensions.includes(fileExtension)) {
            await processFile(file, true);
        }
    }
    
    if (uploadedFiles.length > 0) {
        const message = `成功加载 ${uploadedFiles.length} 个文件，点击"开始分析"进行批量处理`;
        showMessage(message, 'success');

        // 显示批量处理提示
        const batchHint = document.createElement('div');
        batchHint.className = 'batch-hint';
        batchHint.innerHTML = `
            <p>📁 已加载 ${uploadedFiles.length} 个文件，准备进行批量分析</p>
            <p>💡 提示：点击下方的"开始分析"按钮将对所有文件进行批量处理</p>
        `;
        fileList.appendChild(batchHint);

        // 显示批量配置选项
        if (batchConfig) {
            batchConfig.style.display = 'block';
        }

        // 显示批量高级分析按钮
        const batchAdvancedBtn = document.getElementById('batch-advanced-btn');
        if (batchAdvancedBtn) {
            batchAdvancedBtn.style.display = 'inline-block';
        }
    } else {
        showMessage('没有找到支持的文件格式（.txt, .md, .doc, .docx, .pdf）', 'warning');
    }
}

// 处理文件
async function processFile(file, isMultiple = false) {
    const reader = new FileReader();
    
    return new Promise((resolve) => {
        reader.onload = (e) => {
            const content = e.target.result;
            const fileName = file.name;
            const title = fileName.replace(/\.[^/.]+$/, ''); // 移除扩展名作为标题
            
            if (isMultiple) {
                // 多文件模式，添加到列表
                uploadedFiles.push({ title, content, fileName });
                addFileToList(fileName, uploadedFiles.length - 1);
            } else {
                // 单文件模式，直接填充到表单
                docTitle.value = title;
                docContent.value = content;
                showMessage(`文件 "${fileName}" 已加载`, 'success');
            }
            resolve();
        };
        
        reader.onerror = () => {
            showMessage(`无法读取文件: ${file.name}`, 'error');
            resolve();
        };
        
        reader.readAsText(file, 'UTF-8');
    });
}

// 添加文件到列表显示
function addFileToList(fileName, index) {
    const fileItem = document.createElement('div');
    fileItem.className = 'file-item';
    fileItem.innerHTML = `
        <span class="file-name">${fileName}</span>
        <span class="remove-btn" onclick="removeFile(${index})">×</span>
    `;
    fileItem.onclick = () => selectFile(index);
    fileList.appendChild(fileItem);
}

// 选择文件
function selectFile(index) {
    const file = uploadedFiles[index];
    if (file) {
        docTitle.value = file.title;
        docContent.value = file.content;
        showMessage(`已选择: ${file.fileName}`, 'success');
    }
}

// 移除文件
function removeFile(index) {
    uploadedFiles.splice(index, 1);
    updateFileList();
}

// 更新文件列表显示
function updateFileList() {
    fileList.innerHTML = '';
    uploadedFiles.forEach((file, index) => {
        addFileToList(file.fileName, index);
    });
}

// 分析文档
async function analyzeDocument() {
    // 保存API密钥（如果提供）
    const apiKey = apiKeyInput.value.trim();
    if (apiKey) {
        localStorage.setItem('zhipuApiKey', apiKey);
    }
    
    // 保存Base URL（如果提供）
    const baseUrl = baseUrlInput.value.trim();
    if (baseUrl) {
        localStorage.setItem('zhipuBaseUrl', baseUrl);
    }
    
    // 获取输入数据
    const title = docTitle.value.trim();
    const content = docContent.value.trim();

    // 检查是否有上传的文件
    if (uploadedFiles.length > 0) {
        console.log(`检测到 ${uploadedFiles.length} 个上传的文件`);

        // 如果有上传的文件，使用第一个文件或合并所有文件
        if (uploadedFiles.length === 1) {
            console.log('单个文件模式');
            // 单个文件，使用文件的标题和内容
            const file = uploadedFiles[0];
            if (!title && file.title) {
                docTitle.value = file.title;
            }
            if (!content && file.content) {
                docContent.value = file.content;
            }
        } else {
            console.log('多个文件模式，启动批量处理');
            // 多个文件，使用批量处理
            await processBatchAnalysis();
            return;
        }
    }

    // 重新获取可能更新的数据
    const finalTitle = docTitle.value.trim();
    const finalContent = docContent.value.trim();

    // 验证输入
    if (!finalTitle) {
        showMessage(t('errors.noDocumentTitle'), 'error');
        return;
    }

    if (!finalContent) {
        showMessage(t('errors.noDocumentContent'), 'error');
        return;
    }
    
    // 获取选中的任务
    const tasks = [];
    document.querySelectorAll('input[name="task"]:checked').forEach(checkbox => {
        tasks.push(checkbox.value);
    });
    
    if (tasks.length === 0) {
        showMessage(t('errors.noTaskSelected'), 'error');
        return;
    }
    
    // 显示加载状态
    resultsSection.style.display = 'block';
    loading.style.display = 'block';
    resultsContainer.innerHTML = '';
    
    try {
        // 生成文档ID
        const docId = `doc_${Date.now()}`;
        
        // 构建请求头
        const headers = {
            'Content-Type': 'application/json'
        };
        
        // 如果提供了API密钥，添加到请求头
        if (apiKey) {
            headers['X-API-Key'] = apiKey;
        }
        
        // 获取当前选择的模型
        const selectedModel = getCurrentModel();

        // 创建文档
        const docResponse = await fetch('/api/v1/documents/', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify({
                doc_id: docId,
                title: title,
                text: content,
                metadata: {
                    source: 'Web UI',
                    created_at: new Date().toISOString(),
                    api_key: apiKey,  // 传递API密钥到后端
                    base_url: baseUrl,  // 传递Base URL到后端
                    model: selectedModel  // 传递选择的模型到后端
                }
            })
        });
        
        if (!docResponse.ok) {
            throw new Error('创建文档失败');
        }
        
        // 执行分析
        const analysisResponse = await fetch('/api/v1/analysis/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                doc_id: docId,
                tasks: tasks,
                model: selectedModel  // 传递选择的模型
            })
        });
        
        if (!analysisResponse.ok) {
            throw new Error('分析文档失败');
        }
        
        const results = await analysisResponse.json();
        
        // 隐藏加载状态
        loading.style.display = 'none';
        
        // 显示结果
        displayResults(results);
        
        // 添加到历史记录
        addToHistory(docId, title, tasks);
        
        // 保存当前文档ID到sessionStorage，供可视化页面使用
        sessionStorage.setItem('currentDocId', docId);
        sessionStorage.setItem('fromAnalysis', 'true');
        
        showMessage('分析完成！', 'success');
        
    } catch (error) {
        loading.style.display = 'none';
        showMessage(`错误: ${error.message}`, 'error');
        console.error('分析错误:', error);
    }
}

// 批量分析处理
async function processBatchAnalysis() {
    console.log('=== 进入批量分析函数 ===');
    try {
        console.log('开始批量分析，文件数量:', uploadedFiles.length);

        // 检查是否有文件
        if (uploadedFiles.length === 0) {
            showMessage('请先上传文件', 'error');
            return;
        }

        // 显示加载状态
        loading.style.display = 'block';
        analyzeBtn.disabled = true;
        analyzeBtn.textContent = t('common.loading');

        // 获取选中的任务
        const taskCheckboxes = document.querySelectorAll('input[name="task"]:checked');
        const tasks = Array.from(taskCheckboxes).map(cb => cb.value);

        console.log('选中的任务:', tasks);

        // 检查是否选择了任务
        if (tasks.length === 0) {
            showMessage('请至少选择一个分析任务', 'error');
            loading.style.display = 'none';
            analyzeBtn.disabled = false;
            analyzeBtn.textContent = t('analysis.startAnalysis');
            return;
        }

        if (tasks.length === 0) {
            showMessage(t('errors.noTaskSelected'), 'error');
            return;
        }

        // 首先上传所有文件并获取文档ID
        const documentIds = [];

        for (let i = 0; i < uploadedFiles.length; i++) {
            const file = uploadedFiles[i];
            console.log(`上传文档 ${i + 1}/${uploadedFiles.length}: ${file.fileName}`);

            // 创建文档数据
            const docData = {
                title: file.title,
                text: file.content,  // 使用 text 字段而不是 content
                metadata: {
                    original_filename: file.fileName,
                    upload_method: "batch_upload"
                }
            };

            // 添加API密钥到metadata（如果有）
            const apiKey = apiKeyInput.value.trim();
            if (apiKey) {
                docData.metadata.api_key = apiKey;
            }

            // 添加Base URL到metadata（如果有）
            const baseUrl = baseUrlInput.value.trim();
            if (baseUrl) {
                docData.metadata.base_url = baseUrl;
            }

            const docResponse = await fetch('/api/v1/documents/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(docData)
            });

            if (!docResponse.ok) {
                const errorText = await docResponse.text();
                console.error(`上传文档失败:`, errorText);
                throw new Error(`上传文档 "${file.fileName}" 失败: ${errorText}`);
            }

            const docResult = await docResponse.json();
            const docId = docResult.doc_id || docResult.id;
            documentIds.push(docId);
            console.log(`文档上传成功，ID: ${docId}`);
        }

        console.log('所有文档上传完成，开始批量分析...');
        console.log('文档IDs:', documentIds);

        // 获取并发设置
        const concurrency = concurrencySlider ? parseInt(concurrencySlider.value) : 3;
        console.log('并发数:', concurrency);

        // 获取当前选择的模型
        const selectedModel = getCurrentModel();

        // 启动批量分析
        const batchResponse = await fetch('/api/v1/batch/analyze', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                document_ids: documentIds,
                tasks: tasks,
                concurrency: concurrency,
                model: selectedModel  // 传递选择的模型
            })
        });

        if (!batchResponse.ok) {
            const errorText = await batchResponse.text();
            console.error('批量分析启动失败:', errorText);
            throw new Error(`批量分析启动失败: ${errorText}`);
        }

        const batchData = await batchResponse.json();
        console.log('批量分析已启动，作业ID:', batchData.job_id);

        // 显示批量分析状态
        showBatchProgress(batchData.job_id, documentIds.length);

        showMessage(`批量分析已启动，正在处理 ${documentIds.length} 个文件`, 'success');

    } catch (error) {
        console.error('批量分析失败:', error);
        showMessage(`批量分析失败: ${error.message}`, 'error');

        // 隐藏加载状态
        loading.style.display = 'none';
        analyzeBtn.disabled = false;
        analyzeBtn.textContent = t('analysis.startAnalysis');
    }
}

// 显示批量分析进度
function showBatchProgress(jobId, fileCount) {
    resultsContainer.innerHTML = '';
    resultsContainer.style.display = 'block';

    const progressContainer = document.createElement('div');
    progressContainer.className = 'batch-progress-container';
    progressContainer.innerHTML = `
        <h3>📊 批量分析进行中</h3>
        <p>正在分析 ${fileCount} 个文件...</p>

        <div class="progress-bar">
            <div class="progress-fill" id="batch-progress-fill" style="width: 0%"></div>
        </div>

        <div class="progress-details">
            <div class="progress-item">
                <div class="label">已完成</div>
                <div class="value" id="completed-count">0</div>
            </div>
            <div class="progress-item">
                <div class="label">处理中</div>
                <div class="value" id="processing-count">0</div>
            </div>
            <div class="progress-item">
                <div class="label">失败</div>
                <div class="value" id="failed-count">0</div>
            </div>
            <div class="progress-item">
                <div class="label">进度</div>
                <div class="value" id="progress-percentage">0%</div>
            </div>
            <div class="progress-item">
                <div class="label">并发数</div>
                <div class="value" id="concurrency-display">-</div>
            </div>
            <div class="progress-item">
                <div class="label">预估剩余</div>
                <div class="value" id="estimated-time">-</div>
            </div>
        </div>

        <div id="batch-status">准备中...</div>

        <div class="current-files">
            <div class="label">正在处理的文件：</div>
            <div id="current-processing-files"></div>
        </div>

        <button type="button" onclick="checkBatchStatus('${jobId}')" class="btn btn-primary">手动检查进度</button>
    `;
    resultsContainer.appendChild(progressContainer);

    // 开始轮询状态
    pollBatchStatus(jobId);
}

// 轮询批量分析状态
async function pollBatchStatus(jobId) {
    const maxAttempts = 60; // 最多轮询5分钟
    let attempts = 0;

    const poll = async () => {
        try {
            const response = await fetch(`/api/v1/batch/status/${jobId}`);
            if (!response.ok) {
                throw new Error('无法获取批量分析状态');
            }

            const status = await response.json();
            updateBatchProgress(status);

            if (status.status === 'completed' || status.status === 'failed') {
                if (status.status === 'completed') {
                    showBatchResults(jobId);
                } else {
                    showMessage('批量分析失败', 'error');
                }
                return;
            }

            attempts++;
            if (attempts < maxAttempts) {
                setTimeout(poll, 5000); // 每5秒轮询一次
            } else {
                showMessage('分析超时，请手动检查状态', 'warning');
            }
        } catch (error) {
            console.error('轮询状态失败:', error);
            showMessage('无法获取分析状态', 'error');
        }
    };

    poll();
}

// 更新批量分析进度
function updateBatchProgress(status) {
    const progressFill = document.getElementById('batch-progress-fill');
    const statusDiv = document.getElementById('batch-status');
    const completedCount = document.getElementById('completed-count');
    const processingCount = document.getElementById('processing-count');
    const failedCount = document.getElementById('failed-count');
    const progressPercentage = document.getElementById('progress-percentage');
    const concurrencyDisplay = document.getElementById('concurrency-display');
    const estimatedTime = document.getElementById('estimated-time');
    const currentProcessingFiles = document.getElementById('current-processing-files');

    if (progressFill && statusDiv) {
        const progress = status.progress_percentage || ((status.completed / status.total) * 100);
        progressFill.style.width = `${progress}%`;
        statusDiv.textContent = `状态: ${getStatusText(status.status)} - 已完成 ${status.completed}/${status.total} 个文件`;

        // 更新详细信息
        if (completedCount) completedCount.textContent = status.completed;
        if (processingCount) processingCount.textContent = status.processing || 0;
        if (failedCount) failedCount.textContent = status.failed || 0;
        if (progressPercentage) progressPercentage.textContent = `${progress.toFixed(1)}%`;
        if (concurrencyDisplay) concurrencyDisplay.textContent = status.concurrency || '-';

        // 预估剩余时间
        if (estimatedTime) {
            if (status.estimated_remaining_time) {
                const minutes = Math.floor(status.estimated_remaining_time / 60);
                const seconds = status.estimated_remaining_time % 60;
                estimatedTime.textContent = minutes > 0 ? `${minutes}分${seconds}秒` : `${seconds}秒`;
            } else {
                estimatedTime.textContent = '-';
            }
        }

        // 当前处理的文件
        if (currentProcessingFiles && status.current_processing_files) {
            currentProcessingFiles.innerHTML = '';
            status.current_processing_files.forEach(fileName => {
                const fileSpan = document.createElement('span');
                fileSpan.className = 'file-name';
                fileSpan.textContent = fileName;
                currentProcessingFiles.appendChild(fileSpan);
            });
        }
    }
}

function getStatusText(status) {
    const statusMap = {
        'pending': '等待中',
        'processing': '处理中',
        'completed': '已完成',
        'failed': '失败'
    };
    return statusMap[status] || status;
}

// 显示批量分析结果
async function showBatchResults(jobId) {
    try {
        const response = await fetch(`/api/v1/batch/results/${jobId}`);
        if (!response.ok) {
            throw new Error('无法获取批量分析结果');
        }

        const results = await response.json();
        displayBatchResults(results);
    } catch (error) {
        console.error('获取批量结果失败:', error);
        showMessage('无法获取分析结果', 'error');
    }
}

// 显示批量分析结果
function displayBatchResults(batchData) {
    resultsContainer.innerHTML = '';

    const batchResultsContainer = document.createElement('div');
    batchResultsContainer.className = 'batch-results-container';
    batchResultsContainer.innerHTML = `
        <h3>📊 批量分析结果</h3>
        <p>作业ID: ${batchData.job_id}</p>
        <p>状态: ${batchData.status}</p>
        <p>共分析了 ${batchData.results.length} 个文件 (${batchData.completed}/${batchData.total})</p>
        <button onclick="exportBatchResults('${batchData.job_id}')" class="btn btn-secondary">导出所有结果</button>
    `;
    resultsContainer.appendChild(batchResultsContainer);

    // 显示每个文件的结果
    batchData.results.forEach((result, index) => {
        const resultCard = document.createElement('div');
        resultCard.className = 'batch-result-card';

        let resultContent = '';
        if (result.status === 'completed' && result.results) {
            // 显示分析任务结果
            for (const [taskType, taskResult] of Object.entries(result.results)) {
                resultContent += `
                    <div class="task-result">
                        <h5>${getTaskName(taskType)}</h5>
                        <div class="task-content">
                            ${taskResult.status === 'completed' ?
                                `<pre>${formatResult(taskResult.result)}</pre>` :
                                `<p class="error">错误: ${taskResult.error}</p>`
                            }
                        </div>
                    </div>
                `;
            }
        }

        resultCard.innerHTML = `
            <h4>文件 ${index + 1}: ${result.document_title}</h4>
            <div class="result-summary">
                <p>状态: ${result.status === 'completed' ? '✅ 完成' : '❌ 失败'}</p>
                ${result.status === 'completed' ?
                    `<button onclick="toggleResultDetails('result-${index}')" class="btn btn-info">查看详细结果</button>` :
                    `<p class="error">错误: ${result.error}</p>`
                }
            </div>
            <div id="result-${index}" class="detailed-results" style="display: none;">
                ${resultContent}
            </div>
        `;
        resultsContainer.appendChild(resultCard);
    });
}

// 切换详细结果显示
function toggleResultDetails(resultId) {
    const detailsDiv = document.getElementById(resultId);
    if (detailsDiv) {
        detailsDiv.style.display = detailsDiv.style.display === 'none' ? 'block' : 'none';
    }
}

// 导出批量结果
function exportBatchResults(jobId) {
    // 这里可以实现导出功能，比如生成CSV或JSON文件
    console.log('导出批量结果:', jobId);
    showMessage('导出功能开发中...', 'info');
}

// 显示分析结果
function displayResults(results) {
    resultsContainer.innerHTML = '';

    // 添加可视化按钮
    const visualizationBtn = document.createElement('div');
    visualizationBtn.className = 'visualization-btn-container';
    visualizationBtn.innerHTML = `
        <button class="viz-btn" onclick="switchToVisualization()">
            📊 查看数据可视化
        </button>
    `;
    resultsContainer.appendChild(visualizationBtn);
    
    for (const [taskType, result] of Object.entries(results)) {
        const resultCard = document.createElement('div');
        resultCard.className = 'result-card';
        
        const taskName = getTaskName(taskType);
        
        resultCard.innerHTML = `
            <h3>${taskName}</h3>
            <div class="result-content">
                <pre>${formatResult(result)}</pre>
            </div>
        `;
        
        resultsContainer.appendChild(resultCard);
    }
}

// 格式化结果
function formatResult(result) {
    if (result.success === false) {
        return `分析失败: ${result.error || '未知错误'}`;
    }
    
    if (result.result) {
        if (result.result.raw_analysis) {
            return result.result.raw_analysis;
        }
        return JSON.stringify(result.result, null, 2);
    }
    
    return JSON.stringify(result, null, 2);
}

// 获取任务名称
function getTaskName(taskType) {
    const taskNames = {
        'actor_relation': '行为者与关系分析',
        'role_framing': '角色塑造检测',
        'problem_scope': '问题范围策略',
        'causal_mechanism': '因果机制分析'
    };
    return taskNames[taskType] || taskType;
}

// 添加到历史记录
function addToHistory(docId, title, tasks) {
    const historyItem = {
        id: docId,
        title: title,
        tasks: tasks,
        date: new Date().toLocaleString('zh-CN')
    };
    
    historyData.unshift(historyItem);
    
    // 只保留最近10条记录
    if (historyData.length > 10) {
        historyData = historyData.slice(0, 10);
    }
    
    // 保存到本地存储
    localStorage.setItem('analysisHistory', JSON.stringify(historyData));
    
    // 更新显示
    updateHistoryDisplay();
}

// 加载历史记录
function loadHistory() {
    const stored = localStorage.getItem('analysisHistory');
    if (stored) {
        try {
            historyData = JSON.parse(stored);
            updateHistoryDisplay();
        } catch (e) {
            console.error('加载历史记录失败:', e);
        }
    }
}

// 更新历史记录显示
function updateHistoryDisplay() {
    if (historyData.length === 0) {
        historyList.innerHTML = '<p class="empty-state">暂无历史记录</p>';
        return;
    }
    
    historyList.innerHTML = '';
    
    historyData.forEach(item => {
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item';
        historyItem.innerHTML = `
            <h4>${item.title}</h4>
            <div class="date">${item.date}</div>
        `;
        
        historyItem.addEventListener('click', () => {
            loadHistoryItem(item.id);
        });
        
        historyList.appendChild(historyItem);
    });
}

// 加载历史记录项
async function loadHistoryItem(docId) {
    try {
        const response = await fetch(`/api/v1/analysis/results/${docId}`);
        if (response.ok) {
            const results = await response.json();
            resultsSection.style.display = 'block';
            displayResults(results.results);
            showMessage('已加载历史记录', 'success');
        } else {
            showMessage('无法加载历史记录', 'error');
        }
    } catch (error) {
        showMessage(`加载失败: ${error.message}`, 'error');
    }
}

// 切换到数据可视化页面
function switchToVisualization() {
    // 获取当前分析的文档ID
    const currentDocId = historyData.length > 0 ? historyData[0].id : null;
    
    if (currentDocId) {
        // 保存当前文档ID到sessionStorage，供可视化页面使用
        sessionStorage.setItem('currentDocId', currentDocId);
        sessionStorage.setItem('fromAnalysis', 'true');
    }
    
    // 跳转到可视化页面
    window.location.href = '/static/visualization.html';
}

// 显示消息
function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
    messageDiv.textContent = message;
    
    // 插入到主内容区域顶部
    const main = document.querySelector('main');
    main.insertBefore(messageDiv, main.firstChild);
    
    // 3秒后自动移除
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

// 处理模型选择变化
function handleModelChange() {
    const selectedModel = modelSelect.value;
    if (selectedModel === 'custom') {
        customModelInput.style.display = 'block';
        customModelInput.focus();
    } else {
        customModelInput.style.display = 'none';
        customModelInput.value = '';
    }
}

// 获取当前选择的模型
function getCurrentModel() {
    const selectedModel = modelSelect.value;
    if (selectedModel === 'custom') {
        return customModelInput.value.trim() || 'glm-4.5';
    }
    return selectedModel;
}

// 保存配置
function saveConfiguration() {
    try {
        const config = {
            apiKey: apiKeyInput.value.trim(),
            baseUrl: baseUrlInput.value.trim(),
            model: modelSelect.value,
            customModel: customModelInput.value.trim(),
            timestamp: new Date().toISOString()
        };

        // 保存到localStorage
        localStorage.setItem('zhipuApiKey', config.apiKey);
        localStorage.setItem('zhipuBaseUrl', config.baseUrl);
        localStorage.setItem('zhipuModel', config.model);
        localStorage.setItem('zhipuCustomModel', config.customModel);
        localStorage.setItem('zhipuConfigTimestamp', config.timestamp);

        showMessage('配置已保存成功！', 'success');
    } catch (error) {
        console.error('保存配置失败:', error);
        showMessage('保存配置失败，请重试', 'error');
    }
}

// 重置配置
function resetConfiguration() {
    if (confirm('确定要重置所有配置吗？这将清除已保存的API密钥、Base URL和模型设置。')) {
        // 清除localStorage
        localStorage.removeItem('zhipuApiKey');
        localStorage.removeItem('zhipuBaseUrl');
        localStorage.removeItem('zhipuModel');
        localStorage.removeItem('zhipuCustomModel');
        localStorage.removeItem('zhipuConfigTimestamp');

        // 重置表单
        apiKeyInput.value = '';
        baseUrlInput.value = '';
        modelSelect.value = 'glm-4.5';
        customModelInput.value = '';
        customModelInput.style.display = 'none';

        showMessage('配置已重置', 'success');
    }
}

// 加载保存的配置
function loadSavedConfiguration() {
    // 加载API密钥
    const savedApiKey = localStorage.getItem('zhipuApiKey');
    if (savedApiKey && apiKeyInput) {
        apiKeyInput.value = savedApiKey;
    }

    // 加载Base URL
    const savedBaseUrl = localStorage.getItem('zhipuBaseUrl');
    if (savedBaseUrl && baseUrlInput) {
        baseUrlInput.value = savedBaseUrl;
    }

    // 加载模型设置
    const savedModel = localStorage.getItem('zhipuModel');
    if (savedModel && modelSelect) {
        modelSelect.value = savedModel;

        // 如果是自定义模型，显示自定义输入框并加载值
        if (savedModel === 'custom') {
            const savedCustomModel = localStorage.getItem('zhipuCustomModel');
            if (customModelInput) {
                customModelInput.style.display = 'block';
                customModelInput.value = savedCustomModel || '';
            }
        }
    }

    // 显示配置加载时间
    const configTimestamp = localStorage.getItem('zhipuConfigTimestamp');
    if (configTimestamp) {
        console.log('配置加载时间:', new Date(configTimestamp).toLocaleString());
    }
}

// 运行高级分析
async function runAdvancedAnalysis() {
    try {
        // 获取文档ID和任务
        const docId = docTitle.value.trim() || `doc_${Date.now()}`;
        const tasks = [];
        document.querySelectorAll('input[name="task"]:checked').forEach(checkbox => {
            tasks.push(checkbox.value);
        });

        if (tasks.length === 0) {
            showMessage('请至少选择一个分析任务', 'error');
            return;
        }

        // 获取当前选择的模型
        const selectedModel = getCurrentModel();

        // 显示加载状态
        resultsSection.style.display = 'block';
        loading.style.display = 'block';
        resultsContainer.innerHTML = '';

        // 发送高级分析请求
        const response = await fetch('/api/v2/analysis/advanced', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                doc_id: docId,
                tasks: tasks,
                use_ai_orchestrator: true,
                enable_new_tasks: true,
                analysis_config: {
                    model: selectedModel,
                    concurrency: 1
                }
            })
        });

        if (!response.ok) {
            throw new Error('高级分析失败');
        }

        const result = await response.json();

        // 隐藏加载状态
        loading.style.display = 'none';

        // 显示结果
        displayAdvancedResults(result);

        showMessage('高级分析完成！', 'success');

    } catch (error) {
        loading.style.display = 'none';
        showMessage(`高级分析错误: ${error.message}`, 'error');
        console.error('高级分析错误:', error);
    }
}

// 运行批量高级分析
async function runBatchAdvancedAnalysis() {
    try {
        // 检查是否有上传的文件
        if (uploadedFiles.length === 0) {
            showMessage('请先上传文件进行批量高级分析', 'error');
            return;
        }

        // 获取选中的任务
        const tasks = [];
        document.querySelectorAll('input[name="task"]:checked').forEach(checkbox => {
            tasks.push(checkbox.value);
        });

        if (tasks.length === 0) {
            showMessage('请至少选择一个分析任务', 'error');
            return;
        }

        // 获取并发设置
        const concurrency = concurrencySlider ? parseInt(concurrencySlider.value) : 3;

        // 获取当前选择的模型
        const selectedModel = getCurrentModel();

        // 构建批量请求
        const requests = uploadedFiles.map(file => ({
            doc_id: file.docId || `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            tasks: tasks,
            use_ai_orchestrator: true,
            enable_new_tasks: true,
            analysis_config: {
                model: selectedModel
            }
        }));

        // 显示加载状态
        resultsSection.style.display = 'block';
        loading.style.display = 'block';
        resultsContainer.innerHTML = '';

        // 发送批量高级分析请求
        const response = await fetch('/api/v2/analysis/batch-advanced', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                requests: requests,
                concurrency: concurrency,
                batch_name: `批量高级分析_${new Date().toLocaleString()}`,
                priority: 0
            })
        });

        if (!response.ok) {
            throw new Error('批量高级分析启动失败');
        }

        const batchData = await response.json();

        // 隐藏加载状态
        loading.style.display = 'none';

        // 显示批量分析进度
        showAdvancedBatchProgress(batchData.batch_id, requests.length);

        showMessage(`批量高级分析已启动，正在处理 ${requests.length} 个文件`, 'success');

    } catch (error) {
        loading.style.display = 'none';
        showMessage(`批量高级分析错误: ${error.message}`, 'error');
        console.error('批量高级分析错误:', error);
    }
}

// 显示高级分析结果
function displayAdvancedResults(result) {
    resultsContainer.innerHTML = '';

    const advancedResultsContainer = document.createElement('div');
    advancedResultsContainer.className = 'advanced-results-container';
    advancedResultsContainer.innerHTML = `
        <h3>🚀 高级分析结果</h3>
        <div class="analysis-info">
            <p><strong>分析ID:</strong> ${result.analysis_id}</p>
            <p><strong>文档ID:</strong> ${result.doc_id}</p>
            <p><strong>分析时间:</strong> ${new Date(result.timestamp).toLocaleString()}</p>
            <p><strong>使用模型:</strong> ${result.performance_metrics?.model_used || 'N/A'}</p>
            <p><strong>处理时间:</strong> ${result.performance_metrics?.processing_time || 'N/A'}秒</p>
        </div>
        <div class="results-content">
            ${Object.entries(result.results.results || {}).map(([task, taskResult]) => `
                <div class="task-result">
                    <h4>📋 ${task}</h4>
                    <div class="task-content">
                        <pre>${typeof taskResult === 'string' ? taskResult : JSON.stringify(taskResult, null, 2)}</pre>
                    </div>
                </div>
            `).join('')}
        </div>
        ${result.orchestrator_info ? `
            <div class="orchestrator-info">
                <h4>🤖 AI编排信息</h4>
                <pre>${JSON.stringify(result.orchestrator_info, null, 2)}</pre>
            </div>
        ` : ''}
    `;
    resultsContainer.appendChild(advancedResultsContainer);
}

// 显示高级批量分析进度
function showAdvancedBatchProgress(batchId, fileCount) {
    resultsContainer.innerHTML = '';
    resultsContainer.style.display = 'block';

    const progressContainer = document.createElement('div');
    progressContainer.className = 'advanced-batch-progress-container';
    progressContainer.innerHTML = `
        <h3>🚀 批量高级分析进度</h3>
        <div class="batch-info">
            <p><strong>批次ID:</strong> ${batchId}</p>
            <p><strong>总文件数:</strong> ${fileCount}</p>
        </div>

        <div class="progress-bar">
            <div id="advanced-batch-progress-fill" class="progress-fill" style="width: 0%"></div>
        </div>

        <div class="progress-stats">
            <div class="stat-item">
                <span class="stat-label">已完成:</span>
                <span id="advanced-completed-count">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">处理中:</span>
                <span id="advanced-processing-count">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">失败:</span>
                <span id="advanced-failed-count">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">进度:</span>
                <span id="advanced-progress-percentage">0%</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">并发数:</span>
                <span id="advanced-concurrency-display">-</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">预估剩余:</span>
                <span id="advanced-estimated-time">计算中...</span>
            </div>
        </div>

        <div id="advanced-batch-status">准备中...</div>

        <div class="current-files">
            <div class="label">正在处理的文档：</div>
            <div id="advanced-current-processing-docs"></div>
        </div>

        <button type="button" onclick="checkAdvancedBatchStatus('${batchId}')" class="btn btn-primary">手动检查进度</button>
    `;
    resultsContainer.appendChild(progressContainer);

    // 开始轮询状态
    pollAdvancedBatchStatus(batchId);
}

// 轮询高级批量分析状态
async function pollAdvancedBatchStatus(batchId) {
    const maxAttempts = 60; // 最多轮询5分钟
    let attempts = 0;

    const poll = async () => {
        try {
            const response = await fetch(`/api/v2/analysis/batch-advanced/status/${batchId}`);
            if (!response.ok) {
                throw new Error('无法获取批量高级分析状态');
            }

            const status = await response.json();
            updateAdvancedBatchProgress(status);

            if (status.status === 'completed' || status.status === 'failed') {
                if (status.status === 'completed') {
                    showAdvancedBatchResults(batchId);
                } else {
                    showMessage('批量高级分析失败', 'error');
                }
                return;
            }

            attempts++;
            if (attempts < maxAttempts) {
                setTimeout(poll, 5000); // 每5秒轮询一次
            } else {
                showMessage('分析超时，请手动检查状态', 'warning');
            }
        } catch (error) {
            console.error('轮询高级分析状态失败:', error);
            showMessage('无法获取高级分析状态', 'error');
        }
    };

    poll();
}

// 更新高级批量分析进度
function updateAdvancedBatchProgress(status) {
    const progressFill = document.getElementById('advanced-batch-progress-fill');
    const statusDiv = document.getElementById('advanced-batch-status');
    const completedCount = document.getElementById('advanced-completed-count');
    const processingCount = document.getElementById('advanced-processing-count');
    const failedCount = document.getElementById('advanced-failed-count');
    const progressPercentage = document.getElementById('advanced-progress-percentage');
    const concurrencyDisplay = document.getElementById('advanced-concurrency-display');
    const estimatedTime = document.getElementById('advanced-estimated-time');
    const currentProcessingDocs = document.getElementById('advanced-current-processing-docs');

    if (progressFill) {
        progressFill.style.width = `${status.progress_percentage}%`;
    }

    if (statusDiv) {
        statusDiv.textContent = `状态: ${status.status}`;
        statusDiv.className = `batch-status ${status.status}`;
    }

    if (completedCount) completedCount.textContent = status.completed;
    if (processingCount) processingCount.textContent = status.processing;
    if (failedCount) failedCount.textContent = status.failed;
    if (progressPercentage) progressPercentage.textContent = `${status.progress_percentage.toFixed(1)}%`;
    if (concurrencyDisplay) concurrencyDisplay.textContent = status.concurrency;

    if (estimatedTime) {
        if (status.estimated_remaining_time) {
            const minutes = Math.floor(status.estimated_remaining_time / 60);
            const seconds = status.estimated_remaining_time % 60;
            estimatedTime.textContent = `${minutes}分${seconds}秒`;
        } else {
            estimatedTime.textContent = '计算中...';
        }
    }

    if (currentProcessingDocs) {
        if (status.current_processing_docs && status.current_processing_docs.length > 0) {
            currentProcessingDocs.innerHTML = status.current_processing_docs
                .map(docId => `<span class="processing-file">${docId}</span>`)
                .join(', ');
        } else {
            currentProcessingDocs.textContent = '无';
        }
    }
}

// 显示高级批量分析结果
async function showAdvancedBatchResults(batchId) {
    try {
        const response = await fetch(`/api/v2/analysis/batch-advanced/results/${batchId}`);
        if (!response.ok) {
            throw new Error('无法获取批量高级分析结果');
        }

        const batchData = await response.json();
        displayAdvancedBatchResults(batchData);

    } catch (error) {
        console.error('获取批量高级分析结果失败:', error);
        showMessage('无法获取批量分析结果', 'error');
    }
}

// 显示高级批量分析结果
function displayAdvancedBatchResults(batchData) {
    resultsContainer.innerHTML = '';

    const batchResultsContainer = document.createElement('div');
    batchResultsContainer.className = 'advanced-batch-results-container';
    batchResultsContainer.innerHTML = `
        <h3>🚀 批量高级分析结果</h3>
        <div class="batch-summary">
            <p><strong>批次ID:</strong> ${batchData.batch_id}</p>
            <p><strong>状态:</strong> ${batchData.status}</p>
            <p><strong>总任务数:</strong> ${batchData.total_tasks}</p>
            <p><strong>已完成:</strong> ${batchData.completed}</p>
            <p><strong>失败:</strong> ${batchData.failed}</p>
            <p><strong>创建时间:</strong> ${new Date(batchData.created_at).toLocaleString()}</p>
            <p><strong>完成时间:</strong> ${new Date(batchData.updated_at).toLocaleString()}</p>
        </div>
        <button onclick="exportAdvancedBatchResults('${batchData.batch_id}')" class="btn btn-secondary">导出所有结果</button>
        <div class="results-list">
            ${Object.entries(batchData.results || {}).map(([docId, result]) => `
                <div class="result-item ${result.success ? 'success' : 'error'}">
                    <h4>📄 ${docId}</h4>
                    ${result.success ? `
                        <div class="result-content">
                            <p><strong>分析ID:</strong> ${result.analysis_id}</p>
                            <p><strong>处理时间:</strong> ${new Date(result.timestamp).toLocaleString()}</p>
                            <div class="task-results">
                                ${Object.entries(result.results.results || {}).map(([task, taskResult]) => `
                                    <div class="task-result-item">
                                        <strong>${task}:</strong>
                                        <pre>${typeof taskResult === 'string' ? taskResult : JSON.stringify(taskResult, null, 2)}</pre>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : `
                        <div class="error-content">
                            <p><strong>错误:</strong> ${result.error}</p>
                        </div>
                    `}
                </div>
            `).join('')}
        </div>
    `;
    resultsContainer.appendChild(batchResultsContainer);
}

// 手动检查高级批量分析状态
async function checkAdvancedBatchStatus(batchId) {
    try {
        const response = await fetch(`/api/v2/analysis/batch-advanced/status/${batchId}`);
        if (!response.ok) {
            throw new Error('无法获取状态');
        }

        const status = await response.json();
        updateAdvancedBatchProgress(status);

        showMessage(`状态已更新: ${status.status}`, 'success');

    } catch (error) {
        console.error('检查状态失败:', error);
        showMessage('检查状态失败', 'error');
    }
}

// 导出高级批量分析结果
function exportAdvancedBatchResults(batchId) {
    // 这里可以实现导出功能
    showMessage('导出功能开发中...', 'info');
}
