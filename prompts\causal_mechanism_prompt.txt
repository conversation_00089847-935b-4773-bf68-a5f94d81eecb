请分析以下政策文档中的因果机制描述。

文档内容：
{document}

请执行以下分析任务：
1. 识别文档中描述的因果链（事件A导致事件B导致事件C...）
2. 分析归因模式（问题被归因于什么因素）
3. 分析责任框架（谁被认为应对问题负责）
4. 总结关键发现

以JSON格式返回分析结果，包含以下字段：
{
    "causal_chains": [
        {
            "sequence": ["原因1", "结果1/原因2", "结果2"...],
            "description": "因果链描述"
        }
        // 更多因果链...
    ],
    "attribution_patterns": [
        {
            "target": "归因对象",
            "factors": ["归因因素1", "归因因素2"...],
            "evidence": ["支持证据1", "支持证据2"...]
        }
        // 更多归因模式...
    ],
    "responsibility_framing": {
        "responsible_actors": ["负责任的行为者1", "负责任的行为者2"...],
        "absolved_actors": ["被免责的行为者1", "被免责的行为者2"...],
        "framing_strategy": "责任框架策略描述"
    },
    "key_findings": [
        "关键发现1",
        "关键发现2"
        // 更多关键发现...
    ]
}

只返回JSON格式的分析结果，不要包含任何额外的解释。
