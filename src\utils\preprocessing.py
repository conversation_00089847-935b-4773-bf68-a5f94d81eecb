import re
from typing import Dict, List, Any, Optional, Union
import logging
from pathlib import Path
import os

logger = logging.getLogger(__name__)

def preprocess_text(text: str) -> str:
    """预处理文本的便捷函数
    
    Args:
        text: 原始文本
        
    Returns:
        str: 预处理后的文本
    """
    return DocumentPreprocessor.clean_text(text)


class DocumentPreprocessor:
    """文档预处理工具类，提供文本清洗、分段等功能"""

    @staticmethod
    def clean_text(text: str) -> str:
        """清洗文本，移除多余空白、特殊字符等
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清洗后的文本
        """
        if not text:
            return ""
            
        # 移除多余的空白
        text = re.sub(r'\s+', ' ', text)
        # 移除不可见字符
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', text)
        # 替换全角标点为半角
        text = text.replace('，', ',').replace('。', '.').replace('：', ':').replace('；', ';')
        # 移除多余的标点
        text = re.sub(r'[,.;:!?]{2,}', lambda m: m.group(0)[0], text)
        
        return text.strip()

    @staticmethod
    def split_into_paragraphs(text: str) -> List[str]:
        """将文本分割为段落
        
        Args:
            text: 文本内容
            
        Returns:
            List[str]: 段落列表
        """
        if not text:
            return []
            
        # 按照空行分段
        paragraphs = re.split(r'\n\s*\n', text)
        # 去除空段落
        paragraphs = [p.strip() for p in paragraphs if p.strip()]
        
        return paragraphs

    @staticmethod
    def split_by_max_tokens(text: str, max_tokens: int = 2000, overlap: int = 200) -> List[str]:
        """按最大token数分割文本，保留一定重叠以保持上下文连贯
        
        Args:
            text: 文本内容
            max_tokens: 每段最大token数（粗略估计，中文每字算1token）
            overlap: 重叠token数
            
        Returns:
            List[str]: 分段后的文本列表
        """
        if not text:
            return []
            
        # 简单估计token数（粗略估计）
        tokens = list(text)
        total_tokens = len(tokens)
        
        if total_tokens <= max_tokens:
            return [text]
            
        chunks = []
        start = 0
        
        while start < total_tokens:
            # 计算当前段落结束位置
            end = min(start + max_tokens, total_tokens)
            
            # 如果不是最后一段，尝试在适当位置截断
            if end < total_tokens:
                # 优先在句号、问号、感叹号处截断
                sentence_end = text.rfind('.', start, end)
                question_end = text.rfind('?', start, end)
                exclamation_end = text.rfind('!', start, end)
                
                # 找到最靠后的句子结束标记
                break_point = max(sentence_end, question_end, exclamation_end)
                
                # 如果找到了合适的截断点
                if break_point > start:
                    end = break_point + 1
                else:
                    # 否则在空格处截断
                    space_end = text.rfind(' ', start, end)
                    if space_end > start:
                        end = space_end
            
            # 添加当前段落
            chunks.append(text[start:end])
            
            # 计算下一段落的起始位置（考虑重叠）
            start = end - overlap
            
            # 确保起始位置有效
            if start < 0:
                start = 0
            
        return chunks

    @staticmethod
    def extract_metadata(text: str) -> Dict[str, Any]:
        """提取文本元数据，如标题、发布时间等
        
        Args:
            text: 文本内容
            
        Returns:
            Dict[str, Any]: 文本元数据
        """
        metadata = {
            "title": None,
            "date": None,
            "source": None,
            "word_count": len(text),
            "paragraph_count": len(DocumentPreprocessor.split_into_paragraphs(text))
        }
        
        # 尝试提取标题（假设在文本开头）
        lines = text.split('\n')
        if lines and len(lines[0]) < 100:  # 标题一般不会太长
            metadata["title"] = lines[0].strip()
        
        # 尝试提取日期（使用简单的正则表达式模式）
        date_patterns = [
            r'(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日号]?)',  # 2023年1月1日
            r'(\d{4}\.\d{1,2}\.\d{1,2})',                 # 2023.1.1
            r'(\d{1,2}/\d{1,2}/\d{4})'                    # 1/1/2023
        ]
        
        for pattern in date_patterns:
            date_match = re.search(pattern, text)
            if date_match:
                metadata["date"] = date_match.group(1)
                break
        
        # 尝试提取来源
        source_patterns = [
            r'来源[：:]\s*([^\n]+)',
            r'来自[：:]\s*([^\n]+)',
            r'发布者[：:]\s*([^\n]+)',
        ]
        
        for pattern in source_patterns:
            source_match = re.search(pattern, text)
            if source_match:
                metadata["source"] = source_match.group(1).strip()
                break
        
        return metadata

    @staticmethod
    def calculate_stats(text: str) -> Dict[str, Any]:
        """计算文本统计信息
        
        Args:
            text: 文本内容
            
        Returns:
            Dict[str, Any]: 文本统计信息
        """
        if not text:
            return {
                "char_count": 0,
                "word_count": 0,
                "paragraph_count": 0,
                "sentence_count": 0
            }
            
        # 分段
        paragraphs = DocumentPreprocessor.split_into_paragraphs(text)
        
        # 计算句子数（粗略估计）
        sentences = re.split(r'[。！？.!?]+', text)
        sentences = [s for s in sentences if s.strip()]
        
        # 中文分词需要专门的分词工具，此处仅粗略统计
        # 英文单词数
        english_words = len(re.findall(r'[a-zA-Z]+', text))
        # 中文字符数
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        
        return {
            "char_count": len(text),
            "word_count": english_words + chinese_chars,  # 粗略估计
            "paragraph_count": len(paragraphs),
            "sentence_count": len(sentences)
        }
