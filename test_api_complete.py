#!/usr/bin/env python3
"""
完整的API功能测试脚本
"""
import requests
import json
import time

BASE_URL = "http://127.0.0.1:8000/api/v1"

def test_health_check():
    """测试健康检查"""
    print("测试健康检查...")
    response = requests.get(f"{BASE_URL}/test")
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print()

def test_analysis_tasks():
    """测试获取分析任务"""
    print("测试获取分析任务...")
    response = requests.get(f"{BASE_URL}/analysis/tasks")
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
    print()

def test_create_document():
    """测试创建文档"""
    print("测试创建文档...")
    document_data = {
        "doc_id": "api_test_doc_001",
        "title": "API测试文档",
        "text": """人工智能技术正在快速发展，各国政府都在制定相关政策来规范AI的发展。
政府部门强调AI技术应该以负责任的方式发展，企业界则呼吁制定明确的监管框架，
而学术界则关注AI的伦理影响。在这个过程中，政府扮演着引导者的角色，
企业是技术创新的推动者，学术界提供理论支持和伦理指导。
三方需要密切合作，共同推动AI技术的健康发展。""",
        "metadata": {"test": True}
    }
    
    response = requests.post(
        f"{BASE_URL}/documents/",
        json=document_data,
        headers={"Content-Type": "application/json"}
    )
    print(f"状态码: {response.status_code}")
    if response.status_code == 201:
        print("文档创建成功")
        return document_data["doc_id"]
    else:
        print(f"文档创建失败: {response.text}")
        return None

def test_document_analysis(doc_id):
    """测试文档分析"""
    if not doc_id:
        print("跳过文档分析测试（没有有效的文档ID）")
        return
    
    print(f"测试文档分析 (doc_id: {doc_id})...")
    
    analysis_data = {
        "doc_id": doc_id,
        "tasks": ["actor_relation", "role_framing"]
    }
    
    response = requests.post(
        f"{BASE_URL}/analysis/",
        json=analysis_data,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print("分析请求成功")
        print(f"分析结果键: {list(result.get('results', {}).keys())}")
        
        # 显示部分结果
        for task_type, task_result in result.get('results', {}).items():
            print(f"\n任务 {task_type}:")
            print(f"  成功: {task_result.get('success', False)}")
            if task_result.get('success'):
                result_data = task_result.get('result', {})
                print(f"  结果键: {list(result_data.keys()) if isinstance(result_data, dict) else 'N/A'}")
            else:
                print(f"  错误: {task_result.get('error', 'Unknown error')}")
    else:
        print(f"分析请求失败: {response.text}")
    print()

def test_get_analysis_results(doc_id):
    """测试获取分析结果"""
    if not doc_id:
        print("跳过获取分析结果测试（没有有效的文档ID）")
        return
    
    print(f"测试获取分析结果 (doc_id: {doc_id})...")
    
    response = requests.get(f"{BASE_URL}/analysis/results/{doc_id}")
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("获取分析结果成功")
        print(f"结果键: {list(result.keys())}")
        print(f"文档ID: {result.get('doc_id')}")
        print(f"成功状态: {result.get('success')}")
    else:
        print(f"获取分析结果失败: {response.text}")
    print()

def test_visualization_data(doc_id):
    """测试可视化数据"""
    if not doc_id:
        print("跳过可视化数据测试（没有有效的文档ID）")
        return
    
    print(f"测试可视化数据 (doc_id: {doc_id})...")
    
    response = requests.get(f"{BASE_URL}/visualization/data/{doc_id}")
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("获取可视化数据成功")
        print(f"结果键: {list(result.keys()) if isinstance(result, dict) else 'N/A'}")
    else:
        print(f"获取可视化数据失败: {response.text}")
    print()

def main():
    """主测试函数"""
    print("开始API功能测试")
    print("=" * 50)
    
    # 测试健康检查
    test_health_check()
    
    # 测试获取分析任务
    test_analysis_tasks()
    
    # 测试创建文档
    doc_id = test_create_document()
    
    # 等待一下确保文档创建完成
    if doc_id:
        time.sleep(1)
    
    # 测试文档分析
    test_document_analysis(doc_id)
    
    # 等待分析完成
    if doc_id:
        time.sleep(3)
    
    # 测试获取分析结果
    test_get_analysis_results(doc_id)
    
    # 测试可视化数据
    test_visualization_data(doc_id)
    
    print("=" * 50)
    print("API功能测试完成")

if __name__ == "__main__":
    main()