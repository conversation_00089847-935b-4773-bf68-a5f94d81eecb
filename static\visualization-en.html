<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Visualization - Intelligent Document Analysis System</title>
    <link rel="stylesheet" href="/static/style.css">
    <script src="/static/js/i18n.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        .visualization-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .page-header {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .page-header h2 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .page-header p {
            color: #666;
            margin: 0;
            font-size: 1.1em;
        }
        
        .controls-panel {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .control-row {
            display: flex;
            gap: 20px;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .control-group {
            flex: 1;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .control-group select,
        .control-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .control-group select:focus,
        .control-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin-right: 10px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #138496;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .raw-data-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .raw-data-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .raw-data-header h3 {
            margin: 0;
            color: #333;
        }
        
        .raw-data-content {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .chart-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .chart-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .chart-canvas {
            position: relative;
            height: 400px;
            width: 100%;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>📄 Intelligent Document Analysis System</h1>
            <p class="subtitle">Advanced Policy Document Analysis Tool Powered by Zhipu AI</p>
            <nav class="main-nav">
                <a href="/" class="nav-link">🏠 Document Analysis</a>
                <a href="/static/visualization.html" class="nav-link active">📊 Data Visualization</a>
                <a href="/static/data-table.html" class="nav-link">📋 Table View</a>
                <a href="/static/prompts.html" class="nav-link">🔧 Prompt Editor</a>
                <a href="/docs" class="nav-link">📚 API Documentation</a>
                <div class="language-selector">
                    <button data-lang="zh" onclick="switchLanguage('zh')">中文</button>
                    <button data-lang="en" onclick="switchLanguage('en')" class="active">English</button>
                </div>
            </nav>
        </header>

        <main class="visualization-container">
            <!-- Page Title -->
            <div class="page-header">
                <h2>📊 Analysis Results Visualization</h2>
                <p>Transform complex analysis results into intuitive chart displays</p>
            </div>

            <!-- Control Panel -->
            <div class="controls-panel">
                <div class="control-row">
                    <div class="control-group">
                        <label for="doc-select">Select Document:</label>
                        <select id="doc-select" onchange="onDocumentSelect()">
                            <option value="">-- Select Existing Document --</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label for="doc-id">Or Enter Document ID Manually:</label>
                        <input type="text" id="doc-id" placeholder="Enter Document ID" value="">
                    </div>
                </div>
                <div class="control-row">
                    <div class="control-group">
                        <label for="task-filter">Analysis Tasks:</label>
                        <select id="task-filter" multiple>
                            <option value="actor_relation">Actor Relations</option>
                            <option value="role_framing">Role Framing</option>
                            <option value="problem_scope">Problem Scope</option>
                            <option value="causal_mechanism">Causal Mechanism</option>
                        </select>
                    </div>
                </div>
                <div class="control-row">
                    <div class="control-group">
                        <button class="btn btn-primary" onclick="loadVisualization()">🔄 Load Visualization</button>
                        <button class="btn btn-secondary" onclick="loadSampleData()">📋 Load Sample Data</button>
                        <button class="btn btn-info" onclick="toggleRawData()">📄 View Raw Data</button>
                        <a href="/static/data-table.html" class="btn btn-success">📊 Table View</a>
                    </div>
                </div>
            </div>

            <!-- Raw Data Display Area -->
            <div class="raw-data-container" id="raw-data-container" style="display: none;">
                <div class="raw-data-header">
                    <h3>📄 Raw Response Data</h3>
                    <button class="btn btn-secondary" onclick="copyRawData()">📋 Copy Data</button>
                </div>
                <pre id="raw-data-content" class="raw-data-content"></pre>
            </div>

            <!-- Charts Area -->
            <div class="charts-grid" id="charts-container">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Please select a document and load visualization data</p>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Set language to English by default for this page
        document.addEventListener('DOMContentLoaded', function() {
            switchLanguage('en');
        });
    </script>
    <script src="/static/app.js"></script>
</body>
</html>
