请分析以下政策文档中的角色塑造框架。

文档内容：
{document}

请执行以下分析任务：
1. 识别文档中的英雄角色塑造（谁被塑造为解决问题的英雄）
2. 识别文档中的受害者角色塑造（谁被塑造为问题的受害者）
3. 识别文档中的反派角色塑造（谁被塑造为问题的制造者）
4. 分析叙事策略和框架手法
5. 总结关键发现

以JSON格式返回分析结果，包含以下字段：
{
    "heroes": [
        {
            "name": "英雄名称",
            "description": "如何被塑造为英雄",
            "evidence": ["支持证据1", "支持证据2"...]
        }
        // 更多英雄...
    ],
    "victims": [
        {
            "name": "受害者名称",
            "description": "如何被塑造为受害者",
            "evidence": ["支持证据1", "支持证据2"...]
        }
        // 更多受害者...
    ],
    "villains": [
        {
            "name": "反派名称",
            "description": "如何被塑造为反派",
            "evidence": ["支持证据1", "支持证据2"...]
        }
        // 更多反派...
    ],
    "narratives": [
        {
            "type": "叙事策略类型",
            "description": "策略描述",
            "examples": ["例子1", "例子2"...]
        }
        // 更多叙事策略...
    ],
    "key_findings": [
        "关键发现1",
        "关键发现2"
        // 更多关键发现...
    ]
}

只返回JSON格式的分析结果，不要包含任何额外的解释。
