{"doc_id": "42b46490-6cce-40fc-9cfe-68c23d8a1faf", "title": "item108_US_Safety Standards Delivering Controllable and Beneficial AI Tools", "text": "Safety Standards Delivering Controllable and Beneficial AI Tools\r\nWe present a concrete proposal for how humanity can maintain control over highly advanced AI systems.\r\n\r\nIntroduction\r\nThe past decade has seen the extraordinary development of artificial intelligence from a niche academic pursuit to a transformative technology. AI tools promise to unlock incredible benefits for people and society, from Nobel prize-winning breakthroughs in drug discovery to autonomous vehicles and personalized education. Unfortunately, two core dynamics threaten to derail this promise:\r\n\r\nFirst, the speed and manner in which AI is being developed—as a chaotic nearly-unregulated race between companies and countries—incentivizes a race to the bottom, cutting corners on security, safety and controllability. We are now closer to figuring out how to build general-purpose smarter-than-human machines (AGI) than to figuring out how to keep them under control.\r\nSecond, the main direction of AI development not toward trustworthy controllable tools to empower people, but toward potentially uncontrollable AGI that threatens to replace them, jeopardizing our livelihoods and lives as individuals, and our future as a civilization.\r\nWith many leading AI scientists and CEOs predicting AGI to be merely 1-5 years away, it is urgent to correct the course of AI development. Fortunately, there is an easy and well-tested way to do this: start treating the AI industry like all other high-impact industries, with legally binding safety standards, incentivizing companies to innovate to meet them in a race to the top. We make a concrete proposal for such standards below.\r\n\r\nThe Need to Act Now \r\nOnly six years ago, many experts believed that AI as capable as GPT-4 was decades, even centuries away. Now OpenAI’s o3 system recently scored 85% on the ARC-AGI benchmark, and showcases human-level reasoning skills and PhD level skills in biology, chemistry and physics. OpenAI’s “Deep research” feature scores 18% on the ultra-difficult “Humanity’s Last Exam“. Its CEO Sam Altman has claimed they already know how to build AGI, and other companies have explicitly or implicitly announced it as a goal. It is imperative then to understand what AGI is and what it would mean to develop it on our present path.\r\n\r\nAI Risk Thresholds and AGI’s Three Parts  \r\nTo avoid innovation-stifling governmental overreach, we recommend classifying AI systems into tiers of increasing potential risk, with commensurately stricter standards, akin to the five-tier U.S. classification of drugs, and the “AI Safety Levels” from Anthropic’s Responsible Scaling Policy. As described below, our risk tiers are grounded in the capabilities of AI systems in the three core areas required for AGI.\r\n\r\n\r\nAlthough AGI stands for “Artificial General Intelligence”, it is a useful mnemonic to think of it as an “Autonomous General Intelligence”: the triple combination of Autonomy (independence of action), Generality (breath of capability, application, and learning), and Intelligence (competence at tasks). AI systems possess these characteristics in different measures.\r\n\r\nWhile possession of these characteristics can deliver enormous rewards from AI, high levels of convergence between them can result in correspondingly high degrees of unpredictability and risk, with the most dangerous and uncontrollable systems possessing high levels of all three. An AI with all these characteristics would be capable of the large range of effective cognition and action that a human is. However, it would be much more capable—and dangerous—than any individual human, given its scalability in speed, memory, and reproducibility, and its potential for rapid self-improvement and replication.\r\n\r\nThe combination of all three characteristics is currently unique on Earth to homo sapiens. This is why possession of all three capabilities could enable machines to replace us, as individuals in our work or in a wider sense as a species. This is what has made AGI both a target of development and an unprecedented risk.\r\n\r\nAGI: Autonomous Generally Intelligent Systems to Fully Replace Humans\r\nThe nature of AGI as a human replacement—rather than tool—is implicit in how it has been traditionally been defined: as a system that can match human performance on most intellectual tasks. Even more tellingly, OpenAI has defined AGI as “highly autonomous systems that outperform humans at most economically valuable work”.\r\n\r\nThe desire to build powerful machines that can entirely replace humans as workers, educators, companions and more is the goal of a tiny minority. How can we ensure that we instead follow the widely desired path—one that makes us strong, rather than obsolete?\r\n\r\n\r\nTiered Safety Standards \r\nOur proposed AI risk tier classification counts the number (between 0 and 3) of A, G and I factors that are present to high degree within a given AI system.\r\n\r\nFor example, Google DeepMind’s AlphaFold, which solved the protein-folding problem, is more intelligent than any human at its (narrow) task, but not autonomous or general. It therefore scores 1, placing it in Risk Tier 1. OpenAI’s GPT-3 was very general (it could answer questions, generate text, assist with basic coding etc.) but it was not very competent (it was inaccurate, inconsistent, and reasoned poorly). Therefore it falls squarely into Tier 1. Their recently released o3 model, however, has demonstrated human-level reasoning and can answer PhD level science questions, while still being very general, so is in Risk Tier 2.\r\n\r\nThe diagram above us allows us to identify and categorize systems with different levels of A/G/I convergence (and therefore risk). This categorization allows us to place systems in corresponding risk tiers (see the table below), to which a corresponding level of safety and controllability requirements can be applied: roughly speaking, further from the center corresponds to lower risk. Different tiers of convergence trigger different requirements for training (e.g. registration, pre-approval of safety plan) and different requirements for deployment (e.g. safety cases, technical specifications).\r\n\r\nThis approach avoids overly onerous requirements being placed on relatively low-risk/narrow systems, while ensuring that controllability can be guaranteed for more potentially dangerous ones. Rather than hindering competition, this tiered approach will drive companies to innovate to meet requirements, helping to realize the incredible benefits of AI in a secure, responsible, and intentional way.\r\n\r\nThis approach does not require any re-imagining of industry governance. Companies in any other sector, from automobiles and pharmaceuticals to sandwiches, must provide satisfactory evidence that their products are safe before release to the public. They must meet safety standards, and the AI industry should be no different. Furthermore, it is sensible and consistent to place higher standards on technologies or products that have greater potential for harm. We would not want nuclear reactors tested in the same category as sandwiches.\r\n\r\nRisk Tier\tTrigger(s)\tRequirements for training\tRequirements for deployment\r\nRT0\tAI weak in autonomy, generality, and intelligence\tNone\tNone\r\nRT1\tAI strong in one of autonomy, generality, and intelligence\tNone\tQualitative guarantees: Safety audits by national authorities wherever the system can be used, including blackbox and whitebox red-teaming\r\nRT2\tAI strong in two of autonomy, generality, and intelligence\tRegistration with national authority with jurisdiction over the lab\tQuantitative guarantees: National authorities wherever the system can be used must approve company-submitted assessment bounding the risk of major harm below authorized levels\r\nRT3\tAGI strong in autonomy, generality, and intelligence\tPre-approval of safety and security plan by national authority with jurisdiction over the lab\tFormal guarantees: National authorities wherever the system can be used must certify company-submitted formal verification that the system meets required specifications, including cybersecurity, controllability, a non-removable kill-switch, and robustness to malicious use\r\nRT4\tUses more than 1027 FLOP for training or more than 1020 FLOP/s for inference\tProhibited pending internationally agreed lift of compute cap\tProhibited pending internationally agreed lift of compute cap\r\n\r\nRisk classifications and safety standards, with tiers based on compute thresholds as well as combinations of high autonomy, generality, and intelligence:\r\n\r\n• Strong autonomy applies if the system is able to perform many-step tasks and/or take complex real-world actions without significant human oversight or intervention. Examples: autonomous vehicles and robots; financial trading bots. Non-examples: GPT-4; image classifiers\r\n\r\n• Strong generality indicates a wide scope of application, performance of tasks for which the model was not deliberately and specifically trained, and significant ability to learn new tasks. Examples: GPT-4; mu-zero. Non-examples: AlphaFold; autonomous vehicles; image generators\r\n\r\n• Strong intelligence corresponds to matching human expert-level performance on the tasks for which the model performs best (and for a general model, across a broad range of tasks.) Examples: AlphaFold; mu-zero; OpenAI o3. Non-examples: GPT-4; Siri\r\nSafety Guarantees and Controllability  \r\nEnsuring safe and controllable systems requires safety standards that scale with a system’s capabilities, all the way up to and including AGI—which might soon develop into superintelligence. Risk Tiers 0, 1, 2 and 3 correspond to systems that are progressively more difficult to control, and whose potential harm is progressively greater. This is why the corresponding requirements in the table are progressively stricter, ranging from none to qualitative, quantitative and formal proofs. We want our tools to be powerful but controllable (who wants an uncontrollable car?), so we define “Tool AI” as AI that can be controlled with an assurance level commensurate with its Risk Tier.\r\n\r\nSystems with a low degree of risk should not receive onerous requirements that limit their positive impact. Tier 0 systems are therefore unregulated, while Tier 1 systems require only qualitative safety standards. Since Tier 2 systems have significantly greater potential for harm, they require quantitative guarantees, just as is currently the case for other industries with powerful technology. For example, U.S. nuclear power plants are only allowed if government-appointed experts approve a company-provided study quantifying the annual meltdown risk as less than one in a million; similarly, the FDA only approves drugs whose side effects are quantified below an acceptable level. There are many promising approaches to providing such quantitative AI safety guarantees (Dalrymple et al. 2024).\r\n\r\nTier 3 (AGI) is much more risky because it can broadly match or exceed human ability, deceive people, create long-term plans, and act autonomously in pursuit of goals—which by default include self-preservation and resource acquisition. This is why top AI researchers have warned that AGI may escape human control and even cause extinction. To guarantee that AGI remains controllable, a Tier 3 system must be mathematically proven to be controllable using formal verification—which includes proving that it will never resist being turned off.  \r\n\r\nJust as AI progress has revolutionized our ability to auto-generate text, images, video and code, it will soon revolutionize our ability to auto-generate code and proofs that meet user-provided specifications. In other words, rather than deploying unverifiable black-box neural networks, it may soon be possible to have AI systems write deployable formally verifiable code, implementing powerful machine-learned algorithms and knowledge (see Tegmark & Omohundro 2023, and provablysafe.ai for an overview of the field).", "metadata": {"original_filename": "item108_US_Safety Standards Delivering Controllable and Beneficial AI Tools.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T22:21:33.272715", "updated_at": "2025-08-28T22:21:33.272715", "word_count": 11945}