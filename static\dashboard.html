<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档分析可视化仪表板 - 智能数据洞察</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --bg-primary: rgba(255, 255, 255, 0.95);
            --shadow-lg: 0 20px 25px rgba(0, 0, 0, 0.15), 0 10px 10px rgba(0, 0, 0, 0.04);
            --border-radius: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Inter', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--accent-color) 100%);
            background-attachment: fixed;
            min-height: 100vh;
            padding: 20px;
            color: var(--text-primary);
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
        }
        
        .header {
            background: var(--bg-primary);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-lg);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }
        
        h1 {
            color: var(--text-primary);
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .controls {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
        }
        
        .controls input,
        .controls button {
            padding: 12px 24px;
            border: 2px solid var(--primary-color);
            border-radius: 50px;
            font-size: 1rem;
            background: var(--bg-primary);
            color: var(--text-primary);
            cursor: pointer;
            transition: var(--transition);
            font-weight: 500;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .controls button {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .controls button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .controls button.active {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(700px, 1fr));
            gap: 25px;
            margin-bottom: 25px;
        }
        
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .chart-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .chart-title {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .chart {
            width: 100%;
            height: 450px;
        }
        
        .key-findings {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .key-findings h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .findings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        
        .finding-card {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .finding-card:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }
        
        .finding-type {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 10px;
            text-transform: uppercase;
            font-size: 0.9rem;
        }
        
        .finding-items {
            list-style: none;
        }
        
        .finding-items li {
            padding: 5px 0;
            color: #555;
        }
        
        .finding-items li:before {
            content: "✓ ";
            color: #667eea;
            font-weight: bold;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 50px;
            color: white;
            font-size: 1.2rem;
        }
        
        .error {
            display: none;
            background: #ff6b6b;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 10px;
            animation: pulse 2s infinite;
        }
        
        .status-indicator.success {
            background: #51cf66;
        }
        
        .status-indicator.error {
            background: #ff6b6b;
        }
        
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(81, 207, 102, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(81, 207, 102, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(81, 207, 102, 0);
            }
        }
        
        .chart-icon {
            font-size: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 文档分析可视化仪表板 <span id="statusIndicator" class="status-indicator"></span></h1>
            <div class="controls">
                <input type="text" id="docId" placeholder="输入文档ID (例如: doc_123)">
                <button onclick="loadAnalysisResults()">📥 加载分析结果</button>
                <button onclick="loadMockData()" class="active">🎭 演示数据</button>
                <button onclick="refreshCharts()">🔄 刷新图表</button>
            </div>
        </div>
        
        <div id="loading" class="loading">
            ⏳ 正在加载分析结果...
        </div>
        
        <div id="error" class="error"></div>
        
        <div class="dashboard" id="dashboard">
            <div class="chart-container">
                <h2 class="chart-title">
                    <span class="chart-icon">🕸️</span>
                    行为者关系网络
                </h2>
                <div id="actorRelationChart" class="chart"></div>
            </div>
            
            <div class="chart-container">
                <h2 class="chart-title">
                    <span class="chart-icon">🎭</span>
                    角色框架分析
                </h2>
                <div id="roleFramingChart" class="chart"></div>
            </div>
            
            <div class="chart-container">
                <h2 class="chart-title">
                    <span class="chart-icon">🎯</span>
                    问题范围策略
                </h2>
                <div id="problemScopeChart" class="chart"></div>
            </div>
            
            <div class="chart-container">
                <h2 class="chart-title">
                    <span class="chart-icon">🔗</span>
                    因果机制分析
                </h2>
                <div id="causalMechanismChart" class="chart"></div>
            </div>
        </div>
        
        <div class="key-findings" id="keyFindings">
            <h2>🔍 关键发现</h2>
            <div class="findings-grid" id="findingsGrid"></div>
        </div>
    </div>
    
    <script>
        // 图表实例
        let charts = {};
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            loadMockData(); // 默认加载演示数据
        });
        
        // 初始化图表
        function initCharts() {
            charts.actorRelation = echarts.init(document.getElementById('actorRelationChart'));
            charts.roleFraming = echarts.init(document.getElementById('roleFramingChart'));
            charts.problemScope = echarts.init(document.getElementById('problemScopeChart'));
            charts.causalMechanism = echarts.init(document.getElementById('causalMechanismChart'));
            
            // 响应式调整
            window.addEventListener('resize', () => {
                Object.values(charts).forEach(chart => chart.resize());
            });
        }
        
        // 加载分析结果
        async function loadAnalysisResults() {
            const docId = document.getElementById('docId').value;
            if (!docId) {
                showError('请输入文档ID');
                return;
            }
            
            showLoading(true);
            hideError();
            updateStatus('loading');
            
            try {
                const response = await fetch(`/api/visualization/dashboard/${docId}`);
                if (!response.ok) {
                    throw new Error('无法获取分析结果');
                }
                
                const data = await response.json();
                if (data.success && data.dashboard) {
                    visualizeDashboard(data.dashboard);
                    updateStatus('success');
                } else {
                    throw new Error('数据格式错误');
                }
            } catch (error) {
                showError(error.message);
                updateStatus('error');
            } finally {
                showLoading(false);
            }
        }
        
        // 加载演示数据
        async function loadMockData() {
            showLoading(true);
            hideError();
            updateStatus('loading');
            
            try {
                const response = await fetch('/api/visualization/mock/demo');
                if (!response.ok) {
                    throw new Error('无法加载演示数据');
                }
                
                const data = await response.json();
                if (data.success && data.dashboard) {
                    visualizeDashboard(data.dashboard);
                    updateStatus('success');
                    document.getElementById('docId').value = 'mock_demo';
                } else {
                    throw new Error('演示数据格式错误');
                }
            } catch (error) {
                showError(error.message);
                updateStatus('error');
            } finally {
                showLoading(false);
            }
        }
        
        // 可视化仪表板
        function visualizeDashboard(dashboard) {
            const chartConfigs = dashboard.charts || {};
            
            // 渲染各个图表
            if (chartConfigs.actor_relation) {
                charts.actorRelation.setOption(chartConfigs.actor_relation);
            }
            
            if (chartConfigs.role_framing) {
                charts.roleFraming.setOption(chartConfigs.role_framing);
            }
            
            if (chartConfigs.problem_scope) {
                charts.problemScope.setOption(chartConfigs.problem_scope);
            }
            
            if (chartConfigs.causal_mechanism) {
                charts.causalMechanism.setOption(chartConfigs.causal_mechanism);
            }
            
            // 显示关键发现
            displayKeyFindings(chartConfigs);
        }
        
        // 显示关键发现
        function displayKeyFindings(chartConfigs) {
            const findingsGrid = document.getElementById('findingsGrid');
            findingsGrid.innerHTML = '';
            
            const findingsMap = {
                'actor_relation': '行为者关系',
                'role_framing': '角色框架',
                'problem_scope': '问题范围',
                'causal_mechanism': '因果机制'
            };
            
            Object.entries(chartConfigs).forEach(([key, config]) => {
                if (config && config.series) {
                    const card = document.createElement('div');
                    card.className = 'finding-card';
                    
                    const type = document.createElement('div');
                    type.className = 'finding-type';
                    type.textContent = findingsMap[key] || key;
                    
                    const items = document.createElement('ul');
                    items.className = 'finding-items';
                    
                    // 提取关键信息
                    let findings = [];
                    if (key === 'actor_relation' && config.series[0].data) {
                        findings = config.series[0].data.slice(0, 3).map(d => d.name || d.id);
                    } else if (key === 'role_framing' && config.series[0].data) {
                        findings = config.series[0].data.map(d => `${d.name}: ${d.value}`);
                    } else if (key === 'problem_scope' && config.series[0].data) {
                        findings = config.xAxis.data.map((cat, i) => `${cat}: ${config.series[0].data[i]}`);
                    } else if (key === 'causal_mechanism' && config.series[0].data) {
                        findings = ['因果链已识别', '责任框架已建立'];
                    }
                    
                    findings.forEach(finding => {
                        const li = document.createElement('li');
                        li.textContent = finding;
                        items.appendChild(li);
                    });
                    
                    card.appendChild(type);
                    card.appendChild(items);
                    findingsGrid.appendChild(card);
                }
            });
        }
        
        // 刷新图表
        function refreshCharts() {
            Object.values(charts).forEach(chart => {
                chart.resize();
            });
        }
        
        // 显示加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        // 显示错误信息
        function showError(message) {
            const errorEl = document.getElementById('error');
            errorEl.textContent = `❌ ${message}`;
            errorEl.style.display = 'block';
        }
        
        // 隐藏错误信息
        function hideError() {
            document.getElementById('error').style.display = 'none';
        }
        
        // 更新状态指示器
        function updateStatus(status) {
            const indicator = document.getElementById('statusIndicator');
            indicator.className = `status-indicator ${status}`;
        }
    </script>
</body>
</html>
