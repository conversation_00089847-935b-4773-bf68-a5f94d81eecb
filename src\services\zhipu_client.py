import zhipuai
import logging
import json
import time
import re
import os
from typing import Dict, Any, List, Optional, Union
import asyncio
from pathlib import Path

from src.core.config import settings

logger = logging.getLogger(__name__)


class ZhipuAIClient:
    """智谱AI API客户端，负责与智谱AI服务进行交互"""

    def __init__(self, api_key: Optional[str] = None):
        """初始化智谱AI客户端
        
        Args:
            api_key: 智谱AI API密钥，如不提供则从环境变量获取
        """
        # 优先使用传入的api_key，其次是临时API密钥，最后是配置文件
        self.api_key = api_key or os.environ.get('TEMP_ZHIPUAI_API_KEY') or settings.ZHIPUAI_API_KEY
        # 获取base URL，支持自定义
        self.base_url = os.environ.get('ZHIPUAI_BASE_URL', 'https://open.bigmodel.cn/api/paas/v4')
        
        # 使用新版SDK的配置方式
        try:
            from zhipuai import ZhipuAI
            self.client = ZhipuAI(api_key=self.api_key)
            logger.info(f"智谱AI客户端初始化完成，使用Base URL: {self.base_url}")
        except ImportError:
            # 如果是旧版SDK
            zhipuai.api_key = self.api_key
            self.client = None
            logger.info("智谱AI客户端初始化完成（旧版SDK）")
            
        self.max_retries = settings.MAX_RETRIES
        self.request_timeout = settings.REQUEST_TIMEOUT

    async def chat_completion(
        self, 
        messages: List[Dict[str, str]], 
        model: str = "glm-4",
        temperature: float = 0.7,
        max_tokens: int = 2048,
        top_p: float = 0.7,
        retry_count: int = 0
    ) -> Dict[str, Any]:
        """异步发送对话请求到智谱AI
        
        Args:
            messages: 对话消息列表
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大生成token数
            top_p: 采样参数
            retry_count: 当前重试次数
            
        Returns:
            API响应结果
        """
        try:
            logger.debug(f"发送请求到智谱AI: {messages[-1]['content'][:50]}...")
            
            # 检查使用哪个版本的SDK
            if self.client:  # 新版SDK
                response = await asyncio.to_thread(
                    self.client.chat.completions.create,
                    model=model,
                    messages=messages,
                    temperature=temperature,
                    top_p=top_p,
                    max_tokens=max_tokens
                )
                
                # 新版SDK返回的是对象，需要转换
                result_data = {
                    "choices": [
                        {"content": response.choices[0].message.content}
                    ]
                }
                logger.debug(f"智谱AI响应成功: {result_data['choices'][0]['content'][:50]}...")
                return {"data": result_data, "success": True}
            else:  # 旧版SDK
                response = await asyncio.to_thread(
                    zhipuai.model_api.invoke,
                    model=model,
                    prompt=messages,
                    temperature=temperature,
                    top_p=top_p,
                    max_tokens=max_tokens
                )
                
                if response.get("code") != 200:
                    logger.error(f"智谱AI请求失败: {response.get('msg', '未知错误')}")
                    return {"error": response.get("msg", "未知错误"), "success": False}
                    
                logger.debug(f"智谱AI响应成功: {response.get('data', {}).get('choices', [{}])[0].get('content', '')[:50]}...")
                return {"data": response.get("data", {}), "success": True}
            
        except Exception as e:
            logger.error(f"智谱AI请求异常: {str(e)}")
            
            # 重试逻辑
            if retry_count < self.max_retries:
                retry_delay = 2 ** retry_count  # 指数退避策略
                logger.info(f"等待 {retry_delay} 秒后重试 (重试 {retry_count + 1}/{self.max_retries})")
                await asyncio.sleep(retry_delay)
                return await self.chat_completion(
                    messages=messages,
                    model=model,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    top_p=top_p,
                    retry_count=retry_count + 1
                )
            
            return {"error": str(e), "success": False}
    
    async def analyze_document(
        self, 
        text: str, 
        task_type: str,
        prompt_template: str,
        model: str = "glm-4"
    ) -> Dict[str, Any]:
        """使用智谱AI分析文档
        
        Args:
            text: 文档文本内容
            task_type: 分析任务类型
            prompt_template: 提示词模板
            model: 使用的模型
            
        Returns:
            分析结果
        """
        # 构建提示词，使用安全的字符串替换
        prompt = prompt_template.replace("{document}", text)
        
        # 构建消息
        messages = [
            {"role": "system", "content": "你是一个专业的文档分析助手，擅长分析政策文档的叙事结构和话语策略。请提供客观、准确的分析结果。"},
            {"role": "user", "content": prompt}
        ]
        
        # 发送请求
        response = await self.chat_completion(
            messages=messages,
            model=model,
            temperature=0.2,  # 使用较低的温度以获得更确定性的回答
            max_tokens=4096,  # 使用更大的token限制以确保完整的分析
        )
        
        if not response.get("success", False):
            logger.error(f"文档分析失败: {response.get('error', '未知错误')}")
            return {"error": response.get("error", "分析失败"), "success": False}
        
        # 提取分析结果
        try:
            content = response["data"]["choices"][0]["content"]
            logger.info(f"智谱AI原始响应 (前500字符): {content[:500]}...")
            
            # 尝试解析JSON结果
            try:
                # 首先尝试直接解析整个响应（如果AI遵守了指示只返回JSON）
                try:
                    result = json.loads(content)
                    logger.info(f"成功直接解析JSON结果，包含键: {list(result.keys())}")
                except json.JSONDecodeError:
                    # 如果直接解析失败，尝试查找JSON部分
                    json_start = content.find("{")
                    json_end = content.rfind("}") + 1
                    
                    if json_start >= 0 and json_end > json_start:
                        json_str = content[json_start:json_end]
                        
                        # 清理JSON字符串
                        # 1. 移除单行注释
                        json_str = re.sub(r'//.*?(?=\n|$)', '', json_str)
                        # 2. 移除多行注释
                        json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)
                        # 3. 修复省略号
                        json_str = re.sub(r'\.\.\.(?="|\]|\})', '', json_str)
                        # 4. 移除末尾逗号
                        json_str = re.sub(r',\s*([}\]])', r'\1', json_str)
                        # 5. 确保字符串值中的引号被转义
                        # 但这个很复杂，暂时跳过
                        
                        logger.debug(f"清理后的JSON (前500字符): {json_str[:500]}...")
                        
                        # 再次尝试解析
                        try:
                            result = json.loads(json_str)
                            logger.info(f"成功解析清理后的JSON结果，包含键: {list(result.keys())}")
                        except json.JSONDecodeError as parse_error:
                            # 如果还是失败，尝试更激进的清理
                            lines = json_str.split('\n')
                            clean_lines = []
                            for line in lines:
                                # 移除明显的注释行
                                stripped = line.strip()
                                if stripped.startswith('//'):
                                    continue
                                # 移除包含省略号的行
                                if '// ...' in line or '//...' in line:
                                    continue
                                clean_lines.append(line)
                            
                            final_json = '\n'.join(clean_lines)
                            # 再次移除末尾逗号
                            final_json = re.sub(r',\s*([}\]])', r'\1', final_json)
                            
                            try:
                                result = json.loads(final_json)
                                logger.info(f"成功解析最终清理的JSON结果，包含键: {list(result.keys())}")
                            except:
                                logger.warning(f"JSON解析失败: {parse_error}，返回原始内容")
                                logger.debug(f"尝试解析的JSON: {final_json[:1000]}")
                                result = {"raw_analysis": content}
                    else:
                        # 如果没有找到JSON格式，则返回原始内容
                        logger.warning("响应中未找到JSON格式内容")
                        result = {"raw_analysis": content}
                        
            except Exception as e:
                logger.error(f"解析结果时发生错误: {str(e)}")
                logger.debug(f"原始响应内容: {content[:1000]}")
                result = {"raw_analysis": content, "error": str(e)}
                
            return {
                "task_type": task_type,
                "result": result,
                "success": True
            }
        except Exception as e:
            logger.error(f"解析分析结果失败: {str(e)}")
            logger.error(f"响应结构: {response.keys() if isinstance(response, dict) else type(response)}")
            return {"error": str(e), "success": False, "raw_response": str(response)[:500]}


# 创建客户端实例
async def get_zhipu_client() -> ZhipuAIClient:
    """获取智谱AI客户端实例"""
    return ZhipuAIClient()
