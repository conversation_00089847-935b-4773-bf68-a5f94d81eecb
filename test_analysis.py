import asyncio
import json
from src.services.zhipu_client import ZhipuAIClient
from src.services.analysis_service import AnalysisService, TaskType

async def test_analysis():
    """测试分析功能并输出详细信息"""
    
    # 测试文本
    test_text = """
    政府将加强对科技企业的监管，确保其合规经营。
    同时，政府也将为科技创新提供更多的支持和资源。
    民营企业应当承担更多的社会责任，为社会发展做出贡献。
    """
    
    # 创建分析服务
    zhipu_client = ZhipuAIClient()
    service = AnalysisService(zhipu_client)
    
    # 测试每个任务类型
    for task_type in [TaskType.ACTOR_RELATION, TaskType.ROLE_FRAMING, TaskType.PROBLEM_SCOPE, TaskType.CAUSAL_MECHANISM]:
        print(f"\n{'='*60}")
        print(f"测试任务类型: {task_type.value}")
        print(f"{'='*60}")
        
        # 获取提示模板
        prompt_template = service.get_prompt_template(task_type)
        # 使用安全的字符串替换，避免JSON格式冲突
        prompt = prompt_template.replace("{document}", test_text)
        
        print("提示词前200字符:")
        print(prompt[:200])
        print("...")
        
        # 直接调用智谱AI客户端
        try:
            response = await zhipu_client.analyze_document(
                text=test_text,
                task_type=task_type.value,
                prompt_template=prompt_template
            )
            
            print(f"\n响应成功状态: {response.get('success', False)}")
            
            if response.get('success'):
                result = response.get('result', {})
                print(f"结果类型: {type(result)}")
                print(f"结果键: {list(result.keys()) if isinstance(result, dict) else 'N/A'}")
                
                # 打印前500字符的结果
                result_str = json.dumps(result, ensure_ascii=False, indent=2) if isinstance(result, dict) else str(result)
                print(f"\n结果内容（前500字符）:")
                print(result_str[:500])
            else:
                print(f"错误: {response.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"异常: {str(e)}")
            import traceback
            traceback.print_exc()
        
        await asyncio.sleep(2)  # 等待2秒避免请求过快

if __name__ == "__main__":
    asyncio.run(test_analysis())
