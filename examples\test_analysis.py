import os
import sys
import json
import asyncio
import requests
from pathlib import Path

# 确保能够导入src目录下的模块
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.models.schemas import DocumentCreate, TaskType
from src.services.document_service import DocumentService
from src.services.analysis_service import AnalysisService
from src.services.zhipu_client import ZhipuAIClient


async def test_analysis():
    """测试文档分析功能"""
    print("开始测试文档分析功能")
    
    # 创建服务实例
    document_service = DocumentService()
    zhipu_client = ZhipuAIClient()
    analysis_service = AnalysisService(zhipu_client)
    
    # 读取示例文档
    sample_path = Path(__file__).parent / "sample_document.txt"
    with open(sample_path, "r", encoding="utf-8") as f:
        text = f.read()
    
    # 使用时间戳创建唯一文档ID
    import time
    doc_id = f"doc_{int(time.time())}"
    
    # 创建文档
    doc = DocumentCreate(
        doc_id=doc_id,
        title="关于加强人工智能发展治理的指导意见",
        text=text,
        metadata={"source": "测试", "type": "政策文件"}
    )
    
    # 保存文档
    try:
        doc_result = await document_service.create_document(doc)
        print(f"文档创建成功: {doc_result.doc_id}")
    except ValueError as e:
        print(f"文档创建失败: {str(e)}")
        # 如果文档已存在，尝试获取它
        try:
            doc_result = await document_service.get_document(doc_id)
            print(f"获取已存在的文档: {doc_result.doc_id}")
        except Exception as e2:
            print(f"无法获取文档: {str(e2)}")
            return
    
    # 执行分析任务（这里只测试行为者关系分析）
    task_type = TaskType.ACTOR_RELATION
    print(f"开始执行分析任务: {task_type}")
    
    result = await analysis_service.analyze(doc_result.text, task_type)
    print(f"分析完成，结果: {json.dumps(result.dict(), ensure_ascii=False, indent=2)}")
    
    print("测试完成")


async def test_api(host="http://localhost:8000"):
    """测试API接口"""
    print("\n开始测试API接口")
    
    # 测试健康检查接口
    try:
        response = requests.get(f"{host}/health")
        print(f"健康检查接口响应: {response.status_code}")
        print(response.json())
    except Exception as e:
        print(f"健康检查接口测试失败: {str(e)}")
    
    # 测试创建文档接口
    try:
        # 读取示例文档
        sample_path = Path(__file__).parent / "sample_document.txt"
        with open(sample_path, "r", encoding="utf-8") as f:
            text = f.read()
        
        # 创建文档
        doc_data = {
            "doc_id": "api_test_001",
            "title": "API测试文档",
            "text": text,
            "metadata": {"source": "API测试"}
        }
        
        response = requests.post(f"{host}/api/v1/documents/", json=doc_data)
        print(f"创建文档接口响应: {response.status_code}")
        print(json.dumps(response.json(), ensure_ascii=False, indent=2)[:200] + "...")
        
        doc_id = response.json()["doc_id"]
        
        # 测试分析接口
        analysis_data = {
            "doc_id": doc_id,
            "tasks": ["actor_relation"]
        }
        
        response = requests.post(f"{host}/api/v1/analysis/", json=analysis_data)
        print(f"分析接口响应: {response.status_code}")
        if response.status_code == 200:
            print("分析请求成功")
        else:
            print(json.dumps(response.json(), ensure_ascii=False, indent=2))
            
    except Exception as e:
        print(f"API接口测试失败: {str(e)}")
    
    print("API测试完成")


if __name__ == "__main__":
    # 测试本地函数
    asyncio.run(test_analysis())
    
    # 测试API接口（需要先启动服务器）
    # asyncio.run(test_api())
