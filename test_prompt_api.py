#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试提示词管理API功能
"""

import asyncio
import json
import aiohttp
import time

API_BASE = "http://127.0.0.1:8000/api/v1/prompts/prompts/"

async def test_prompt_management():
    """测试提示词管理功能"""
    
    print("=== 测试提示词管理API功能 ===")
    
    async with aiohttp.ClientSession() as session:
        
        # 1. 测试获取提示词列表
        print("\n1. 测试获取提示词列表...")
        try:
            async with session.get(API_BASE) as response:
                if response.status == 200:
                    prompts = await response.json()
                    print(f"成功获取 {len(prompts)} 个提示词")
                    for prompt in prompts[:2]:  # 只显示前两个
                        print(f"   - {prompt['name']} ({prompt['task_type']})")
                else:
                    print(f"获取提示词列表失败: {response.status}")
        except Exception as e:
            print(f"获取提示词列表异常: {e}")
        
        # 2. 测试创建提示词
        print("\n2. 测试创建新提示词...")
        new_prompt = {
            "task_type": "actor_relation",
            "name": "API测试提示词",
            "description": "通过API创建的测试提示词",
            "template": "请分析以下文档：\n{document}\n\n返回JSON格式结果。",
            "tags": ["API测试", "自动化"]
        }
        
        try:
            async with session.post(API_BASE, json=new_prompt) as response:
                if response.status == 200:
                    created_prompt = await response.json()
                    prompt_id = created_prompt['id']
                    print(f"成功创建提示词: {prompt_id}")
                    
                    # 3. 测试获取单个提示词
                    print(f"\n3. 测试获取单个提示词 ({prompt_id})...")
                    async with session.get(f"{API_BASE}{prompt_id}") as response:
                        if response.status == 200:
                            prompt = await response.json()
                            print(f"成功获取提示词: {prompt['name']}")
                        else:
                            print(f"获取提示词失败: {response.status}")
                    
                    # 4. 测试更新提示词
                    print(f"\n4. 测试更新提示词 ({prompt_id})...")
                    update_data = {
                        "description": "更新后的描述",
                        "tags": ["API测试", "自动化", "已更新"]
                    }
                    async with session.put(f"{API_BASE}{prompt_id}", json=update_data) as response:
                        if response.status == 200:
                            updated_prompt = await response.json()
                            print(f"成功更新提示词，版本: {updated_prompt['version']}")
                        else:
                            print(f"更新提示词失败: {response.status}")
                    
                    # 5. 测试删除提示词
                    print(f"\n5. 测试删除提示词 ({prompt_id})...")
                    async with session.delete(f"{API_BASE}{prompt_id}") as response:
                        if response.status == 200:
                            print("成功删除提示词")
                        else:
                            print(f"删除提示词失败: {response.status}")
                            
                else:
                    error_text = await response.text()
                    print(f"创建提示词失败: {response.status}, {error_text}")
        except Exception as e:
            print(f"创建提示词异常: {e}")
        
        # 6. 测试提示词比较功能
        print("\n6. 测试提示词比较功能...")
        
        # 首先获取两个提示词进行比较
        try:
            async with session.get(API_BASE) as response:
                if response.status == 200:
                    prompts = await response.json()
                    if len(prompts) >= 2:
                        prompt1_id = prompts[0]['id']
                        prompt2_id = prompts[1]['id']
                        
                        compare_data = {
                            "prompt1_id": prompt1_id,
                            "prompt2_id": prompt2_id,
                            "test_document": "政府将加强对科技企业的监管，确保其合规经营。"
                        }
                        
                        async with session.post(f"{API_BASE}compare", json=compare_data) as response:
                            if response.status == 200:
                                result = await response.json()
                                print(f"成功比较提示词，相似度: {result['similarity_score']:.2%}")
                                print(f"推荐: {result['recommendation'][:50]}...")
                            else:
                                print(f"比较提示词失败: {response.status}")
                    else:
                        print("提示词数量不足，跳过比较测试")
        except Exception as e:
            print(f"比较提示词异常: {e}")
        
        print("\n=== 提示词管理API功能测试完成 ===")

if __name__ == "__main__":
    asyncio.run(test_prompt_management())