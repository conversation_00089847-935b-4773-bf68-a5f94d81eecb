#!/usr/bin/env python3
"""
简化的分析测试脚本
"""
import requests
import json

def test_simple_analysis():
    """测试简单的文档分析"""
    print("🧪 开始简化分析测试...")
    
    # 1. 创建文档
    print("📝 创建测试文档...")
    doc_data = {
        "title": "AI政策测试文档",
        "text": "政府将加强对AI企业的监管，确保其合规经营。同时，政府也将为AI创新提供支持。企业应承担社会责任。",
        "metadata": {"test": True}
    }
    
    try:
        response = requests.post("http://localhost:8000/api/v1/documents/", json=doc_data, timeout=10)
        if response.status_code == 201:
            doc_result = response.json()
            doc_id = doc_result["doc_id"]
            print(f"✅ 文档创建成功: {doc_id}")
        else:
            print(f"❌ 文档创建失败: {response.status_code} - {response.text}")
            return
    except Exception as e:
        print(f"❌ 文档创建异常: {e}")
        return
    
    # 2. 分析文档
    print("🔍 开始文档分析...")
    analysis_data = {
        "doc_id": doc_id,
        "tasks": ["actor_relation"]
    }
    
    try:
        response = requests.post("http://localhost:8000/api/v1/analysis/", json=analysis_data, timeout=60)
        if response.status_code == 200:
            result = response.json()
            print("✅ 文档分析成功!")
            print(f"📊 分析结果概览:")
            
            # 显示结果统计
            if 'results' in result:
                for task_type, task_result in result['results'].items():
                    if task_result.get('success'):
                        task_data = task_result.get('result', {})
                        actors_count = len(task_data.get('actors', []))
                        relations_count = len(task_data.get('relations', []))
                        findings_count = len(task_data.get('key_findings', []))
                        
                        print(f"  - {task_type}: {actors_count}个行为者, {relations_count}个关系, {findings_count}个关键发现")
                    else:
                        print(f"  - {task_type}: 分析失败 - {task_result.get('error', '未知错误')}")
            
        else:
            print(f"❌ 文档分析失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 文档分析异常: {e}")
    
    # 3. 获取分析结果
    print("📤 获取保存的分析结果...")
    try:
        response = requests.get(f"http://localhost:8000/api/v1/analysis/results/{doc_id}", timeout=10)
        if response.status_code == 200:
            result = response.json()
            print("✅ 成功获取保存的分析结果")
        else:
            print(f"⚠️  获取结果失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ 获取结果异常: {e}")
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    test_simple_analysis()
