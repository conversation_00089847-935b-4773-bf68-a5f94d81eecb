from fastapi import APIRouter, Depends
from typing import Dict, Any
import logging
import platform
import psutil
import os
from datetime import datetime

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/", response_model=Dict[str, Any])
async def health_check():
    """健康检查接口"""
    try:
        # 系统信息
        health_info = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "system_info": {
                "platform": platform.platform(),
                "python_version": platform.python_version(),
                "processor": platform.processor()
            },
            "resources": {
                "cpu_percent": psutil.cpu_percent(),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_percent": psutil.disk_usage('/').percent
            }
        }
        
        logger.debug("健康检查请求成功")
        return health_info
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }
