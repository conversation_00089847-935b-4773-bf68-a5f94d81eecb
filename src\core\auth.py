"""
身份验证模块：FastAPI依赖项和中间件
"""
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from fastapi.security import APIKeyHeader
from typing import Optional, Dict, Any
import time
from src.core.security import security_service
from src.core.config import settings
import logging

logger = logging.getLogger(__name__)

# JWT Bearer令牌认证
security = HTTPBearer()

# API密钥认证
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)

class AuthDependencies:
    """身份验证依赖项类"""
    
    @staticmethod
    async def get_current_user(
        credentials: HTTPAuthorizationCredentials = Depends(security)
    ) -> Dict[str, Any]:
        """
        获取当前用户信息
        
        Args:
            credentials: HTTP授权凭据
            
        Returns:
            用户信息字典
            
        Raises:
            HTTPException: 认证失败
        """
        try:
            # 验证JWT令牌
            payload = security_service.verify_token(credentials.credentials)
            
            # 提取用户信息
            user_id: str = payload.get("sub")
            if user_id is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效的令牌载荷",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            # 返回用户信息
            return {
                "user_id": user_id,
                "username": payload.get("username"),
                "role": payload.get("role", "user"),
                "permissions": payload.get("permissions", [])
            }
            
        except Exception as e:
            logger.error(f"身份验证失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="身份验证失败",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    @staticmethod
    async def get_api_key(
        api_key: Optional[str] = Depends(api_key_header)
    ) -> str:
        """
        验证API密钥
        
        Args:
            api_key: API密钥
            
        Returns:
            API密钥字符串
            
        Raises:
            HTTPException: API密钥无效
        """
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="缺少API密钥",
            )
        
        # 这里可以添加API密钥验证逻辑
        # 暂时简单验证非空
        if len(api_key) < 32:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的API密钥",
            )
        
        return api_key
    
    @staticmethod
    async def get_current_active_user(
        current_user: Dict[str, Any] = Depends(get_current_user)
    ) -> Dict[str, Any]:
        """
        获取当前活跃用户
        
        Args:
            current_user: 当前用户信息
            
        Returns:
            用户信息字典
            
        Raises:
            HTTPException: 用户被禁用
        """
        if not current_user.get("is_active", True):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户账户已被禁用"
            )
        return current_user
    
    @staticmethod
    async def require_admin(
        current_user: Dict[str, Any] = Depends(get_current_active_user)
    ) -> Dict[str, Any]:
        """
        要求管理员权限
        
        Args:
            current_user: 当前用户信息
            
        Returns:
            用户信息字典
            
        Raises:
            HTTPException: 权限不足
        """
        if current_user.get("role") != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要管理员权限"
            )
        return current_user

class RateLimiter:
    """简单的内存速率限制器"""
    
    def __init__(self):
        self.requests: Dict[str, list] = {}
        self.max_requests = 100  # 每分钟最大请求数
        self.window_size = 60    # 时间窗口（秒）
    
    async def check_rate_limit(self, request: Request, key: str = None) -> bool:
        """
        检查速率限制
        
        Args:
            request: FastAPI请求对象
            key: 速率限制键（默认使用客户端IP）
            
        Returns:
            是否允许请求
        """
        if key is None:
            key = request.client.host
        
        current_time = time.time()
        
        # 清理过期记录
        if key in self.requests:
            self.requests[key] = [
                req_time for req_time in self.requests[key]
                if current_time - req_time < self.window_size
            ]
        else:
            self.requests[key] = []
        
        # 检查是否超过限制
        if len(self.requests[key]) >= self.max_requests:
            return False
        
        # 记录请求
        self.requests[key].append(current_time)
        return True

# 创建速率限制器实例
rate_limiter = RateLimiter()

# 创建身份验证依赖项实例
auth_deps = AuthDependencies()