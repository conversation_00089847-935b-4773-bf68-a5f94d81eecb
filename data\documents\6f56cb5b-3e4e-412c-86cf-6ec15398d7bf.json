{"doc_id": "6f56cb5b-3e4e-412c-86cf-6ec15398d7bf", "title": "item034_US_A report of the Cyberinfrastructure Research and Innovation Working Group, Advisory Committee for Cyberinfrastructure", "text": "3. “Virtuous Cycles” and Scale   Discovery in science and engineering has always placed unique—and often extreme—demands on computing hardware and software technologies. But because discovery represents a relatively small “market share” (in dollars, in visibility, etc.), it tends not to be the natural focus of computer scientists, computer engineers, statisticians, and others dedicated to advancing computing technology. At the same time, researchers who rely on computing technology to drive advances in their field are unlikely to be at the forefront of computing advances, particularly in emerging areas such as AI and ML. The federal government—particularly the Department of Energy and NSF—has played a crucial role in bridging those who advance computing technology and those who employ computing technology to advance discovery in other fields of science and engineering. The goal—the imperative—is to create a “virtuous cycle” in which advances in computing drive advances in science and engineering, which in turn drive and inform further advances in computing. Innovation in AI has exploded in recent years, thanks largely to the advent of “deep learning.” Bringing this innovation to bear on discovery is essential to advancing science and engineering and vital to our Nation's competitiveness and leadership across a broad range of important fields. OAC should partner with the Directorate for Computer & Information Science & Engineering (CISE) and the other NSF Directorates to partner researchers in AI, ML, and data science with researchers in science and engineering disciplines. These partnerships, at scale, have not been a strength of NSF. For example, the “virtuous cycle” in data-intensive discovery was catalyzed by awards from the Gordon and Betty Moore Foundation and the Alfred P. Sloan Foundation after NSF failed to fund multiple cross-disciplinary proposals in this area. Decades previously, the Whittaker Foundation stepped in to create the field of bioengineering. 11 Opportunities in AI and ML 4. Recommendations  Investments in the six high-priority research themes below will fill gaps in the OAC research program. They should be made in collaboration with CISE and the various NSF Directorates to partner researchers in AI, ML, and data science with researchers in science and engineering disciplines. The goal is to establish “virtuous cycles\" where advances in computing drive advances in science and engineering, which in turn drive and inform further advances in computing. Advance the development of predictive models and data-model integration. Predictive models and data-model integration remain foundational tools across many science, engineering, and medicine areas. Research in areas such as advanced data assimilation, uncertainty quantification, multi-scale multi-physics approaches, and adapting existing state-of-the-art algorithms will enable scientists and engineers to fully leverage computational science, data science, AI, and ML.  Robust Data Harnessing and Domain-Specific CI “Prototypes.” (a) Develop robust methods of data handling covering all necessary aspects, (b) Support the development of more domain-specific data repositories that model the tremendous success of efforts such as the Protein Data Bank, (c) Develop domain-specific CI prototypes that can be replicated to other domains with adjustments. (d) Help catalyze cross-disciplinary discussions on data harnessing and within domains, but in the broadest sense, with international partnerships. Trust and Explainability for AI. Partner with CISE and the Directorate for Mathematical & Physical Sciences (MPS) to develop new techniques and methods for the assurance of AI models used to accelerate scientific discovery. Also, it is crucial to partner with the science and engineering directorates that can leverage these new techniques to accelerate advances. Machine Learning Across the Cyber Infrastructure for S&E. To address future facilities challenges with edge computing and enable MLCI architecture and software research, OAC should consider all modalities to deliver MLCI testbeds to the research community, including partnering (with other government agencies and industry), capitalization, and renting, and continue this practice as new, more capable systems architectures for AI-based discovery emerge. Deeply Network-aware Workflow Management Systems and Software-Defined Cyberinfrastructure and Intelligent Networks. Foster the development of Intelligent, self-managed CI subsystems by supporting partnerships among cyberinfrastructure facilities and professionals, applied AI and ML experts, and application domain scientists. Requirements from CI technology and application workflows need to drive the use of AI and ML techniques via multidisciplinary teams developing community testbeds. Extended time awards will be ideal for making new, experimental resources available to the communities of interest and share insights and solutions. New Models and Paradigms for S&E Discovery Based on AI. Partner researchers in AI, ML, and/or data science with researchers in science and engineering disciplines to adapt forefront approaches in AI, ML, and/or data science to the requirements of scientific and engineering discovery. These should be largescale awards in size and duration to attract top researchers' attention in methodology and application. 12 Opportunities in AI and ML 5. Suggested Connections and Touchpoints between OAC and NSF  The working group identified connections and touchpoints between OAC and other NSF directorates to improve OAC's effectiveness. These efforts are considered essential but are of lower priority than the themes mentioned above. (a) Modifying Workflows at the Application Level for Data-Centric and Distributed Operation Currently, there is a focus on providing the HPC community with exascale systems by 2021-2022. Simultaneously, we see the emergence of AI accelerators, cloud systems, and edge computing. It is expected that future CI resources will be distributed and heterogeneous. To facilitate the efficient use of such systems, it is essential to provide workflows that allow science applications to use the given resources to manage and analyze data easily. The data can be generated from sensors, instruments, experiments, or simulations. It is important to provide workflow frameworks that are seamless to domain scientists. Relevance to OAC: OAC should take the lead in making available frameworks and workflows for the data-centric and distributed operation of science applications. This is in line with the recommendation for NSF to support foundational cyberinfrastructure research for data science, focusing on frameworks, workflows, and tools. (b) Programming and Debugging New AI and Analytics-Driven Workloads New AI and analytics-driven workloads present fundamentally new characteristics and require new programming models and debugging tools. For example, debugging an AI workload can lead to several considerations that range from dynamically changing data to the direct influence that a particular set of inputs has on the output or the training of a specific model. This activity probably needs some independent basic research and some research focused on the use within a broader cyberinfrastructure and can substantially impact the way the cyberinfrastructure is managed. The use within the cyberinfrastructure is undoubtedly in the purview of the OAC in conjunction with CNS and CCF. (c) Machine Learning and Cyber-Physical System Security As noted in [2], cybersecurity is critical to the integrity of the global computing system. New infrastructure elements are needed to support persistent data collections and researcher identities across multiple collaborations. It is important to have a framework and infrastructure for real-time analysis of multimedia data (voice and gesture recognition, biometrics, sentiment analysis, and social relation graph analytics) to better detect security threats to researcher identity. Data is the major component by which machine learning methods are used to develop models that aid in driving science discovery. It is critical to have a framework to maintain the integrity of data collections and researcher identity. 13 Opportunities in AI and ML The end-to-end infrastructure for science is rapidly becoming a cyber-physical system with sensors and actuators that interface to large facilities and other physical experimental apparatus with automated inference capabilities in control and analysis loops, self-driving laboratories, and scientific workflows []. New AI-based cyber-physical security methods need to be developed. Relevance to OAC: OAC should take the lead on developing secure frameworks via programs such as CICI (Cybersecurity Innovation for Cyberinfrastructure). NSFCISE has an active research program on cyber-physical security. OAC should ensure that the cyber-physical infrastructure for science outlined above is prioritized in the NSF CPS program. (d) Programming Models for Quantum Computing Where it can be a Game Changer, e.g., Quantum Mechanics Simulations Physics and chemistry – quantum mechanics simulations – are the “killer apps” for quantum computers, the applications most likely to yield a high payoff in the relatively near term. With useful quantum computers clearly on the horizon, there is a pressing need for algorithms and programming models that address these fields' needs. Relevance to OAC: Quantum architectures are being pioneered in the industry (IBM, Microsoft, Google, Amazon, and others) by other research agencies and different NSF divisions. It is recognized that NSF has made some significant investments in this area via the Quantum Leap Challenge Institutes (QLCI) program. OAC, via NSF Expeditions, is best positioned to bring together physicists, chemists, and computer scientists to pioneer improved algorithms and programming models for addressing grand challenge problems on quantum architectures. ", "metadata": {"original_filename": "item034_US_A report of the Cyberinfrastructure Research and Innovation Working Group, Advisory Committee for Cyberinfrastructure.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:51:53.643775", "updated_at": "2025-08-28T21:51:53.643775", "word_count": 10004}