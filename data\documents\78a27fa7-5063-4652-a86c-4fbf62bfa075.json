{"doc_id": "78a27fa7-5063-4652-a86c-4fbf62bfa075", "title": "item105_US_Soft law as a complement to AI regulation", "text": "Soft law as a complement to AI regulation\r\n\r\nCorporate leaders including Google CEO <PERSON><PERSON>, Microsoft President <PERSON>, Tesla and SpaceX CEO <PERSON><PERSON>, and IBM ex-CEO <PERSON><PERSON><PERSON> have called for increased regulation of artificial intelligence. So have politicians on the both sides of the aisle, as have respected scholars at academic research institutes and think tanks. At the root of the call to action is the understanding that, for all of its many benefits, AI also presents many risks. Concerns include biased algorithms, privacy violations, and the potential for injuries attributable to defective autonomous vehicle software. With the increasing adoption of AI-based solutions in areas such as criminal justice, health care, robotics, financial services, and education, there will be incentives that conflict corporate interests with societal benefits. That conflict raises the question of what systems should be put in place to mitigate potential harms.\r\n\r\nThe role of soft law\r\nWhile the dialogue on how to responsibly foster a healthy AI ecosystem should certainly include regulation, that shouldn’t be the only tool in the toolbox. There should also be room for dialogue regarding the role of “soft law.” As Arizona State University law professor <PERSON> has explained, soft law refers to frameworks that “set forth substantive expectations but are not directly enforceable by government, and include approaches such as professional guidelines, private standards, codes of conduct, and best practices.”\r\n\r\nSoft law isn’t new. The authors of a 2018 article in the Colorado Technology Law Journal point out that uses of soft law go back decades. Examples they cite include the U.S. Green Building Council’s 1993 Leadership in Energy and Environmental Design (LEED) certification standards, the Food and Drug Administration’s longstanding practice of issuing non-binding guidance, and, in recent years, explanatory blog posts and tweets from agencies such as the Federal Trade Commission and Federal Communications Commission.\r\n\r\nThere are other examples as well. Bluetooth, the standard used for short-range wireless communication between pairs of devices like laptops and wireless speakers, was developed through a collaboration among technology companies. Wi-Fi, the set of wireless local area networking (LAN) standards that allows us to connect computers and many other devices to hotspots in homes and offices, was developed by the Institute of Electrical and Electronics Engineers (IEEE). The certification through the Wi-Fi Alliance, a related soft law framework, ensures that products labeled with the Wi-Fi Alliance logo have “met industry-agreed standards for interoperability, security, and a range of application specific protocols.”\r\n\r\n“While soft law has been applied in many fields, there are multiple reasons why it is particularly well suited for AI.”\r\n\r\nWhile soft law has been applied in many fields, there are multiple reasons why it is particularly well suited for AI. First, as Marchant writes, “the pace of development of AI far exceeds the capability of any traditional regulatory system to keep up.” Congressional legislation and agency administrative rulemaking operate on time scales of years. By contrast, enormous private and public sector investments have spurred rapid AI development. According to the National Venture Capital Association, “1,356 AI-related companies in the U.S. raised $18.457 billion” in 2019. A February 2020 report from the White House Office of Science and Technology Policy (OSTP) reported that, for the government’s 2020 fiscal year, “unclassified, non-defense Federal investments in AI R&D, total[ed] $973.5 million.” Research funded by the Department of Defense includes DARPA’s $2 billion “AI Next” campaign, which was announced in late 2018. With those levels of investment, the AI technology landscape is changing by the month. This rate of development is ill suited for time scales involved in administrative law, which often takes more than a year to go from a proposed rule to a final rule.\r\nA second reason soft law is suitable for AI is the sheer complexity of the landscape. It would make no sense—financially, logistically, or in terms of staffing—to task one or more government agencies with the rulemaking and oversight extensive enough to cover all of the many applications and industries where AI will be used. For a small subset of applications, such as autonomous vehicles where the potential harms caused by failures are particularly acute, government regulation will play an important role. But most uses of AI aren’t nearly so high stakes. For those applications, soft law developed with input from companies, civil society groups, academic experts, and governments will be a key method to promote innovation consistent with ethical frameworks and principles.\r\n\r\nEven for high-stakes applications, soft law can be an important complement to “hard” law. Consider the potential safety advantages enabled by information conveyed through vehicle-to-vehicle (V2V) communication, which can allow coordination among vehicles in close proximity in order to reduce the likelihood of accidents and increase the efficiency of traffic flow. Realizing the potential of V2V will require a combination of regulation from the Department of Transportation (e.g., to address core safety issues such as accident avoidance) and soft law (e.g., in relation to the processes and protocols for wirelessly exchanging information among vehicles).\r\n\r\nAI soft law frameworks\r\nRecent years have seen a growing number of AI soft law efforts. One category of AI soft law is principles, laying out high-level goals to guide the development and deployment of AI-based solutions. For instance, the Future of Life Institute has developed the Asilomar AI Principles, which address ethical and research funding issues in the context of AI.\r\n\r\nDetailed models for AI governance constitute another form of soft law. Singapore’s Model AI Governance Framework, which was first released in early 2019 and updated in early 2020, is intended to serve as a “ready-to-use tool to enable organizations that are deploying AI solutions at scale to do so in a responsible manner.” It provides specific guidance in the four key areas of internal governance structures and measures, human involvement in AI-augmented decision-making, operations management, and stakeholder interaction and communication.\r\n\r\nThe increased attention to AI principles and governance approaches has also led to a growing number of resources to catalog and compare them. Algorithm Watch maintains an online AI Ethics Guidelines Global Inventory that, as of June 2020, listed over 150 frameworks and guidelines. An early 2020 publication from a group of researchers at the Harvard Berkman Klein Center provided a comparison of “the contents of thirty-six prominent AI principles documents.” And in a September 2019 article in Nature Machine Intelligence, Anna Jobin, Marcello Ienca, and Effy Vayena “mapped and analysed the current corpus of principles and guidelines on ethical AI.”\r\n\r\nStandards, which tend to be highly detailed and technical, are yet another form of soft law. The International Organization for Standardization, which is a “non-governmental international organization with a membership of 164 national standards bodies,” is teaming with the International Electrotechnical Commission to develop a portfolio of AI-focused standards. IEEE is developing AI standards on topics including “transparency of autonomous systems,” “certification methodology addressing transparency, accountability and algorithmic bias,” and the impact of AI on human well-being.", "metadata": {"original_filename": "item105_US_Soft law as a complement to AI regulation.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:35:12.302706", "updated_at": "2025-08-28T21:35:12.302706", "word_count": 7703}