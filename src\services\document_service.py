from typing import List, Dict, Any, Optional
import logging
import uuid
from datetime import datetime
import json
import os
from pathlib import Path

from src.models.schemas import DocumentCreate, DocumentResponse
from src.core.config import settings

logger = logging.getLogger(__name__)


class DocumentService:
    """文档服务，负责文档的存储和检索"""

    def __init__(self):
        """初始化文档服务"""
        # 创建数据目录（在实际项目中会使用数据库，这里使用文件系统模拟）
        self.data_dir = Path(settings.BASE_DIR) / "data" / "documents"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"文档服务初始化完成，数据目录: {self.data_dir}")

    async def create_document(self, document: DocumentCreate) -> DocumentResponse:
        """创建新文档

        Args:
            document: 文档创建请求

        Returns:
            DocumentResponse: 创建的文档信息
        """
        try:
            # 检查文档ID是否已存在
            if document.doc_id and await self._document_exists(document.doc_id):
                raise ValueError(f"文档 {document.doc_id} 已存在")

            # 创建文档
            now = datetime.now()
            doc_data = {
                "doc_id": document.doc_id,
                "title": document.title,
                "text": document.text,
                "metadata": document.metadata,
                "created_at": now.isoformat(),
                "updated_at": now.isoformat(),
                "word_count": len(document.text)
            }

            # 保存文档
            file_path = self.data_dir / f"{document.doc_id}.json"
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(doc_data, f, ensure_ascii=False, indent=2)

            logger.info(f"创建文档成功: {document.doc_id}")
            return DocumentResponse(**doc_data)
        except Exception as e:
            logger.error(f"创建文档失败: {str(e)}")
            raise

    async def get_document(self, doc_id: str) -> Optional[DocumentResponse]:
        """获取文档信息

        Args:
            doc_id: 文档ID

        Returns:
            DocumentResponse: 文档信息，如不存在则返回None
        """
        try:
            file_path = self.data_dir / f"{doc_id}.json"
            if not file_path.exists():
                logger.warning(f"文档不存在: {doc_id}")
                return None

            with open(file_path, "r", encoding="utf-8") as f:
                doc_data = json.load(f)

            logger.info(f"获取文档成功: {doc_id}")
            return DocumentResponse(**doc_data)
        except Exception as e:
            logger.error(f"获取文档失败: {str(e)}")
            raise

    async def list_documents(self, skip: int = 0, limit: int = 100) -> List[DocumentResponse]:
        """获取文档列表

        Args:
            skip: 跳过的文档数
            limit: 返回的最大文档数

        Returns:
            List[DocumentResponse]: 文档列表
        """
        try:
            documents = []
            file_list = list(self.data_dir.glob("*.json"))
            
            # 应用分页
            paged_files = file_list[skip:skip + limit]
            
            for file_path in paged_files:
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        doc_data = json.load(f)
                    documents.append(DocumentResponse(**doc_data))
                except Exception as e:
                    logger.error(f"读取文档 {file_path.stem} 失败: {str(e)}")
                    continue

            logger.info(f"获取文档列表成功，共 {len(documents)} 个文档")
            return documents
        except Exception as e:
            logger.error(f"获取文档列表失败: {str(e)}")
            raise

    async def update_document(self, document: DocumentCreate) -> Optional[DocumentResponse]:
        """更新文档

        Args:
            document: 文档更新请求

        Returns:
            DocumentResponse: 更新后的文档信息，如不存在则返回None
        """
        try:
            # 检查文档是否存在
            existing_doc = await self.get_document(document.doc_id)
            if not existing_doc:
                logger.warning(f"文档不存在，无法更新: {document.doc_id}")
                return None

            # 更新文档
            now = datetime.now()
            doc_data = {
                "doc_id": document.doc_id,
                "title": document.title,
                "text": document.text,
                "metadata": document.metadata,
                "created_at": existing_doc.created_at.isoformat(),
                "updated_at": now.isoformat(),
                "word_count": len(document.text)
            }

            # 保存文档
            file_path = self.data_dir / f"{document.doc_id}.json"
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(doc_data, f, ensure_ascii=False, indent=2)

            logger.info(f"更新文档成功: {document.doc_id}")
            return DocumentResponse(**doc_data)
        except Exception as e:
            logger.error(f"更新文档失败: {str(e)}")
            raise

    async def delete_document(self, doc_id: str) -> bool:
        """删除文档

        Args:
            doc_id: 文档ID

        Returns:
            bool: 是否删除成功
        """
        try:
            file_path = self.data_dir / f"{doc_id}.json"
            if not file_path.exists():
                logger.warning(f"文档不存在，无法删除: {doc_id}")
                return False

            # 删除文档
            os.remove(file_path)
            logger.info(f"删除文档成功: {doc_id}")
            return True
        except Exception as e:
            logger.error(f"删除文档失败: {str(e)}")
            raise

    async def _document_exists(self, doc_id: str) -> bool:
        """检查文档是否存在

        Args:
            doc_id: 文档ID

        Returns:
            bool: 文档是否存在
        """
        file_path = self.data_dir / f"{doc_id}.json"
        return file_path.exists()


# 创建服务实例
async def get_document_service() -> DocumentService:
    """获取文档服务实例"""
    return DocumentService()
