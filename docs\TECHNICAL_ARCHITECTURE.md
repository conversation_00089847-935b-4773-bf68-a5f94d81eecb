# 详细技术架构设计

## 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │    │   Web Frontend  │    │   Mobile Apps   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      Load Balancer        │
                    │      (Nginx/HAProxy)      │
                    └─────────────┬─────────────┘
                                  │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
┌─────────▼─────────┐ ┌─────────▼─────────┐ ┌─────────▼─────────┐
│  API Gateway      │ │  API Gateway      │ │  API Gateway      │
│  (FastAPI)        │ │  (FastAPI)        │ │  (FastAPI)        │
└─────────┬─────────┘ └─────────┬─────────┘ └─────────┬─────────┘
          │                       │                       │
          └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     Service Layer         │
                    └─────────────┬─────────────┘
                                 │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
┌─────────▼─────────┐ ┌─────────▼─────────┐ ┌─────────▼─────────┐
│  Analysis Engine │ │   Document Mgmt  │ │   User Mgmt     │
│  (Task Runner)   │ │   (Storage)      │ │   (Auth)        │
└─────────┬─────────┘ └─────────┬─────────┘ └─────────┬─────────┘
          │                       │                       │
          └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      Data Layer           │
                    │  (PostgreSQL + Redis)     │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │   External Services       │
                    │   (ZhipuAI + Storage)     │
                    └───────────────────────────┘
```

## 核心组件详细设计

### 1. API Gateway Layer

#### 技术栈
- **FastAPI**: 主要框架，支持异步处理
- **Pydantic**: 数据验证和序列化
- **Uvicorn**: ASGI服务器
- **Gunicorn**: WSGI服务器（生产环境）

#### 关键功能
```python
# 主要路由结构
/api/v1/
├── /health                    # 健康检查
├── /documents/                # 文档管理
│   ├── POST /upload          # 上传文档
│   ├── GET /{doc_id}         # 获取文档信息
│   └── DELETE /{doc_id}      # 删除文档
├── /analysis/                # 分析任务
│   ├── POST /submit          # 提交分析任务
│   ├── GET /{task_id}        # 获取任务状态
│   └── GET /{doc_id}/results # 获取分析结果
├── /batch/                   # 批量处理
│   ├── POST /upload          # 批量上传
│   └── POST /analyze         # 批量分析
└── /admin/                   # 管理接口
    ├── GET /stats           # 系统统计
    └── GET /logs            # 日志查询
```

#### 中间件设计
```python
# 中间件栈
Middleware Stack:
├── CORSMiddleware           # 跨域处理
├── SecurityMiddleware       # 安全头设置
├── AuthenticationMiddleware # 身份验证
├── RateLimitMiddleware      # 请求限流
├── LoggingMiddleware        # 日志记录
├── ErrorHandlingMiddleware  # 错误处理
└── MetricsMiddleware        # 性能监控
```

### 2. Service Layer

#### 核心服务架构
```python
# 服务接口设计
class ServiceInterface:
    """所有服务的基础接口"""
    async def initialize(self) -> None
    async def process(self, request: Request) -> Response
    async def cleanup(self) -> None
    async def health_check(self) -> HealthStatus

# 具体服务实现
class AnalysisService(ServiceInterface):
    """文档分析服务"""
    async def analyze_document(self, doc_id: str, tasks: List[str]) -> TaskResult
    
class DocumentService(ServiceInterface):
    """文档管理服务"""
    async def store_document(self, doc: Document) -> str
    async def retrieve_document(self, doc_id: str) -> Document
    
class TaskService(ServiceInterface):
    """任务管理服务"""
    async def create_task(self, task: Task) -> str
    async def get_task_status(self, task_id: str) -> TaskStatus
```

#### 任务调度系统
```python
# 任务调度器设计
class TaskScheduler:
    """异步任务调度器"""
    
    def __init__(self):
        self.task_queue = asyncio.Queue()
        self.workers = []
        self.task_registry = {}
        
    async def submit_task(self, task: Task) -> str:
        """提交任务"""
        task_id = generate_task_id()
        await self.task_queue.put(task)
        return task_id
        
    async def start_workers(self, num_workers: int):
        """启动工作线程"""
        for i in range(num_workers):
            worker = TaskWorker(f"worker-{i}", self.task_queue)
            self.workers.append(worker)
            await worker.start()
            
    async def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """获取任务结果"""
        return self.task_registry.get(task_id)
```

### 3. Analysis Engine

#### 分析引擎架构
```python
# 分析引擎核心
class AnalysisEngine:
    """文档分析引擎"""
    
    def __init__(self):
        self.task_executors = {
            "task1": ActorRelationshipExtractor(),
            "task2": PortrayalDetector(),
            "task3": IssueScopeAnalyzer(),
            "task4": CausalMechanismDetector()
        }
        self.prompt_manager = PromptManager()
        self.llm_client = ZhipuAIClient()
        
    async def execute_analysis(self, document: Document, tasks: List[str]) -> AnalysisResult:
        """执行分析任务"""
        results = {}
        
        for task_name in tasks:
            if task_name in self.task_executors:
                executor = self.task_executors[task_name]
                result = await executor.execute(document)
                results[task_name] = result
                
        return AnalysisResult(
            doc_id=document.id,
            results=results,
            metadata=self._generate_metadata()
        )
```

#### 任务执行器设计
```python
# 任务执行器基类
class TaskExecutor:
    """任务执行器基类"""
    
    def __init__(self, llm_client: LLMClient, prompt_manager: PromptManager):
        self.llm_client = llm_client
        self.prompt_manager = prompt_manager
        
    async def execute(self, document: Document) -> TaskResult:
        """执行任务"""
        prompt = await self._build_prompt(document)
        response = await self.llm_client.generate(prompt)
        result = await self._parse_response(response)
        return result
        
    async def _build_prompt(self, document: Document) -> str:
        """构建提示词"""
        raise NotImplementedError
        
    async def _parse_response(self, response: str) -> TaskResult:
        """解析响应"""
        raise NotImplementedError

# 具体任务执行器示例
class ActorRelationshipExtractor(TaskExecutor):
    """行为者关系提取器"""
    
    async def _build_prompt(self, document: Document) -> str:
        """构建Task 1提示词"""
        template = await self.prompt_manager.get_template("task1")
        return template.format(
            doc_id=document.id,
            title=document.title,
            text=document.content
        )
        
    async def _parse_response(self, response: str) -> TaskResult:
        """解析Task 1响应"""
        try:
            data = json.loads(response)
            return Task1Result(**data)
        except json.JSONDecodeError:
            # 处理解析错误
            return await self._handle_parse_error(response)
```

### 4. Data Layer

#### 数据库设计
```python
# 数据库模型
from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class Document(Base):
    """文档模型"""
    __tablename__ = "documents"
    
    id = Column(String, primary_key=True)
    title = Column(String, nullable=True)
    content = Column(Text, nullable=False)
    content_type = Column(String, default="text/plain")
    file_path = Column(String, nullable=True)
    file_size = Column(Integer)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    analysis_tasks = relationship("AnalysisTask", back_populates="document")

class AnalysisTask(Base):
    """分析任务模型"""
    __tablename__ = "analysis_tasks"
    
    id = Column(String, primary_key=True)
    document_id = Column(String, ForeignKey("documents.id"))
    task_type = Column(String, nullable=False)  # task1, task2, task3, task4
    status = Column(String, default="pending")  # pending, running, completed, failed
    input_data = Column(JSON, nullable=True)
    result_data = Column(JSON, nullable=True)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # 关系
    document = relationship("Document", back_populates="analysis_tasks")

class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    id = Column(String, primary_key=True)
    username = Column(String, unique=True, nullable=False)
    email = Column(String, unique=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)
```

#### 缓存设计
```python
# 缓存层设计
class CacheManager:
    """缓存管理器"""
    
    def __init__(self, redis_client: Redis):
        self.redis = redis_client
        self.default_ttl = 3600  # 1小时
        
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        try:
            data = await self.redis.get(key)
            if data:
                return json.loads(data)
            return None
        except Exception as e:
            logger.error(f"Cache get error: {e}")
            return None
            
    async def set(self, key: str, value: Any, ttl: int = None) -> bool:
        """设置缓存"""
        try:
            ttl = ttl or self.default_ttl
            await self.redis.setex(key, ttl, json.dumps(value))
            return True
        except Exception as e:
            logger.error(f"Cache set error: {e}")
            return False
            
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            await self.redis.delete(key)
            return True
        except Exception as e:
            logger.error(f"Cache delete error: {e}")
            return False
```

### 5. External Services Integration

#### 智谱AI集成
```python
# 智谱AI客户端
class ZhipuAIClient:
    """智谱AI API客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://open.bigmodel.cn/api/paas/v4"):
        self.api_key = api_key
        self.base_url = base_url
        self.client = httpx.AsyncClient(
            base_url=base_url,
            headers={"Authorization": f"Bearer {api_key}"},
            timeout=30.0
        )
        
    async def generate(self, prompt: str, model: str = "glm-4.5") -> str:
        """生成文本"""
        try:
            response = await self.client.post(
                "/chat/completions",
                json={
                    "model": model,
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": 0.1,
                    "max_tokens": 4000
                }
            )
            response.raise_for_status()
            data = response.json()
            return data["choices"][0]["message"]["content"]
        except httpx.HTTPError as e:
            logger.error(f"ZhipuAI API error: {e}")
            raise ZhipuAIError(f"API request failed: {e}")
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            raise ZhipuAIError(f"Unexpected error: {e}")
            
    async def batch_generate(self, prompts: List[str], model: str = "glm-4.5") -> List[str]:
        """批量生成文本"""
        tasks = [self.generate(prompt, model) for prompt in prompts]
        return await asyncio.gather(*tasks, return_exceptions=True)
```

### 6. Error Handling Strategy

#### 错误分类和处理
```python
# 错误类型定义
class DocumentAnalysisError(Exception):
    """基础错误类"""
    pass

class ValidationError(DocumentAnalysisError):
    """验证错误"""
    pass

class AuthenticationError(DocumentAnalysisError):
    """认证错误"""
    pass

class AuthorizationError(DocumentAnalysisError):
    """授权错误"""
    pass

class RateLimitError(DocumentAnalysisError):
    """限流错误"""
    pass

class ZhipuAIError(DocumentAnalysisError):
    """智谱AI API错误"""
    pass

class DatabaseError(DocumentAnalysisError):
    """数据库错误"""
    pass

# 错误处理中间件
class ErrorHandlingMiddleware:
    """错误处理中间件"""
    
    def __init__(self, app):
        self.app = app
        
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
            
        try:
            await self.app(scope, receive, send)
        except Exception as e:
            await self._handle_error(scope, receive, send, e)
            
    async def _handle_error(self, scope, receive, send, error: Exception):
        """处理错误"""
        request = Request(scope, receive)
        
        # 记录错误
        logger.error(f"Error processing request: {error}", exc_info=True)
        
        # 根据错误类型返回相应状态码
        if isinstance(error, ValidationError):
            status_code = 422
            detail = str(error)
        elif isinstance(error, AuthenticationError):
            status_code = 401
            detail = "Authentication failed"
        elif isinstance(error, AuthorizationError):
            status_code = 403
            detail = "Authorization failed"
        elif isinstance(error, RateLimitError):
            status_code = 429
            detail = "Rate limit exceeded"
        elif isinstance(error, ZhipuAIError):
            status_code = 502
            detail = "External AI service error"
        elif isinstance(error, DatabaseError):
            status_code = 503
            detail = "Database service unavailable"
        else:
            status_code = 500
            detail = "Internal server error"
            
        # 返回错误响应
        response = JSONResponse(
            status_code=status_code,
            content={"detail": detail, "error_type": type(error).__name__}
        )
        await response(scope, receive, send)
```

### 7. Security Design

#### 认证和授权
```python
# JWT认证
class JWTAuthHandler:
    """JWT认证处理器"""
    
    def __init__(self, secret_key: str, algorithm: str = "HS256"):
        self.secret_key = secret_key
        self.algorithm = algorithm
        
    def create_access_token(self, data: dict, expires_delta: timedelta = None) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=30)
            
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
        
    def verify_token(self, token: str) -> dict:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token expired")
        except jwt.InvalidTokenError:
            raise AuthenticationError("Invalid token")

# 权限装饰器
def require_permission(permission: str):
    """权限装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 获取当前用户
            current_user = get_current_user()
            
            # 检查权限
            if not current_user.has_permission(permission):
                raise AuthorizationError(f"Permission denied: {permission}")
                
            return await func(*args, **kwargs)
        return wrapper
    return decorator
```

### 8. Performance Optimization

#### 性能优化策略
```python
# 连接池管理
class ConnectionPoolManager:
    """连接池管理器"""
    
    def __init__(self):
        self.db_pool = None
        self.redis_pool = None
        
    async def initialize(self):
        """初始化连接池"""
        # 数据库连接池
        self.db_pool = create_async_engine(
            DATABASE_URL,
            pool_size=20,
            max_overflow=30,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        
        # Redis连接池
        self.redis_pool = redis.ConnectionPool(
            host=REDIS_HOST,
            port=REDIS_PORT,
            max_connections=50,
            retry_on_timeout=True
        )
        
    async def close(self):
        """关闭连接池"""
        if self.db_pool:
            await self.db_pool.dispose()
        if self.redis_pool:
            await self.redis_pool.disconnect()

# 异步任务处理
class AsyncTaskProcessor:
    """异步任务处理器"""
    
    def __init__(self, max_concurrent_tasks: int = 10):
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self.task_queue = asyncio.Queue()
        
    async def process_task(self, task: Callable) -> Any:
        """处理单个任务"""
        async with self.semaphore:
            try:
                return await task()
            except Exception as e:
                logger.error(f"Task processing error: {e}")
                raise
                
    async def process_batch(self, tasks: List[Callable]) -> List[Any]:
        """批量处理任务"""
        async_tasks = [self.process_task(task) for task in tasks]
        return await asyncio.gather(*async_tasks, return_exceptions=True)
```

### 9. Monitoring and Logging

#### 监控系统设计
```python
# 性能监控
class MetricsCollector:
    """性能指标收集器"""
    
    def __init__(self):
        self.request_count = Counter("requests_total", "Total requests", ["method", "endpoint"])
        self.request_duration = Histogram("request_duration_seconds", "Request duration")
        self.error_count = Counter("errors_total", "Total errors", ["error_type"])
        self.active_tasks = Gauge("active_tasks", "Active analysis tasks")
        
    def record_request(self, method: str, endpoint: str, duration: float):
        """记录请求指标"""
        self.request_count.labels(method=method, endpoint=endpoint).inc()
        self.request_duration.observe(duration)
        
    def record_error(self, error_type: str):
        """记录错误指标"""
        self.error_count.labels(error_type=error_type).inc()
        
    def update_active_tasks(self, count: int):
        """更新活跃任务数"""
        self.active_tasks.set(count)

# 日志系统
class LogManager:
    """日志管理器"""
    
    def __init__(self):
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        logger.remove()  # 移除默认处理器
        
        # 控制台日志
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level="INFO",
            colorize=True
        )
        
        # 文件日志
        logger.add(
            "logs/app.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level="DEBUG",
            rotation="10 MB",
            retention="30 days",
            compression="zip"
        )
        
        # 错误日志
        logger.add(
            "logs/error.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level="ERROR",
            rotation="10 MB",
            retention="30 days",
            compression="zip"
        )
```

这个详细的技术架构设计提供了：

1. **完整的系统架构图** - 展示了各层组件的关系
2. **详细的组件设计** - 包括API网关、服务层、分析引擎等
3. **数据库模型** - 完整的SQLAlchemy模型定义
4. **错误处理策略** - 全面的错误分类和处理机制
5. **安全设计** - JWT认证和权限控制
6. **性能优化** - 连接池、异步处理等
7. **监控系统** - 指标收集和日志管理

每个组件都有详细的代码实现和接口定义，为开发提供了清晰的技术指导。