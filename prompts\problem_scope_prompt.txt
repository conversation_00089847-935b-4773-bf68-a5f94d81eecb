请分析以下政策文档中的问题范围策略。

文档内容：
{document}

请执行以下分析任务：
1. 识别文档中的问题扩大化策略（如何将问题描述得比实际更大）
2. 识别文档中的问题缩小化策略（如何将问题描述得比实际更小）
3. 分析问题框架模式
4. 总结关键发现

以JSON格式返回分析结果，包含以下字段：
{
    "expansion_strategies": [
        {
            "type": "扩大化策略类型",
            "description": "策略描述",
            "examples": ["例子1", "例子2"...]
        }
        // 更多扩大化策略...
    ],
    "reduction_strategies": [
        {
            "type": "缩小化策略类型",
            "description": "策略描述",
            "examples": ["例子1", "例子2"...]
        }
        // 更多缩小化策略...
    ],
    "framing_patterns": [
        {
            "type": "框架模式类型",
            "description": "模式描述",
            "examples": ["例子1", "例子2"...]
        }
        // 更多框架模式...
    ],
    "key_findings": [
        "关键发现1",
        "关键发现2"
        // 更多关键发现...
    ]
}

只返回JSON格式的分析结果，不要包含任何额外的解释。
