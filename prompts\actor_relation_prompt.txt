请分析以下政策文档中的行为者与关系。

文档内容：
{document}

请执行以下分析任务：
1. 识别文档中的主要行为者（人物、组织、机构等）
2. 分析行为者之间的关系和互动
3. 提取行为者的主要行动和立场
4. 总结关键发现

以JSON格式返回分析结果，包含以下字段：
{
    "actors": [
        {
            "name": "行为者名称",
            "type": "行为者类型（如：政府、企业、个人等）",
            "description": "行为者描述",
            "actions": ["行为者的主要行动1", "行为者的主要行动2"...],
            "stance": "行为者的立场或态度"
        }
        // 更多行为者...
    ],
    "relations": [
        {
            "source": "行为者1",
            "target": "行为者2",
            "type": "关系类型（如：合作、对抗、支持等）",
            "description": "关系描述"
        }
        // 更多关系...
    ],
    "key_findings": [
        "关键发现1",
        "关键发现2"
        // 更多关键发现...
    ]
}

只返回JSON格式的分析结果，不要包含任何额外的解释。
