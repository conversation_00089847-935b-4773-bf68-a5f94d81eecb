#!/usr/bin/env python3
"""
Test script to verify FastAPI app and route registration
"""
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.main import app
    print("FastAPI app imported successfully")
    
    # Get all routes
    routes = []
    for route in app.routes:
        routes.append({
            "path": route.path,
            "name": route.name,
            "methods": list(route.methods) if hasattr(route, 'methods') else []
        })
    
    print(f"Total routes registered: {len(routes)}")
    print("\nRegistered Routes:")
    for route in routes:
        print(f"  {route['path']} [{', '.join(route['methods'])}] - {route['name']}")
    
    # Check if visualization routes are registered
    viz_routes = [r for r in routes if 'visualization' in r['path']]
    print(f"\nVisualization routes found: {len(viz_routes)}")
    for route in viz_routes:
        print(f"  {route['path']} [{', '.join(route['methods'])}]")
        
except Exception as e:
    print(f"Error importing FastAPI app: {e}")
    import traceback
    traceback.print_exc()