{"doc_id": "2bc0f070-b184-47df-b6bd-5ca963af4302", "title": "item255_US_<PERSON><PERSON>, <PERSON><PERSON><PERSON>., Vice President and Chief AI Scientist, Meta Platforms, Inc; Jeffrey", "text": "   STATEMENT OF <PERSON><PERSON><PERSON>CUN, PhD, VICE PRESIDENT AND <PERSON><PERSON>F AI \r\n  SCIENTIST, META PLATFORMS, AND SILVER PROFESSOR OF COMPUTER \r\n         SCIENCE AND DATA SCIENCE, NEW YORK UNIVERSITY\r\n\r\n    Dr. <PERSON>. Chairman <PERSON>, Vice Chairman <PERSON><PERSON><PERSON>, and \r\ndistinguished Members of the Committee. Thank you for the \r\nopportunity to appear before you today to discuss important \r\nissues regarding AI.\r\n    My name is <PERSON><PERSON>. I'm currently the Silver Professor \r\nof Computer Science and Data Science at New York University. \r\nI'm also Meta's Chief AI Scientist and co-founder of Meta's \r\nFundamental AI Research Lab. At Meta, I focus on AI research, \r\ndevelopment strategy, and scientific leadership.\r\n    AI has progressed leaps and bounds since I began my \r\nresearch career in the 1980s. Today, we are witnessing the \r\ndevelopment of generative AI, and in particular, large language \r\nmodels. These systems are trained through self-supervised \r\nlearning. Or more simply, they are trained to fill in the \r\nblanks. In the process of doing so, those AI models learn to \r\nrepresent text or images--including the meaning, style, and \r\nsyntax--in multiple languages. The internal representation can \r\nthen be applied to downstream tasks such as translation, topic \r\nclassification, et cetera. It can also be used to predict the \r\nnext words in a text, which allow LL<PERSON> to answer questions or \r\nwrite essays, and write code as well. It is important not to \r\nundervalue the far-reaching potential opportunities they \r\npresent. The development of AI is as foundational as the \r\ncreation of the microprocessor, the personal computer, the \r\nInternet, and the mobile device. Like all foundational \r\ntechnologies, there will be a multitude of uses of AI. And like \r\nevery technology, AI will be used by people for good and bad \r\nends.\r\n    As AI systems continue to develop, I'd like to highlight \r\ntwo defining issues. The first one is safety, and the second \r\none is access. One way to start to address both of these issues \r\nis through the open sharing of current technology and \r\nscientific information. The free exchange of scientific papers, \r\ncode, and trained models in the case of AI has enabled American \r\nleadership in science and technology. This concept is not new. \r\nIt started a long time ago. Open sourcing technology has \r\nspurred rapid progress in systems we now consider basic \r\ninfrastructure, such as the Internet and mobile communication \r\nnetworks.\r\n    This doesn't mean that every model can or should be open. \r\nThere is a role for both proprietary and open-source AI models. \r\nBut an open-source basic model should be the foundation on \r\nwhich industry can build a vibrant ecosystem. An open-source \r\nmodel creates an industry standard, much like the model of the \r\nInternet in the mid '90s. Through this collaborative effort, AI \r\ntechnology will progress faster, more reliably, and more \r\nsecurely.\r\n    Open sourcing also gives businesses and researchers access \r\nto tools that they could not otherwise build by themselves, \r\nwhich helps create a vast social and economic set of \r\nopportunities. In other words, open sourcing democratizes \r\naccess. It gives more people and businesses the power to build \r\nupon state-of-the-art technology and to remedy potential \r\nweaknesses. This also helps promote democratic values and \r\ninstitutions, minimize social disparities, and improve \r\ncompetition. We want to ensure that the United States and \r\nAmerican companies, together with other democracies, lead in AI \r\ndevelopment ahead of our adversaries, so that the foundational \r\nmodels are developed here and represent and share our values. \r\nBy open sourcing current AI tools, we can develop our research \r\nand development ecosystem faster than our adversary.\r\n    As AI technology progresses, there is an urgent need for \r\ngovernments to work together, especially democracies, to set \r\ncommon AI standards and governance models. This is another \r\nvaluable area where we welcome working with regulators to set \r\nappropriate transparency requirements, red teaming standards, \r\nand safety mitigations to help ensure those codes of practice, \r\nstandards, and guardrails are consistent across the world. The \r\nWhite House's voluntary commitment is a critical step in \r\nensuring responsible guardrails, and they create a model for \r\nother governments to follow. Continued U.S. leadership by \r\nCongress and the White House is important in ensuring that \r\nsociety can benefit from innovation in AI while striking the \r\nright balance with protecting rights and freedom, preserving \r\nnational security interests, and mitigating risks where those \r\narise.\r\n    I'd like to close by thanking Chairman Warner, Vice \r\nChairman Rubio, and the other Members of the Committee for your \r\nleadership. At the end of the day, our job is to work \r\ncollaboratively with you, with Congress, with other nations, \r\nand with other companies in order to drive innovation and \r\nprogress in a manner that is safe and secure and consistent \r\nwith our national security interests.\r\n    Thank you. I look forward to your questions.\r\n    [The prepared statement of the witness follows:]\r\n    [GRAPHICS NOT AVAILABLE IN TIFF FORMAT]", "metadata": {"original_filename": "item255_US_<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Vice President and Chief AI Scientist, Meta Platforms, Inc; Jeffrey.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:35:14.078677", "updated_at": "2025-08-28T21:35:14.078677", "word_count": 5200}