{"doc_id": "7037eccd-f00b-410d-96fc-a5bd770a2386", "title": "item258_US_<PERSON><PERSON><PERSON>, <PERSON>, general counsel and executive vice president, business and legal affairs, Universal Music Group", "text": "STATEMENT OF <PERSON><PERSON><PERSON>EW SAG, PROFESSOR OF LAW, ARTIFICIAL \r\n    INTELLIGENCE, MACHINE LEARNING, AND DATA SCIENCE, EMORY \r\n           UNIVERSITY SCHOOL OF LAW, ATLANTA, GEORGIA\r\n\r\n    Professor <PERSON><PERSON>. Chair <PERSON><PERSON>, Ranking Member <PERSON>, Members \r\nof the Subcommittee, thank you for the opportunity to testify \r\nhere today. I am a professor of law in AI, machine learning, \r\nand data science at Emory University, where I was hired as part \r\nof Emory's AI Humanity Initiative.\r\n    Although we are still a long way from the science fiction \r\nversion of artificial general intelligence that thinks, feels, \r\nand refuses to open the pod bay doors, recent advances in \r\nmachine learning and artificial intelligence have captured the \r\npublic's attention and apparently lawmakers' interest.\r\n    We now have large language models, or LLMs, that can pass \r\nthe bar exam, carry on a conversation, create new music and new \r\nvisual art. Nonetheless, copyright law does not and should not \r\nrecognize computer systems as authors. Even where an AI \r\nproduces images, text, or music that is indistinguishable from \r\nhuman authored works, it makes no sense to think of a machine \r\nlearning program as the author.\r\n    The Copyright Act rightly reserves copyrights for original \r\nworks of authorship. As the Supreme Court explained long ago in \r\nthe 1884 case of <PERSON>ow-<PERSON>, authorship entails \r\noriginal, intellectual conception. An AI can't produce a work \r\nthat reflects its own original intellectual conception because \r\nit has none.\r\n    Thus, when AI models produce content with little or no \r\nhuman oversight, there is no copyright in those outputs. \r\nHowever, humans using AI as tools of expression may claim \r\nauthorship if the final form of the work reflects their \r\noriginal intellectual conception in sufficient detail. And I \r\nhave elaborated in my written submissions how this will depend \r\non the circumstances.\r\n    Training generative AI on copyrighted works is usually fair \r\nuse because it falls into the category of non-expressive use. \r\nCourts addressing technologies such as reverse engineering, \r\nsearch engines, and plagiarism detection software have held \r\nthat these non-expressive uses are fair use. These cases \r\nreflect copyright's fundamental distinction between protectable \r\noriginal expression and unprotect-able facts, ideas, and \r\nabstractions.\r\n    Whether training an LLM is in non-expressive use depends \r\nultimately on the outputs of the model. If an LLM is trained \r\nproperly and operated with appropriate safeguards, its outputs \r\nwill not resemble its inputs in a way that would trigger a \r\ncopyright liability. Training such an LLM on copyrighted works \r\nwould thus be justified under current fair use principles.\r\n    It is important to understand that generative AI are not \r\ndesigned to copy original expression. One of the most common \r\nmisconceptions about generative AI is the notion that the \r\ntraining data is somehow copied into the model. Machine \r\nlearning models are influenced by the data. They would be \r\npretty useless without it. But they typically don't copy the \r\ndata in any literal sense.\r\n    So rather than thinking of an LLM as copying the training \r\ndata like a scribe in a monastery, it makes more sense to think \r\nof it as learning from the training data like a student. If an \r\nLLM like GPT3 is working as intended, it doesn't copy the \r\ntraining data at all. The only copying that takes place is when \r\nthe training corpus is assembled and pre-processed, and that is \r\nwhat you need a fair use justification for. Whether a \r\ngenerative AI produces truly new content or simply conjures up \r\nan infringing cut and paste of works in the training data \r\ndepends on how it is trained.\r\n    Accordingly, companies should adopt best practices to \r\nreduce the risk of copyright infringement and other related \r\nharms, and I have elaborated on some of these best practices in \r\nmy written submission. Failure to adopt best practices may \r\npotentially undermine claims of fair use.\r\n    Generative AI does not, in my opinion, require a major \r\noverhaul of the U.S. copyright system at this time.\r\n    If Congress is considering new legislation in relation to \r\nAI and copyright, that legislation should be targeted at \r\nclarifying the application of existing fair use jurisprudence, \r\nnot overhauling it.\r\n    Israel, Singapore, and South Korea have recently \r\nincorporated fair use into their copyright statutes because \r\nthese countries recognize that the flexibility of the fair use \r\ndoctrine gives U.S. companies and U.S. researchers a \r\nsignificant competitive advantage.\r\n    Several other jurisdictions, most notably Japan, the United \r\nKingdom, and the European Union, have specifically adopted \r\nexemptions for text data mining that allow use of copyrighted \r\nworks as training for machine learning and other purposes.\r\n    Copyright law should encourage the developers of generative \r\nAI to act responsibly. However, if our laws become overly \r\nrestrictive, then corporations and researchers will simply move \r\nkey aspects of technology development overseas to our \r\ncompetitors.\r\n    Thank you very much.", "metadata": {"original_filename": "item258_US_<PERSON><PERSON><PERSON>, <PERSON>, general counsel and executive vice president, business and legal affairs, Universal Music Group.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:51:56.268364", "updated_at": "2025-08-28T21:51:56.268364", "word_count": 5172}