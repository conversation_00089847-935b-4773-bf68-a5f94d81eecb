{"doc_id": "ff2b5b6f-5597-4e28-88ce-f041e545e06a", "title": "item014_US_Dual-Use Foundation Models with Widely Available Model Weights Report", "text": "Dual-Use Foundation Models with Widely Available Model Weights NTIA Report JULY 2024 i Dual-Use Foundation Models with Widely Available Model Weights Dual-Use Foundation Models with Widely Available Model Weights  Contents  Executive Summary.........................................................................................................................................1  Overview .......................................................................................................................................................2  Glossary ...............................................................................................................................................................5  Background ........................................................................................................................................................7  AI Model Weights.........................................................................................................................................8  Model Openness as a Spectrum .............................................................................................................9  An Approach to Analysis of Marginal Risks and Benefits.............................................................. 10  Risks and Benefits of Dual-Use Foundation Models with Widely Available Model Weights.. 12  Public Safety ............................................................................................................................................. 13  Risks of Widely Available Model Weights for Public Safety........................................................ 14  Benefits of Widely Available Model Weights for Public Safety.................................................. 17  Geopolitical Considerations................................................................................................................. 19  Geopolitical Risks of Widely Available Model Weights ............................................................... 19  Geopolitical Benefits of Widely Available Model Weights ......................................................... 22  Societal Risks and Well-Being .............................................................................................................. 23  Societal Risks of Widely Available Model Weights....................................................................... 24  Societal Benefits of Widely Available Model Weights ................................................................. 28  Competition, Innovation, and Research........................................................................................... 29  Risks of Widely Available Model Weights for Competition, Innovation, and Research........ 30  Benefits of Widely Available Model Weights for Competition, Innovation, and Research .. 31  Uncertainty in Future Risks and Benefits ......................................................................................... 33  Summary of Risks and Benefits ........................................................................................................... 34  Policy Approaches and Recommendations .......................................................................................... 35  Policy Approaches ................................................................................................................................... 36  Restrict the Availability of Model Weights for Dual-Use Foundation Models......................... 36  Continuously Evaluate the Dual-Use Foundation Model Ecosystem and Build    & Maintain the Capacity to Effectively Respond ......................................................................... 37  Accept or Promote Openness ......................................................................................................... 39  Recommendations .................................................................................................................................. 40  Step 1: Collect Evidence................................................................................................................... 41  Step 2: Evaluate Evidence ................................................................................................................. 43  Step 3: Act on Evaluations................................................................................................................ 45  Additional Government Action ....................................................................................................... 47  Conclusion ................................................................................................................................................. 47  Appendix: Monitoring Template............................................................................................................... 48 Executive Summary 1 Dual-Use Foundation Models with Widely Available Model Weights  Dual-Use Foundation Models with Widely Available Model Weights 2  tion models with widely available model weights while  managing and mitigating the attendant risks, Section  4.6 of the EO tasked the Secretary of Commerce, acting  through the Assistant Secretary of Commerce for Com munications and Information and in consultation with  the Secretary of State, with soliciting feedback “from  the private sector, academia, civil society, and other  stakeholders through a public consultation process on  the potential risks, benefits, other implications, and  appropriate policy and regulatory approaches related  to dual-use foundation models for which the model  weights are widely available.”  Overview  As stated by President Biden, “Artificial Intelligence (AI)  holds extraordinary potential for both promise and per il.”1 The development of increasingly advanced AI mod els, such as dual-use foundation models, has signifi cantly heightened the potential risks and benefits of AI  systems. Many developers provide limited or no public  access to the inner workings of their advanced models,  including their weights.2 In contrast, some developers,  such as Meta, Google, Microsoft, Stability AI, Mistral,  the Allen Institute for AI, and EleutherAI,3 have released  models – though not always their most advanced mod els – with weights that are widely available (i.e., open to  the public by allowing users to download these weights  from the Internet or through other mechanisms).  Dual-use foundation models with widely available  model weights (referred to in this Report as open foun dation models) introduce a wide spectrum of benefits.  They diversify and expand the array of actors, including  less resourced actors, that participate in AI research  and development. They decentralize AI market control  from a few large AI developers. And they enable users to  leverage models without sharing data with third parties,  increasing confidentiality and data protection.  However, making the weights of certain foundation  models widely available could also engender harms and  risks to national security, equity, safety, privacy, or civil  rights through affirmative misuse, failures of effective  oversight, or lack of clear accountability mechanisms.  In October 2023 President Biden signed the Executive  Order (EO) on “Safe, Secure, and Trustworthy Develop ment and Use of Artificial Intelligence.” Noting the im portance of maximizing the benefits of dual-use founda“Dual-use foundation models with widely available model weights...introduce a wide spectrum of benefits. They diversify and expand the array of actors, including less resourced actors, that participate in AI R&D. They decentralize AI market control from a few large AI developers. And they enable users to leverage models without sharing data with third parties, increasing confidentiality and data protection.”  Dual-Use Foundation Models with Widely Available Model Weights 3  weight models and models that are not considered du al-use foundation models under the EO definition (such  as foundation models with fewer than 10 billion param eters).  Finally, the Report considers under what circumstances  the U.S. government should restrict the wide availabil ity of model weights for dual-use foundation models. It  evaluates a range of policy approaches, assessing their  risks and benefits. And it concludes that, at the time of  this Report, current evidence is not sufficient to defini tively determine either that restrictions on such open weight models are warranted, or that restrictions will  never be appropriate in the future.  Instead, this Report suggests that the government  should actively monitor a portfolio of risks that could  arise from dual-use foundation models with widely  available model weights and take steps to ensure that  the government is prepared to act if heightened risks  emerge. Specifically, we recommend that the govern ment:  1. Collect evidence through: a.Encouraging standards and – if appropriate –compelling audits, disclosures, and transpar ency for dual-use foundation models (including those without widely available model weights); b. Supporting and conducting research into the safety, security, and trustworthiness of foun dation models and high-risk models, as well as their downstream uses; c.Supporting external research into the present and future capabilities and limitations of specific dual-use foundation models and risk mitiga tions; and The EO further denoted that the Secretary of Commerce,  through the Assistant Secretary of Commerce for Com munications and Information and in consultation with  the Secretary of State and heads of relevant agencies,  would author a report to the President on the “poten tial benefits, risks, and implications of dual-use foun dation models for which the model weights are widely  available, as well as policy and regulatory recommen dations pertaining to those models.” In fulfilment of  this tasking, the National Telecommunications and In formation Administration (NTIA) published a public Re quest for Comment in February 2024 and received 332  comments in response.4 NTIA further conducted exten sive stakeholder outreach, including two public events  gathering input from a range of policy and technology  experts. This Report and its findings are based in large  part on this feedback.  This Report provides a non-exhaustive review of the  risks and benefits of open foundation models, broken  down into the broad categories of Public Safety; Soci etal Risks and Wellbeing; Competition, Innovation, and  Research; Geopolitical Considerations; and Uncertainty  in Future Risks and Benefits. It is important to under stand these risks as marginal risks—that is, risks that  are unique to the deployment of dual-use foundation  models with widely available model weights relative to  risks from other existing technologies, including closed d. Developing and maintaining a set of risk portfolios, indicators, and thresholds. 2. Evaluate evidence through: a. Assessing the lag time between developers introducing capabilities in leading proprietary models, and those same capabilities being made available in open models; b. Developing benchmarks and definitions for monitoring and potential action if deemed appropriate; and c. Maintaining and bolstering federal government expert capabilities in technical, legal, social science, and policy domains to support the evaluation of evidence. 3. Act on evaluations through actions such as: a. Restrictions on access to models; or b. Other risk mitigation measures. 4. Keep open the possibility of additional government action. These recommendations support the ability of developers electing to make model weights widely available at this time, while bolstering the government’s ability to monitor whether future models pose risks that indicate that it may be appropriate to limit model weight availability or apply other appropriate risk mitigation measures. The Report provides high-level guidance and considerations for this recommendation. This Report provides relatively little insight to future releases of dual-use foundation models with widely available model weights; however, the recommended action to monitor risks would help the government determine the capabilities of future dual-use foundation models with widely available model weights. Without changes in research and monitoring capabilities, this dynamic may persist: any evidence of risks that would justify possible policy interventions to restrict the availability of model weights might arise only after AI models with those capabilities, closed or open, have been developed or released. In summary, this Report outlines a cautious yet optimistic path that follows longstanding U.S. government policies supporting widespread access to digital technologies and their benefits, while nonetheless preparing for the potential future development of models for which an alternate approach may be justified. 4 Dual-Use Foundation Models with Widely Available Model Weights Glossar y 5 Dual-Use Foundation Models with Widely Available Model Weights  Dual-Use Foundation Models with Widely Available Model Weights 6  B. enabling powerful offensive cyber operations through automated vulnerability discovery and ex ploitation against a wide range of potential targets of cyber attacks; or C. permitting the evasion of human control or over sight through means of deception or obfuscation7 Models meet this definition even if they are provided  to end users with technical safeguards that attempt to  prevent users from taking advantage of the relevant un safe capabilities.8 A dual-use foundation model with widely available  model weights (or, in this Report, an open foundation  model) is a dual-use foundation model whose model  weights have been released openly to the public, either  by allowing users to download them from the Internet  or through other mechanisms.  The term foundation model is used synonymously  with the term dual-use foundation model in this Report.  However, the term has been used more broadly in the AI  community, notably without the “tens of billions of pa rameters” requirement.9 Further, all foundation models  do not necessarily display “dual-use” capabilities.  Model weights are “numerical parameters within an AI  model that helps determine the model’s output in re sponse to inputs.”10 There are multiple types of weights,  including the pre-trained model weights, weights from  intermediate checkpoints, and weights of fine-tuned  models.  This Report uses the following definitions, many of  which arise from the definitions in Executive Order  14110:  Artificial intelligence (AI) means a machine-based  system that can, for a given set of human-defined ob jectives, make predictions, recommendations or deci sions influencing real or virtual environments. Artificial  intelligence systems use machine and human-based  inputs to:  • perceive real and virtual environments, • abstract such perceptions into models through analysis in an automated manner, and • use model inference to formulate options for infor mation or action. 15 U.S.C 9401(3).5 An AI model is a component of an information system  that implements AI technology and uses computation al, statistical, or machine-learning techniques to pro duce outputs from a given set of inputs.6 A dual-use foundation model means an AI model that  is trained on broad data; generally uses self-supervi sion; contains at least tens of billions of parameters; is  applicable across a wide range of contexts; and that ex hibits, or could be easily modified to exhibit, high levels  of performance at tasks that pose a serious risk to secu rity, national economic security, national public health  or safety, or any combination of those matters, such as  by:  A. substantially lowering the barrier of entry for non-experts to design, synthesize, acquire, or use chemical biological, radiological, or nuclear (CBRN) weapons;Background 7 Dual-Use Foundation Models with Widely Available Model Weights  Dual-Use Foundation Models with Widely Available Model Weights 8  context not included in the original training corpus.13 Other techniques, such as quantization,14 pruning,15 and merging multiple models together, do not require  new data. Customization techniques typically require  significantly less technical knowledge, resources, and  computing power than training a new model from  scratch. The gap between the resources required to  customize pre-trained models compared to training a  full model will likely continue to widen.16, 17 This accessi bility afforded by open weights significantly lowers the  barrier of entry to fine-tune models for both beneficial  and harmful purposes. Adversarial actors can remove  safeguards from open models via fine-tuning, then free ly distribute the model, ultimately limiting the value of  mitigation techniques.18 Users can also circumvent some of these safeguards in  closed AI models, such as by consulting online informa tion about how to ‘jailbreak’ a model to generate un intended answers (i.e., creative prompt engineering) or  by fine-tuning AI models via APIs.19 However, there are  significantly fewer model-based safeguards for open weight models overall.  Second, developers who publicly release model weights  give up control over and visibility into its end users’ ac tions. They cannot rescind access to the weights or per form moderation on model usage.20 While the weights  could be removed from distribution platforms, such  as Hugging Face, once users have downloaded the  weights they can share them through other means.21 For example, the company Mistral AI publicly released  Mixtral 8x7b, a dual-use foundation model with widely  available model weights via BitTorrent, a decentralized  peer-to-peer file sharing protocol which is designed  specifically to evade control by one party.22 AI Model Weights  An AI model processes an input—such as a user prompt—  into a corresponding output, and the contents of that  output are determined by a series of numerical param eters that make up the model, known as the model’s  weights. The values of these weights, and therefore  the behavior of the model, are determined by training  the model with numerous examples.11 The weights  represent numerical values that the model has learned  during training to achieve an objective specified by  the developers. Parameters encode what a model has  learned during the training phase, but they are not the  only important component of an AI model. For exam ple, foundation models are trained on great quantities  of data; for large language models (LLMs) in particular,  training data can be further decomposed into trillions  of sub-units, called tokens. Other factors also play a sig nificant role in model performance, such as the model’s  architecture, training procedures, the types of data (or  modalities) processed by the model, and the complexi ty of the tasks the model is trained to perform.12 Some model developers have chosen to keep these  weights guarded from the public, opting to control ac cess through user-focused web interfaces or through  APIs (application programming interfaces). Users or  software systems can interact with these models by  submitting inputs and receiving outputs, but cannot  directly access the weights themselves. If a developer  does decide to make a model’s weights widely avail able, three important consequences arise.  First, once weights have been released, individuals  and firms can customize them outside the developer’s  initial scope. For instance, users can fine-tune models  on new data, such as text from a language or cultural  Dual-Use Foundation Models with Widely Available Model Weights 9  over time. This is done to allow time for safety research  and for risks at one stage to become apparent before  increasing access. The time scale for staged releases  can vary, since “generally substantial sociotechnical  research requires multiple weeks, months, and some times years.”25 There are currently a wide range of AI  licenses in use, which can be used by themselves or in  conjunction with forms of structured access. Some li censes require the user or downloader to agree to use  and redistribution restrictions, sometimes including  behavioral or ethical guidelines, though they can be  hard to enforce.26 Even developers of models that are not “open” can  increase transparency and visibility through compre hensive documentation. Model cards are one method  for describing a model’s technical details, intended  uses, and performance on evaluation and red-teaming  efforts.27 Independent of whether the training data it self is widely available, information about the training  dataset(s) can be distributed using data sheets, where  developers can share the processes they used to train  the model and any artifacts or procedures involved in  human-in-the-loop training such as data annotation or  reinforcement learning with human feedback instruc tions.28 These openness factors can and should be considered  at all stages of the AI lifecycle, including post-deploy ment. For instance, a dual-use foundation model can be  open at one stage of development and closed at anoth er, such as a base model that is open but that is custom ized to create a downstream, closed consumer-facing  system.  Finally, open model weights allow users to perform  computational inference using their own computa tional resources, which may be on a local machine or  bought from a cloud service. This localizability allows  users to leverage models without sharing data with the  developers of the model, which can be important for  confidentiality and data protection (i.e., healthcare and  finance industry). However, it also limits the capacity to  monitor model use and misuse, in comparison to mod els that only allow API or web interface access.  Model size and use is an important factor when con sidering the effectiveness of legal means such as take down requests in controlling the wide distribution of  model weights. Large models and models that are used  heavily are more likely to leverage commercial datacen ter infrastructure than smaller or less frequently used  models.  The Spectrum of Model Open ness  This Report focuses on widely available model weights,  but developers of dual-use foundation models can re lease their models with varying levels of openness.23 Weights, code, training or fine-tuning data, and docu mentation can all be made available through multiple  channels with varying types of restrictions.  Multiple layers of structured access can provide vary ing levels of access to different individuals at different  times.24 For example, access to model weights could be  given to vetted researchers, but not to the general pub lic. Model sharing can involve a staged release, where  information and components are gradually released An Approach to Analysis of Marginal Risks and Benefits As mandated by Executive Order 14110, this Report analyzes “the potential benefits, risks, and implications of dual-use foundation models for which the model weights are widely available.”29 The assessment of policy options to address such models specifically, versus potential interventions to address risks more broadly, is the touchstone of our analysis. This Report will provide a broad assessment of the marginal risks and benefits of dual-use foundation models with widely available model weights. We define marginal risks and benefits as the additional risks and benefits that widely available model weights introduce compared to those that come from non-open foundation models or from other t echnologies more generally. Public commenters generally agreed that a marginal risk and benefit analysis framework is appropriate for our analysis.30 “The consideration of marginal risk is useful to avoid targeting dual-use foundation models with widely available weights with restrictions that are unduly stricter than alternative systems that pose a similar balance of benefits and risks.” The consideration of marginal risk is useful to avoid targeting dual-use foundation models with widely available weights with restrictions that are unduly stricter than alternative systems that pose a similar balance of benefits and risks. This does not mean that it is wise to distribute an unsafe open model as long as other equally unsafe systems already exist. Risks from open models and closed models should both be managed, though the particular mitigations required may vary. In some cases, managing the risk of open models may pose unique opportunities and challenges to reduce risk while maintaining as many of the benefits of openness as possible. As the basis for generating policy recommendations for open foundation models, this Report assesses the marginal benefits and risks of harm that could plausibly be affected by policy and regulatory measures. Marginal benefits and risks, as assessed in this Report, meet the following conditions: 1. There is a difference in magnitude between dual-use foundation models with widely available model weights as compared to such models without widely available weights. • Risks and benefits arising equally from both dual-use foundation models with widely available model weights and closed-weight dual-use foundation models are not considered “marginal.”31 2. The benefits or risks are greater for dual-use foundation models than for non-AI technologies and AI models not fitting the dual-use foundation model definition. • Only risks and benefits that arise differently from dual-use foundation models and models that do not meet this definition (e.g., models with fewer than 10 billion parameters) are considered “marginal.” • Similarly, the risks and benefits that exist equally in both dual-use foundation models AI and other technological products or services (such as Internet search engines) are not considered “marginal.” 10 Dual-Use Foundation Models with Widely Available Model Weights 3. The risks and benefits arise from models that will have widely available weights in the future over and above those with weights that have al-ready been widely released. As discussed above, once model weights have been widely released, it is difficult to “un-release” them. Any policy that restricts the wide availability of dual-use foundation model weights will be most effective on models that have not yet been widely released. When deciding whether to restrict the availability of a specific future set of dual-use foundation models, it is important to consider whether those future models will present substantially greater marginal risks and/ or benefits over existing models with widely available model weights. Not all policy options require restricting the wide availability of model weights. This consideration is most relevant for those policy options that require restricting the wide availability of model weights. Risks and benefits that satisfy all three conditions are difficult to assess based on current evidence. Most current research on the capabilities of dual-use foundation models is conducted on models that have already been released. Evidence from this research provides a baseline against which to measure marginal risks and benefits, but cannot preemptively measure the risks and benefits introduced by the wide release of a future model. It can provide relatively little support for the marginal risks and benefits of future releases of dual-use foundation models with widely available model weights, except to the extent that such evidence supports a determination about the capabilities of future dual-use foundation models with widely available model weights. Without changes in research and monitoring capabilities, this dynamic may persist: Any evidence of risks that would justify possible policy interventions to restrict the availability of model weights might arise only after those AI models, closed or open, have been released. “Without changes in research and monitoring capabilities, this dynamic may persist: Any evidence of risks that would justify possible policy interventions to restrict the availability of model weights might arise only after those AI models, closed or open, have been released.” 11 Dual-Use Foundation Models with Widely Available Model Weights Risks and Benefits of Dual-Use Foundation Models with Widely Available Model Weights 12 Dual-Use Foundation Models with Widely Available Model Weights  Dual-Use Foundation Models with Widely Available Model Weights 13  ments of the risks and benefits of open foundation mod els would benefit from an evidence base that includes a  robust set of “leading indicators,” or measures that can  act as warning signs for potential or imminent risk that  future open foundation models may introduce. Those  leading indicators might include assessments of the  capabilities of leading closed-weight foundation mod els (as similar behaviors and performance are likely to  be found in open foundation models within months or  years32) and other assessments of the evolving land scape of risks and benefits.  Open foundation model capabilities and limitations are  evolving, and it is difficult to extrapolate their capabili ties, as well as their impact on society, based on current  evidence. Further, even if we could perfectly extrapo late model performance, quantifying the marginal risks  and benefits is extremely difficult. For these reasons,  our analysis favors taking steps to develop the evidence  base and improve research techniques, as we address  in our policy recommendations.  Public Safety  This section examines the marginal risks and benefits to  public safety posed by dual-use foundation models with  widely available model weights. As the AI landscape  evolves, these risks, benefits, and overall impacts on pub lic safety may shift. The policy recommendations section  addresses these challenges.  This section considers some of the marginal risks and  benefits posed by open foundation models. This sec tion overviews the main factors identified in the Exec utive Order, the comments submitted to NTIA for this  Report, and existing literature. Neither the risks and  benefits discussed here, nor the categories they are  grouped into, should be considered comprehensive or  definitive. Other reports identified in the Executive Or der also overview some of these topics at greater length.  One limitation of this Report is that many AI models  with widely available model weights—while highly ca pable—have fewer than 10 billion parameters, and are  thus outside the scope of this Report as defined in Exec utive Order 14110. However, the number of parameters  in a model (especially in models of different modalities,  such as text-to-image or video generation models) may  not correspond to their performance. For instance, ad vances in model architecture or training techniques  can lead models which previously required more than  10 billion parameters to be matched in capabilities  and performance by newer models with fewer than 10  billion parameters. Further, as science progresses, it  is possible that this dynamic will accelerate, with the  number of parameters required for advanced capabil ities steadily decreasing.  These limitations, along with other factors, ultimate ly lead us to recommend that the federal government  adopt a monitoring framework to inform ongoing as sessments and possible policy action. Future assess“One limitation of this Report is that many AI models with widely available model weights—while highly capable—have fewer than 10 billion parameters, and are thus outside the scope of this Report as defined in Executive Order 14110. However, the number of parameters in a model (especially in models of different modalities, such as text-to-image or video generation models) may not correspond to their performance.”  Dual-Use Foundation Models with Widely Available Model Weights 14  actors to exploit proprietary models because open mod els are easier to manipulate and can share properties with  closed models.41  One mitigation that may work is using techniques includ ing tuning a model on distinct objective functions and  weakening its ability to produce dangerous information,  prior to its weights being made widely available. However,  we currently have limited technical understanding of the  relative efficacy of different safeguards, and protections  available to closed models might end up providing signifi cant additional protection.42  This Report considers two discrete public safety risks dis cussed in relation to dual-use foundation models with  widely available model weights: (a) lowering the barrier of  entry for non-experts to leverage AI models to design and  access information about chemical, biological, radiologi cal, or nuclear (CBRN) weapons, as well as potentially syn thesize, produce, acquire, or use them; and (b) enabling  offensive cyber operations through automated vulnerabil ity discovery and exploitation for a wide range of potential  targets.  Chemical, Biological, Radiological, or Nuclear  Threats to Public Safety  Widely available model weights could potentially exac erbate the risk that non-experts use dual-use founda tion models to design, synthesize, produce, acquire, or  use, chemical, biological, radiological, or nuclear (CBRN)  weapons.  Open model weights could possibly increase this risk  because they are: (i) more accessible to a wider range of  actors, including actors who otherwise could not develop  advanced AI models or use them in this way (either be cause closed models lack these capabilities, or they can RISKS OF WIDELY AVAILABLE MODEL  WEIGHTS FOR PUBLIC SAFETY  Dual-use foundation models with widely available mod el weights could plausibly exacerbate the risks AI models  pose to public safety by allowing a wider range of actors,  including irresponsible and malicious users, to leverage  the existing capabilities of these models and augment  them to create more dangerous systems.33 For instance,  even if the original model has built-in safeguards to pro hibit certain prompts that may harm public safety, such as  content filters,34 blocklists,35 and prompt shields,36 direct  model weight access can allow individuals to strip these  safety features.37 While people may be able to circum vent these mechanisms in closed models, direct access  to model weights can allow these safety features to be cir cumvented more easily. Further, these actions are much  easier and require fewer resources and technical knowl edge than training a new model directly. Such actions  may be difficult to monitor, oversee, and control, unless  the individual uploads the modified model publicly.38 As  with all digital data in the Internet age, the release of mod el weights also cannot feasibly be reversed.  While users can also circumvent safeguards in closed AI  models, such as by consulting online information about  how to ‘jailbreak’ a model to generate unintended an swers (i.e., creative prompt engineering) or, for more tech nical actors, fine-tuning AI models via APIs,39 methods to  mitigate these circumventions for an API-access system,  such as moderating data sent to a model and incorporat ing safety-promoting data during fine-tuning, exist. These  same mitigation strategies do not reliably work on AI mod els with widely available model weights.40 Experimenta tion with available model weights, while often helpful for  research to employ defenses against previously unknown  attacks, can also illuminate new channels for malicious  Dual-Use Foundation Models with Widely Available Model Weights 15  II. EASE OF DISTRIBUTION  Some experts have argued that the indiscriminate and  untraceable distribution unique to open model weights  creates the potential for enabling CBRN activity amongst  bad actors, especially as foundation models increase their  multi-modal capabilities and become better lab assis tants.48 No current models, proprietary or widely available,  offer uplift on these tasks relative to open source informa tion resources on the Internet.49 But future models, espe cially those trained on confidential, proprietary, or heavily  curated datasets relevant to CBRN, or those that signifi cantly improve in multi-step reasoning, may pose risks of  information synthesis and disclosure.50  III. FURTHER RESEARCH  Further research is needed to properly address the mar ginal risk added by the accessibility and ease of distribu tion of open foundation models. For instance, the risk  delta between jailbreaking future closed models for CBRN  content and augmenting open models, as well as how the  size of the model, type of system, and technical expertise  of the actor, may change these calculations remains un clear. Previous evaluations on CBRN risk may not cover all  available open models or closed models whose weights  could be made widely available.51 Future analysis should  distinguish between and treat separately each aspect of  chemical, biological, radiological, or nuclear risks associ ated with open model weights.  Experts must also assess the amount open models in crease this risk in the context of the entire design and de velopment process of CBRN material. Information about  how to design CBRN weapons may not be the highest  barrier for developing them. Beyond computational de sign, pathogens, toxins, and chemical agents need to be  not “jailbreak” them to generate the desired information);  and (ii) easy to distribute, which means that the original  model and augmented, offshoot models, as well as in structions for how to exploit them, can be proliferated and  used for harm without developer knowledge.  I. ACCESSIBILITY  This ease of access may enable various forms of CBRN risk.  For instance, large language models (LLMs) can generate  existing, dual-use information (defined as information  that could support creation of a weapon but is not sen sitive) or act as chemistry subject matter experts and lab  assistants, and LLMs with open model weights specifically  can be fine-tuned on domain-specific datasets, potentially  exacerbating this risk.43 However, the CBRN-related infor mation open models can generate compared to what us ers can find from closed models and other easily accessi ble sources of information (e.g., search engines), as well as  the ease of implementing mitigation measures for these  respective threats, remains unclear.44  Open dual-use foundation models also potentially in crease the level of access to biological design tools (BDT).  BDTs can be defined “as the tools and methods that en able the design and understanding of biological process es (e.g., DNA sequences/synthesis or the design of novel  organisms).”45 Intentional or unintentional misuse of BDTs  introduces the risk that they can create new information,  as opposed to large language models’ dissemination of  information that is widely available.46 While BDTs exceed ing the 10B parameter threshold are just now beginning to  appear, sufficiently capable BDTs of any scale should be  discussed alongside dual-use foundation models because  of their potential risk for biological and chemical weapon  creation.47  Dual-Use Foundation Models with Widely Available Model Weights 16  models to automatically generate malware attacks and  develop more sophisticated malware, such as viruses,56  ransomware,57 and Trojans.58 For instance, one of Meta’s  open foundation models, Llama 2, may have helped cy ber-attackers illicitly download other individuals’ employ ee login credentials.59  Finally, actors can leverage open models to exploit vul nerabilities in other AI models, through data poisoning,  prompt injections, and data extractions.60  FURTHER RESEARCH  The marginal cybersecurity risk posed by dual-use founda tion models with widely available model weights remains  unclear, and likely varies by attack vector and the preex isting capabilities of the cyber attackers in question.61 For  years, tools and exploits have become more readily acces sible to lower-resourced adversaries, suggesting that foun dation models may not drastically change the state of cy bersecurity, but rather represent a continuation of existing  trends. In the near term, the marginal uplift in capabilities  for cyber attackers that widely available weights introduce  to social engineering and phishing uses of foundation  models may be the most significant of possible risks.62  Closed foundation models and other machine learning  models that can detect software vulnerabilities, alongside  other cyber-attack tools, such as Metasploit, can also be  found online for free, and play a critical role in adversary  emulation.63 Further, while open models could provide  new instruments for performing offensive attacks, hackers  may not want to invest time, energy, and resources into le veraging these models to update their existing techniques  and tools.64 The extent to which a particular dual-use foun dation model with widely available model weights would  meaningfully increase marginal risk is therefore uncertain.  When an AI system or tool is built using a foundation mod physically generated, which requires expertise and lab  equipment to create in the real world.52 Other factors, such  as the ease of attaining CBRN material, the incentives for  engagement in these activities, and other mitigation mea sures—i.e., current legal prohibitions on nuclear, biologi cal, and chemical weapons—also determine the extent to  which open models introduce a substantive CBRN threat.  Offensive Cyber Operations Risks to Public Safety  Modifying an advanced dual-use foundation model with  widely available model weights requires significantly  fewer resources than training a new model and may be  more plausible than circumventing safeguards on closed  models. It is possible that fine tuning existing models on  tasks relevant to cyber operations could further aid in con ducting cyberattacks—especially for actors that conduct  operations regularly enough to have rich training data and  experimentation environments.53  FORMS OF ATTACKS  Cyber attacks that rely on dual-use foundation models  with widely available model weights could take various  forms, such as social engineering and spear-phishing, mal ware attack generation, and exploitation of other models’  vulnerabilities.  First, open foundation models could enable social engi neering (including through voice cloning and the auto mated generation of phishing emails).54 Attacks could also  take the form of automated cybersecurity vulnerability  detection and exploitation.55 The marginal cybersecurity  risk posed by the wide distribution of dual-use foundation  models may increase the scale of malicious action, over whelming the capacity of law enforcement to effectively  respond.  Cyber-attackers could also potentially leverage open  Dual-Use Foundation Models with Widely Available Model Weights 17  trinsic technical benefits of openness allow a wider range  of users to benefit from the value foundation models in troduce to securing computing systems.70 For instance,  entities have published cyber-defense toolkits and creat ed open-source channels for collaboration on cyber de fense.71  Furthermore, any advances in dual-use foundation mod els’ offensive cyber-attack capabilities may also strength en defensive cybersecurity capabilities. If dual-use foun dation models develop advanced offensive capabilities,  those same capabilities can be used in securing systems  and defending against cyberattacks. By detecting and ad dressing otherwise-undetected cybersecurity vulnerabil ities, dual-use foundation models with widely available  model weights could facilitate stronger cyber-defensive  measures at scale.72 Parity of licit access to models that  have offensive cyber capabilities is also important for ac curate adversary emulation, as advanced international  cyber actors may incorporate such models into their own  tradecraft. However, these benefits must be contextual ized within the larger cyber defense landscape, as many  developers perform their most effective cyber defense re search internally.  Safety Research & Identification of Safety and Se curity Vulnerabilities  Widely available model weights can propel AI safety re search. Open foundation models allow researchers with out in-house proprietary AI models, such as academic  institutions, non-profits, and individuals, to participate in  AI safety research. A broad range of actors can experiment  with open foundation model weights to advance research  on many topics, such as vulnerability detection and miti gation, watermarking failures, and interpretability.73  el with widely available model weights, the inclusion of  the model could introduce unintentional cybersecurity  vulnerabilities into the application as well.65 Promisingly,  it is more readily possible to prevent these types of harms  –where the deployer of the model does not desire for the vulnerability to be present – than harms intentionally lev eraged by the deployer of the model.66 In line with the Cy bersecurity and Infrastructure Security Agency’s Secure by  Design guidance, developers of AI models – whether open  or closed source – can take steps to build in security from the start.67  BENEFITS OF WIDELY AVAILABLE MODEL  WEIGHTS FOR PUBLIC SAFETY  The open release of foundation model weights also in troduces benefits. Specifically, widely available model  weights could: (a) bolster cyber deterrence and defense  mechanisms; (b) propel safety research and help identify  safety and security vulnerabilities on future and existing  models; and (c) facilitate transparency and accountability  through third-party auditing mechanisms.  Cyber Defense  Open foundation models can further cyber defense initia tives. For example, various cyber defense models, such as  Security-BERT,68 a privacy-preserving cyber-threat detec tion model, are fine-tuned versions of open foundation  models.69 These models and other systems built on open  models provide security benefits by allowing firms, re searchers, and users to use potentially sensitive data with out sending this data to a third-party proprietary model  for processing. Models with widely available weights also  have more flexibility to be narrowly optimized for a partic ular deployment context, including through quantization,  allowing opportunities for cost savings. Thus, several inActors can also tailor safeguards to specific use-cases, thus improving downstream models. Creating external guardrails for dual-use foundation models can pose an abstract, under-specified task; actors that use open models for specific purposes can narrow and concretize this task and add on more targeted and effective safety training, testing, and guardrails. For instance, an actor that fine-tunes a foundation model to create an online therapy chatbot can add specific content filters for harmful mental health content, whereas a general-purpose developer may not consider all the possible ways an LLM could produce negative mental health information. Open foundation models allow a broader range of actors to examine and scrutinize models to identify potential vulnerabilities and implement safety measures and patches, permitting more detailed interrogation and testing of foundation models across a range of conditions and variables.74 This scrutiny from more individuals allows developers to understand models’ limitations and ensure models’ reliability and accuracy in scientific applications. An open model ecosystem also increases the availability of tools, such as open-source audit tooling projects, available for regulators to monitor and evaluate AI systems.75 Experimentation on model weights for research may also help propel alignment techniques. Llama 2, for example, has enabled research on reinforcement learning from human feedback (RLHF), though the underlying RLHF mechanism was first introduced by OpenAI, a closed model-weight company.76 Open models will likely help the AI community grapple with future alignment issues. However, as models develop, this research benefit should be weighed against the possibility that open model weights could enable some developers to develop, use, or finetune systems without regard for safety best practices or regulations, resulting in a race to the bottom with negative impacts on public safety or national security. Malicious actors could weaponize or misuse models, increasing challenges to effective human control over highly capable AI systems.77 Auditing and Accountability Weights, along with data and source code, are a critical piece of any accountability regime. Widely available model weights more readily allow neutral third-party entities t o assess systems, perform audits, and validate internal developer safety checks. While access to model weights alone is insufficient for conducting more exhaustive testing, it is necessary for most useful testing of foundation models.78 Expanding the realm of auditors and allowing for external oversight regarding developers’ internal safety checks increases accountability and transparency throughout the AI lifecycle, as well as public preparedness for harms. This is for three reasons. First, the developer may be able to use information from external auditors about its model’s robustness to improve the model’s next iteration, and other AI developers may be able to benefit from this information to identify potential vulnerability points to avoid in future models. “Weights, along with data and source code, are a critical piece of any accountability regime. Widely available model weights more readily allow neutral third-party entities to assess systems, perform audits, and validate internal developer safety checks.” 18 Dual-Use Foundation Models with Widely Available Model Weights  Dual-Use Foundation Models with Widely Available Model Weights 19  geopolitics. The availability of model weights could allow  countries of concern to develop more robust advanced  AI ecosystems, which, given the dual-use nature of foun dation models, could pose risks to national security and  public safety, and undercut the aims of U.S. chip controls.  These countries could take U.S.-developed models and  use them to enhance, perhaps substantially, their military  and intelligence capabilities. There are also risks that may  arise from the imposition of international standards that  are not in line with U.S. values and commercial interests.  On the benefits side, encouraging the availability of model  weights could bolster cooperation with allies and deepen  new relationships with developing partners.  GEOPOLITICAL RISKS OF WIDELY AVAIL ABLE MODEL WEIGHTS  Implications for Global Digital Ecosystem  The wide availability or restrictions of U.S. origin model  weights could have unpredictable consequences for the  global digital ecosystem, including by prompting some  states to restrict open-source systems, causing further  fragmentation of the Internet based on level of AI open ness (i.e., a “splinter-net scenario”).  More restrictive jurisdictions may also be better placed to  set the terms of AI regulation and standards more broadly,  even if this comes at the cost of innovation. States look ing for how to regulate AI generally, including open mod els, may naturally look to imitate or adapt already existing  regulatory approaches. Regulatory requirements would  breed demand for standards. The United States would in  this scenario pay a penalty in its ability to shape interna tional standards on AI, even if it is still at a net advantage  in setting standards.  Second, third-party evaluations can hold developers ac countable for their internal safety and security checks,  as well as downstream deployers responsible for which  models they choose to use and how, which could im prove accountability throughout the AI lifecycle. Of note,  it may be difficult to implement such a third-party evalu ation system due to differences in evaluations, the lack of  ability to articulate how models can fail, and the scale of  potential risks. Accessible model weights, alongside data  and source code, facilitate oversight by regulatory bodies  and independent researchers, allowing for more effective  monitoring of AI technologies.79  Finally, a robust accountability environment may increase  public trust and awareness of model capabilities, which  could help society prepare for potential risks introduced  by AI. The public can then respond to and develop resil iency measures to potential harms that have been demon strated empirically. A foundation model ecosystem in  which many models have widely available weights also  further promotes transparency and visibility within the  field, making it easier for the broader community to un derstand how models are developed and function.80  These community-led AI safety approaches could result  in safer models, increased accountability, and improved  public trust in AI and preparedness for potential risks. This  transparency is vital for fostering trust between AI devel opers and the public and encourages accountability, as  work is subject to scrutiny by the global community.81  Geopolitical Considerations  This section highlights the marginal risks and benefits re lated to the intersection of open foundation models and  Dual-Use Foundation Models with Widely Available Model Weights 20  also apply saved resources to other initiatives, such as tal ent cultivation, other technologies, and various other sec tors that provide economic and security benefits.  Actors in these nations can also glean insights from open  models about model architecture and training techniques,  bolstering their long-term dual-use AI innovation.83 Lead ing labs have already developed models based on Llama  2’s architecture and training process with similar capabili ties to Llama 2.84  This dual-use foundation model R&D could then acceler ate global competition to build powerful AI-enabled na tional security applications and undercut the aim of U.S.  export controls on semiconductors and related materials.  DUAL-USE IMPLICATIONS  Countries of concern could incorporate open foundation  models into their military and intelligence planning, capa bilities, and deployments. Due to the lower-cost nature of  models with widely available model weights and the abil ity to operate these systems in network-constrained envi ronments, it is easier for countries of concern to integrate  dual-use foundation models in military and intelligence  applications. Actors could experiment with foundation  models to advance R&D for myriad military and intelli gence applications,85 including signal detection, target  recognition, data processing, strategic decision making,  combat simulation, transportation, signal jams, weapon  coordination systems, and drone swarms. Open models  could potentially further these research initiatives, allow ing foreign actors to innovate on U.S. models and discover  crucial technical knowledge for building dual-use models.  Some foreign actors are already experimenting with this  type of AI military and intelligence research and applica tions. Since actors can inference on open model weights  Inconsistencies in approaches to model openness may  also divide the Internet into digital silos, causing a “splin ter-net” scenario. If one state decides to prohibit open  model weights but others, such as the United States, do  not, the restrictive nations must, in some way, prevent  their citizens from accessing models published elsewhere.  Since developers usually publish open model weights on line, countries that choose to implement stricter measures  will have to restrict certain websites, as some countries’  websites would host open models and others would not.  Accelerate Dual-Use AI Innovation in Countries  of Concern  Countries of concern can leverage U.S. model weights  for their own AI research, development, and innovation,  which, given the dual-use nature of foundation models,  may introduce risk to U.S. national security and public  safety.  GENERAL AI INNOVATION  Many U.S. open foundation models are more advanced  than most closed models developed in other nations, in cluding countries of concern.82 Developers in these coun tries may choose to use U.S. open models instead of ex pending the resources or time necessary to build their  own.  This development bolsters dual-use AI innovation in these  countries and could help them create advanced techno logical ecosystems that they (1) may not have been able to  build otherwise and (2) do not have to expend significant  resources or time to create. Individual companies can al locate the funds they would have spent on up-front train ing to downstream product enhancements, improved  dissemination tactics (e.g., marketing), and new products.  Governments that would have subsidized AI training could  Dual-Use Foundation Models with Widely Available Model Weights 21  with the wide availability of model weights.86 Thus, the  open release of some foundation models may speed up  the dual-use AI innovation and military application of AI by  countries of concern, directly propelling the industry and  national security capabilities that export controls and oth er U.S. actions aim to restrict.  The open release of some specific model weights could  undermine U.S. technological leadership by distributing  access to cutting-edge models—and the associated tech nological power—to foreign adversaries.  Some have argued that models accessible only through  APIs pose fewer national security risks.87 OpenAI com mented that it had already taken steps to disrupt certain  malicious nation-state affiliated actors who were using  ChatGPT for cyber operations, which has been enabled  in part through its use of a closed model distributed via  an API.88 Notably, the fact that these operations were con ducted using closed models suggests that adversaries  might used closed models even when open foundation  models are available.  FURTHER RESEARCH  While open model weights could bolster AI military in novations in an untraceable, unregulated manner, the  marginal risk open models pose to overall military and  intelligence R&D, as well as the amount U.S. open models  support country-of-concern AI development for military  locally, U.S. developers cannot determine when and how  countries of concern are using their model weights to  research military and intelligence innovations. As open  models improve over time, countries of concern could  potentially use them to create even more dangerous sys tems, such as adaptive agent systems and coordinated  weapon systems. Making model weights widely available  may also allow countries of concern to coordinate their AI  development, allowing them to develop specialized mod els depending on their needs and diversify spending. For  example, a country of concern could invest in a specialized  model to assist in creating CBRN weapons, or create mod els focused on misinformation and disinformation.  The U.S. government has already recognized the national  security threat of countries of concern gaining access to  powerful, dual-use leading models and worked to mitigate  it through actions such as restrictions on advanced com puting chips and semiconductor manufacturing equip ment exports to certain nations. These controls attempt to  limit adversaries access to computing power, which slows  their development of advanced AI models related to mil itary applications. Widely available model weights could  allow strategic countries of concern to avoid U.S. policy in struments designed to hinder computational capabilities.  Strategic countries of concern would not need to make the  same investments in semiconductors and other hardware  in order to produce similarly high performing AI models  Dual-Use Foundation Models with Widely Available Model Weights 22  man rights through technological cooperation. Countries  that have more open AI model ecosystems can more easily  participate in international technological cooperation and  crucial research exchanges, while, in turn, supporting their  own open AI model ecosystems.  The safe, secure, and trustworthy deployment of AI re quires coordination with allies and partners. Many U.S.  allies have expressed an interest in maintaining an open  AI ecosystem. Supporting an AI ecosystem that includes  foundation models with widely available model weights  could also bolster existing U.S. alliances, as well as po tentially enhance partnerships that continue to mature.  For example, economies that actively support their open  model ecosystems include the Republic of Korea, Taiwan,  Philippines, France, and Poland. These like-minded part ners are critical in the current geopolitical landscape, and  further cooperation with them in the realm of foundation  models with widely available model weights would only  serve to deepen ties and reinforce these mutually benefi cial relationships.  By building relationships with other like-minded partners,  U.S. allies can promote models with widely available mod el weights to third countries. This will become increasingly  important as AI is adopted in global majority nations. The  more the United States and its likeminded partners coor dinate on creating a narrative that U.S. models with wide ly available model weights can spur innovation and pro mote competition, the more adoption of U.S. AI models  will take place in developing countries. Coordination with  like-minded partners will also be important to generally  manage risks that arise from AI.89 Moreover, if developing  partners have access to U.S. open models, they may use  open models from foreign countries of concern models  less, if at all.  and civil technologies, is unknown. The trade-off between  country-of-concern usage of open models compared to  their own domestic national security research also re mains uncertain and depends on the country.  GEOPOLITICAL BENEFITS OF WIDELY  AVAILABLE MODEL WEIGHTS  Increase Global Use of U.S. Open Models  Widely available model weights could increase global  adoption of U.S. origin models, thereby promoting the de velopment of a global technology ecosystem around U.S.  open models rather than competitors’ open models. A  burgeoning foundation model ecosystem does not appear  overnight; creating the necessary infrastructure, such as  datacenters and software ecosystems, talent cultivation  systems, such as AI education and training, and even a  creative, innovation start-up culture requires significant  time, funding, and consideration. If the option to use open  U.S. models exists, foreign states and non-state actors may  not want to invest the time, money, and energy necessary  to create their own foundation model market. This incen tive structure could increase foreign adoption of U.S. ori gin open models, which are currently less powerful than  proprietary models. Additionally, widespread use of U.S.  open models would promote the United States’ ability to  set global norms for AI, bolstering our ability to foster glob al AI tools that promote the enjoyment of human rights.  Foster Positive Relationships across the Globe  Nurturing the open model ecosystem could foster positive  relationships between the United States, allies and oth er countries that benefit from an open model ecosystem,  such as developing partners, which may lead to enhanced  international cooperation as well as the promotion of huPromote Democratic Values in the Global AI Ecosystem U.S. open model weights could steer the technological frontier towards AI that aligns with democratic values. The U.S. spearheading frontier AI development, and other nations building models on the foundation of U.S. open models, may increase the likelihood that the many cutting-edge AI technologies, along with correlated training techniques, safety and security protections, and deployment strategies, are built to uphold democratic values. However, we would note that it is not a given that AI applications built on open model weights will preserve democratic values. Societal Risks and Well-Being Dual-use foundation models with widely available model weights have the potential to create benefits across society, primarily through the access to AI capabilities that such models provide. At the same time, they also pose a substantial risk of causing harms to individuals and society. As noted above, our assessment of risk is tied to a framework of marginal risk: “the extent to which these models increase societal risk by intentional misuse beyond closed foundation models or pre-existing technologies.”90 Further, due to the relative novelty of dual-use foundation models, especially models that generate output in modalities beyond text (i.e., video and image generation), combined with known difficulties in accurate reporting for societal risks, precise estimates of the extent of these risks (especially the marginal risk of open foundation models over other models) are challenging to produce.91 The societal risks and benefits discussed in this section e xtrapolate from existing applications of open models that do not meet the parameter size criteria for this Report.92 For example, there are no text-to-image models with widely available model weights with over 10 billion (10B) parameters available today, while there are multiple text generative based open models over the 10B parameter threshold.93 Other developers of sophisticated closedweight multi-modal models, such as SORA from OpenAI, have not publicly announced how many parameters they have. “Dual-use foundation models with widely available model weights have the potential to create bene its across society, primarily through the access to AI capabilities that such models provide.” It is also important to note that the content discussed in this section is not created in a vacuum. Both risks and benefits accrue due to how easily and widely accessible the tools for content creation are, as well as how the content is distributed. In the case of harmful content, some measure of that risk is dependent on how effectively platforms, content distributors, and others can prevent its widespread distribution. In the case of privacy and information security, there are still open questions as to how much the model “memorizes” from the data sets it was trained on and how much of that “memorization” contains personally identifiable information.94 These risks are also embedded in our social systems. While a number of social risks 23 Dual-Use Foundation Models with Widely Available Model Weights and benefits arise from open foundation models, this section covers only a select few. SOCIETAL RISKS OF WIDELY AVAILABLE MODEL WEIGHTS CSAM & NCII Models with widely available weights are already used today for AI-generated child sexual abuse material (CSAM), AI-generated non-consensual intimate imagery (NCII), and other forms of abusive content.95 Such content may include the depiction of wholly fabricated individuals as well as specific individuals created using preexisting images of them. This content disproportionately affects women and teens, although any individual can be affected, and creates a hostile online environment that undermines equitable access to online services.96 Such content, even if completely AI generated, can pose both immediate and long-term harm to its targets, especially if widely distributed, and creates a systemic risk across digital platforms for gender-based harassment and intimidation.97, 98 Open foundation models lower the barrier to create AI-generated CSAM and NCII. Creating such content using an open foundation model requires only a set of images to fine-tune the model, as opposed to creating a model from scratch. Open foundation models and downstream applications built from them, such as so-called ‘nudifying’ apps,99 have made it easy to create with little to no cost individually targeted NCII and CSAM, which significantly enables both the production and distribution of AI-generated (but highly realistic) NCII and CSAM.100 They also make it easier to distribute CSAM and NCII because there is no limit on the amount of content that can be created, making it possible to rapidly generate large amounts of AI-generated material. In contrast, closed model providers can more easily restrict or prevent the creation of CSAM and NCII through restrictions on prompts as well as on APIs. Prior to the wide availability of AI systems, synthetic CSAM (e.g., non-photo based CSAM) primarily focused on non-realistic categories of material, such as anime-styled CSAM.101 Open foundation models that include pornography or CSAM in their training data, as well as downstream implementations of open models that have been finetuned on CSAM or similar material, allow for the creation of AI-generated CSAM that is realistic to the point of being easily confused with non-AI-generated images and even based on real individuals.102 NCII content specifically is often based on individuals who have never shared any form of nude images online. Creating such content prior to the release of generative AI models at the level of realism now achievable previously required both considerable skill with photo editing tools as well as a significant investment of time. “Models with widely available weights are already used today for AI-generated child sexual abuse material (CSAM), AI-generated non-consensual intimate imagery (NCII), and other forms of abusive content.” While the threat of NCII specifically is not unique to open foundation models (in one of the most publicized incidents to date—when AI-generated NCII images of singer Taylor Swift spread across the Internet in early 2024—the images were created with a closed generative model), since the emergence of open foundation models, researchers have 24 Dual-Use Foundation Models with Widely Available Model Weights  Dual-Use Foundation Models with Widely Available Model Weights 25  Political Deepfakes  The capacity for malicious actors to use open foundation  models to create convincing political deepfake content  across a variety of modalities has introduced marginal  risk to the integrity of democratic processes.110 Actors are  already generating and disseminating political deepfakes  using downstream apps built upon both open and pro prietary models. For example, deepfake audio recordings  created with a proprietary voice cloning AI model emerged  in January 2024, mimicking President Biden discouraging  voters in the New Hampshire primary via robocalls.111 This  incident created immediate and widespread concerns  about the potential impact on voter turnout, as well as  what such incidents portended for upcoming elections  and the democratic process as a whole.112  Internationally, campaigners supporting the re-election of  Prime Minister Modi and other politicians in India are using  open models to create synthetic videos and deliver per sonalized messages to voters by name, creating concerns  that the public may not be able to discern the fake content  from authentic material.113 (At least some post-election  accounts indicate that this concern failed to materialize  and that generative AI enabled politicians to more easily  communicate with voters in the 22 official languages of In dia.114) In 2022, a deepfake of Ukrainian President Volody myr Zelensky circulated widely online, in which the false  imitation of President Zelensky urges Ukrainian soldiers to  lay down arms.115  In the absence of detection, disclosure, or labeling of  synthetic political content, malicious actors can create  deepfake videos or audio messages to unduly influence  elections or enable disinformation campaigns.116 Once re leased, they can be difficult to remove from the Internet,  even after they have been verified as fake, in part due to  documented significant increases in AI-created CSAM103  and NCII. For example, the release of Stable Diffusion 1.5,  an open model with 860 million parameters104 (and which  was revealed to have included documented CSAM in its  training data),105 enables the direct creation of CSAM, and  fine-tuned versions of the model have been used in down stream apps.106 This increase in harmful content enables  producers to flood online platforms with enough content  to overwhelm platform trust and safety teams and law en forcement’s capacity to ingest and process CSAM reports.  Due to the sheer volume and speed of production they  enable, the availability of open foundation models to cre ate CSAM and NCII represents an increase in marginal risk  over both existing closed foundation models and existing  technologies.107 The legal and regulatory system devoted  to investigating and preventing the distribution of CSAM  is not equipped to handle this influx of content.108 As open  foundation models become more advanced, this threat  will likely increase.  The mass proliferation of CSAM and NCII also creates a  substantial burden for women, teens and other vulnera ble groups to live and participate in an increasingly online  and digitized society. Further, proliferation of CSAM and  NCII can discredit and undermine women leaders, jour nalists, and human rights defenders, and the implications  of this harm extend beyond the individual to society and  democracy at-large.109 Again, these are risks that do not ex ist in a vacuum; the magnitude of harm in part depends on  the ability to distribute content at scale.  Dual-Use Foundation Models with Widely Available Model Weights 26  DISINFORMATION  While the release of open foundation models raises con cerns about the potential to enable disinformation cam paigns by adversarial actors, assessments are mixed re garding whether open models, at least at current levels of  capabilities, pose risks that are distinct from proprietary  models. There is evidence that open foundation models,  including LLMs, are already being used today to create dis information-related content.120 Disinformation research ers anticipate that generative models “will improve the  content, reduce the cost, and increase the scale of cam paigns; that they will introduce new forms of deception  like tailored propaganda; and that they will widen the  aperture for political actors who consider waging these  campaigns.”121 As a consequence of an anticipated in crease in the production of disinformation related content,  one commenter expressed concerns that such content  produced at significant enough of a scale would later be  ingested by AI systems as training data, perpetuating its  half-life.122  While many agree that open foundation models enable a  larger range of adversarial actors to create disinformation,  others dispute the importance of this assertion. Some re searchers argue that the bottleneck for successful disinfor mation operations is not the cost of creating it.123 Because  the success of disinformation campaigns is dependent on  effective distribution, key to evaluating marginal risk is  whether the potential increased volume alone is an im portant factor, such that it may overwhelm the gatekeep ers on platforms and other distribution venues. Some are  skeptical that this is the case.124 Carnegie researchers ar gue that not only has disinformation existed long before  the advent of AI, but that generative AI tools may prove  useful to researchers and others combating disinforma tion.125  the reluctance of social media platforms to remove this  content. Deepfakes can also increase the “liar’s dividend”:  skepticism and disbelief about legitimate content, contrib uting to pollution of the wider information environment.117  This could undermine democratic processes by confusing  voters and reducing the public’s ability to determine fake  events from actual ones.  As with concerns about CSAM and NCII, most of the open  models capable of producing political deepfakes today  have fewer than 10 billion parameters, but evidence does  exist that political deepfake content has already been cre ated and disseminated using open models under the 10B  threshold. While deepfakes are a widespread source of  concern, current dual-use foundation models with widely  available model weights may not substantially exacerbate  their creation or inflict major societal damage given the  existing ability to create deepfakes using closed models,  but, as open foundation models develop, this risk may in crease.  Disinformation & Misinformation  Similar to the concerns described earlier regarding CSAM  and NCII, the primary risks are tied to the low barriers to  entry that open models may create for greater numbers  of individuals, as well as to coordinated influence opera tions to create content and distribute it at scale.118 Further,  researchers have expressed concerns that the capabilities  of generative LLMs may allow foreign actors to create tar geted disinformation with greater cultural and linguistic  sophistication.119 As noted in the section on geopolitical  considerations, the wide availability of open U.S. models  could bolster dual-use AI innovation in countries of con cern, which can enable them to develop more sophisticat ed disinformation campaigns.  Dual-Use Foundation Models with Widely Available Model Weights 27  the tool fictionalized legal sources.134 In other instances,  generative models have output untrue and potentially  slanderous information about individuals, including pub lic figures.135  Discriminatory Outcomes  Discrimination occurs when people are treated differently,  solely or in part, based on protected characteristics such  as gender, religion, sexual orientation, national origin, col or, race, or disability.136 Discrimination based on protected  classes is, unfortunately, a widespread issue, impacting  many groups by race, gender, ethnicity, disability, and oth er factors.137 There has been substantial documentation of  AI models, including open foundation models, generating  biased or discriminatory outputs, despite developers’ ef forts to prevent them from doing so.138  Open foundation models may exacerbate this risk be cause, even if the original model has guardrails in place  to help alleviate biased outcomes, downstream actors  can fine-tune away these safeguards. These models could  also be integrated into rights-impacting systems with little  oversight and no means of monitoring their impact. Thus,  it may be difficult to prevent open foundation models or  their downstream applications from perpetuating biases  and harmful institutional norms that may impact individ uals’ civil rights.139 Bias encoded in foundation models be comes far more powerful when those models are used in  decisional contexts, such as lending,140 health care,141 and  criminal sentencing.  From a regulatory perspective, commercial actors that  implement tools built on open foundation models will  be subject to the same federal civil rights laws as those  who leverage single-use models or proprietary founda tion models.142 The breadth of the potential impact and  MISINFORMATION  Unlike disinformation, which implies intentional malfea sance, misinformation encompasses factually incorrect  information, or information presented in a misleading  manner.126 All foundation models are known to create and  even help propagate factually incorrect content.127, 128 Mali cious actors may intentionally use models to create this in formation, and models can unintentionally produce inac curate information, often referred to as “hallucinations.”129,  130 The marginal risks open foundation models pose in  regards to misinformation are similar to those raised  by CSAM, NCII, deepfakes and disinformation: they may  lower the bar for individuals to create misinformation at  scale and allow for more prolific distribution of misinfor mation. These impacts may exacerbate the disruption to  the overall information ecosystem131 and high volumes of  misinformation may overwhelm information distributors’  capacity to identify and respond to misinformation. How ever, some researchers argue that consumption of misin formation is limited to individuals more likely to seek it out  and that foundation models do not substantially alter the  amount or impact of this content online.132  One aspect of this marginal risk that necessitates further  study is how individuals react to misinformation when it  is directly outputted from an AI system (e.g., a chatbot)  compared to consumed on social media or another plat form. Little research to date exists that interrogates the  consumption of misinformation directly from AI powered  tools.133 The majority of the public does not yet seem to  understand generative AI’s propensity for producing in accurate information and may place undue trust in these  systems; there have been well-publicized instances of law yers, for example, relying on ChatGPT to assist with brief  writing only to be surprised and embarrassed to find that  Dual-Use Foundation Models with Widely Available Model Weights 28  Scientists and researchers can tailor open models to better  suit specific research needs or experimental parameters,  enhancing the relevance and applicability of their work.148  This customization capability is crucial for advancing sci entific inquiries that require specialized models to analyze  unique datasets or to simulate particular scenarios.149  AI Access for Entrepreneurs and Creatives  The wide availability of model weights can catalyze cre ativity and innovation, providing entrepreneurs, artists,  and creators access to state-of-the-art AI tools.150 By low ering barriers to access, a broader community can experi ment with AI, leading to novel applications and creations.  New entrants into the marketplace would not have to pay  for a closed model, for example, to utilize the benefits of  advanced AI systems. This democratization fuels a wide  range of entrepreneurial ventures and artistic expressions,  enriching the cultural landscape and reflecting a diverse  array of perspectives and experiences.151 This democrati zation can be particularly beneficial for small and medi um-sized enterprises, which may otherwise face signifi cant barriers to accessing more advanced AI systems.  Bias and Algorithmic Discrimination Mitigation  The ability to test for bias and algorithmic discrimination is  significantly enhanced by widely available model weights.  A wider community of researchers can work to identify  biases in models and address these issues to create fair er AI systems. Including diverse communities and partici pants in this collaborative effort towards de-biasing AI and  improving representation in generative AI is essential for  promoting fairness and equity. mitigating bias in AI. Com menters have highlighted the importance of transparency  and oversight enabled by open model weights in attempt ing to fight bias and algorithmic discrimination in foun SOCIETAL BENEFITS OF WIDELY AVAIL ABLE MODEL WEIGHTS  Releasing foundation model weights widely also introduc es benefits for society. Specifically, widely available model  weights can: (a) support AI use for socially beneficial initia tives; (b) promote creativity by providing more accessible  AI tools for entrepreneurial or artistic creation and expres sion; and (c) provide greater ability to test for bias and al gorithmic discrimination.  Open Research for the Public Good  Making foundation model weights widely available allows  a broader range of actor researchers and organizations  to leverage advanced AI for projects aimed at improving  public welfare. This approach democratizes access to  cutting-edge technology, enabling efforts across health care,143 environmental conservation,144 biomedical inno vations, and other critical areas that benefit society.145, 146, 147 the current lack of clear determination regarding how to eliminate bias from all forms of AI models indicates that more research is needed to determine whether open foundation models substantially change this risk. “...it may be di icult to prevent open foundation models or their downstream applications from perpetuating biases and harmful institutional norms that may impact individuals’ civil rights. Bias encoded in foundation models becomes far more powerful when those models are used in decisional contexts, such as lending, health care, and criminal sentencing.”  Dual-Use Foundation Models with Widely Available Model Weights 29  vantage of as the technologies develop. A few companies  spend vast amounts on the physical infrastructure to train  foundation models, rendering it difficult for academics,  smaller companies, nonprofits, and the public sector to  keep pace.158 The $2.6 billion requested in January 2023  over six years for the National Artificial Intelligence Re search Resource (NAIRR) is significantly less than the over  $7 billion that Meta expects to spend on GPUs alone this  year; Facebook, a Meta company, states that it will have  350,000 H100 GPUs by the end of 2024, whereas leading  universities have just hundreds.159, 160, 161 Companies retain ing proprietary control over the most advanced AI models,  with the biggest companies making the largest invest ments by far, could continue to concentrate the economic  power that derives from foundation models and hinder  innovation more broadly.162 The potential entrenchment  of incumbent firms risks significant harm to competition.  The effects that dual-use foundation models with widely  available model weights may have on these dynamics is  uncertain. Open model weights are unlikely to substantial ly impact the advanced foundation model industry, given  constraints such as access to compute and other resourc dation models.152 As more companies, local governments,  and non-profits use AI for rights-impacting activities, such  as in healthcare, housing, employment, lending, and edu cation, the need to better understand how these systems  perpetuate bias and discrimination. There is a long history  in the civil rights community of collaborative testing, and  the wide availability of model weights enables that tradi tion to continue.153  Competition, Innovation, and  Research  This section covers the marginal risks and benefits du al-use foundation models with widely available model  weights may introduce to AI competition, innovation, and  research.154  In traditional products, like cars or clothes, much of the  price paid by the consumer goes toward producing that  specific item. However, AI models, like television shows  and books, are information goods. Training an advanced  AI model requires a vast amount of resources, including  financial resources,155 but once trained, the model can be  reproduced at a much lower cost.156 “Vertical” markets of  information goods like this reduce competition and lead  to the dominance of a small number of companies. Suc cessful companies can use their resources to produce  higher quality products, driving out competitors and gain ing even more market control.157 Despite this rapid growth,  markets related to AI foundation models risk potentially  tending towards concentration that may lead to monopo ly or oligopoly. The potential tendency toward monopoly  or oligopoly partially derives from the structural advan tages that already-dominant firms may be able to take ad“The ability to test for bias and algorithmic discrimination is significantly enhanced by widely available model weights. A wider community of researchers can work to identify biases in models and address these issues to create fairer AI systems.”  Dual-Use Foundation Models with Widely Available Model Weights 30  Thus, there is a risk that the dual-use foundation models  with widely available model weights—without additional  components being made available or other work being  undertaken—may create the perception of more compe tition. Without additional openness and transparency, it  could seem as if there are more players throughout the  AI supply chain while only a few companies still control  most of the compute and human capital. For example, a  small number of companies currently dominate the tech  sector, having grown to prominence in an era that em braced open-source software for many purposes. With  open source software, programmers make their work  freely available to the community.168 This open sharing  of resources has added trillions to the global economy169  and is a staple of software development today and wide ly supported worldwide.170 But it has also been argued  to increase global inequality.171 Investing in open source  models can be an optimal business model for companies  in ways that might lead to further market concentration,172  without necessarily reinvesting into the communities that  contributed to the development of the technologies.173  Businesses might create an open AI model to create a “first  mover advantage,” leading to wider adoption of their par ticular technology. In turn, this might push competitors  out of the marketplace, support free public development  of the companies’ internal systems,174 and create future li censing opportunities.175 In the context of open source, for  example, some commentators have noticed that “where  some tech companies initially fought open source, see ing it as a threat to their own proprietary offerings, more  recently these companies have tended to embrace it as a  mechanism that can allow them to entrench dominance  by setting standards of development while benefiting  from the free labor of open source contributors.”176 A sim ilar dynamic may occur in the AI context.177 At the same  es. However, even with just a few foundation models in  the ecosystem, downstream applications may generally  become more competitive.163 With this caveat in mind,  there are certain effects to competition, innovation, and  research that can be associated with dual-use foundation  models with widely available model weights.  RISKS OF WIDELY AVAILABLE MODEL  WEIGHTS FOR COMPETITION, INNOVA TION, AND RESEARCH  Perception of more market diversity than actual ly exists due to other factors  Widely available model weights are only one of many  components in the gradient of openness of AI dual-use  foundation models,164 and their availability alone may  be insufficient to bring about significant and long-lasting  benefits.165 The degree to which dual-use foundation mod els with widely available model weights may provide ben efits to competition, innovation, and research is not fully  clear, but the benefits seem more likely to be realized or  maximized when additional conditions are in place to per mit their full utilization. In particular, the benefits of these  models may vary depending on whether other compo nents of a model (e.g., training data, model architecture)  and related resources (e.g., compute, talent/labor, fund ing for research) are also readily available.166 Furthermore,  vertical integration of the AI stack among a few key players  could serve to bottleneck upstream markets, which may  impact downstream use and applications of these mod els.167  Dual-Use Foundation Models with Widely Available Model Weights 31  them more control over sensitive data used in fine-tuning,  the biases of systems, and more, as opposed to accessing  models through an API, which may raise latency and priva cy concerns. This control may be particularly pertinent to  healthcare and education service providers. However, the  level at which dual-use foundation models with widely  available model weights could affect market concentra tion in upstream and specialized markets necessitates fur ther examination.181  Second, open foundation models can help lower the  barrier to entry for smaller actors to enter the market for  downstream AI-powered products and services by reduc ing upfront costs that would have gone into model devel opment or costs associated with paying a developer to use  one,182 and enabling competition against entrenched in cumbents183 (who may cut off API access to start-ups that  pose a competitive threat), potentially reducing switching  costs.184 Start-ups can leverage these models in a variety  of “wrapper” systems, such as chatbots, search engines,  generative customer service tools, automated legal anal ysis, and more. Lowering the barrier to entry can allow  smaller companies and startups to compete on a more  even scale with better resourced competitors in down stream markets, thereby diversifying and decentralizing  the concentration of power.185 This benefit applies inter nationally as well—open foundation models contribute to  international development and reducing the global digital  divide, goals stated in the U.S.-led UN General Assembly  resolution, “Seizing the opportunities of safe, secure and  trustworthy artificial intelligence systems for sustainable  development.”186  Further, the diversification of the AI ecosystem through  dual-use foundation models with widely available model  weights may also allow communities to access AI systems  time, “[t]he history of traditional open-source software  provides a vision of the value that could result from the  availability of open-weights AI models—including en abling greater innovation, driving competition, improving  consumer choice, and reducing costs.”178  BENEFITS OF WIDELY AVAILABLE MODEL  WEIGHTS FOR COMPETITION, INNOVA TION, AND RESEARCH  Lower Market Barriers to Entry  Dual-use foundation models with widely available model  weights provide a building block for a variety of down stream uses and seem likely to foster greater participation  by diverse actors along the AI supply chain.  While these models still require vast resources to train  and develop, and the resources necessary to train leading  models are likely to increase,179 broadened access to mod el weights may decentralize the downstream AI applica tion market. Open models can help: (i) businesses across  a range of industries integrate AI into their services and (ii)  lower the barrier to entry for non-incumbents to innovate  downstream AI applications.  First, widely available model weights offer a significant  advantage to businesses by enabling the development  of innovative products and the customization of existing  applications. These enterprises can also augment and  fine-tune these models to fit seamlessly into their spe cific, sector-based products, enhancing the functionality  and user experience of their applications.180 A company  could leverage these models to create a bespoke internal  knowledge base, optimizing information retrieval and  decision-making processes within the organization. Orga nizations can also control their own models, which gives  Dual-Use Foundation Models with Widely Available Model Weights 32  space as a whole, and some research may require or oth erwise benefit from deeper levels of access than closed  models may offer.197, 198 Shifting away from open research  methods may also incur a cost to communities that cur rently operate on openness.199 At the same time, there  may be a risk that a reliance on open models could reduce  incentives for capital intensive research.200  The net benefit to AI R&D from widely available model  weights may be limited, however. R&D may also depend  on other factors, such as access to computational resourc es201 and to components such as training data.202 Further,  potential limitations related to the lack of user feedback  and model usage fragmentation may impact the degree  of innovation associated with dual-use foundation mod els with widely available model weights; in particular, as  some academics note, “open foundation model devel opers generally do not have access to user feedback and  interaction logs that closed model developers do for im proving models over time” and “because open foundation  models are generally more heavily customized, model us age becomes more fragmented and lessens the potential  for strong economies of scale.”203  Disrupt AI Monoculture  These models could also help disrupt potential “algorith mic monoculture” by introducing alternatives to leading  firms’ proprietary models for downstream deployers.  While, as noted above, open model weights may not im pact the very frontier of foundation model competition,  they will likely increase the amount of models available to  create downstream products. “Algorithmic monoculture”  has been described as “the notion that choices and prefer ences will become homogenous in the face of algorithmic  curation.”204 In an algorithmic monoculture, the AI ecosys tem comes to rely on one or a few foundation models for  when they would otherwise not be served by large AI com panies (e.g., because serving smaller communities may be  less economically viable or the interest too niche to legit imize financially).187 This could strengthen the national AI  workforce and foster the creation of specialized products  and services that serve these communities in particular.188  Bolster AI Research and Development  Widely available model weights allow actors without ac cess to the resources needed to train large models, such as  non-profits and academics, to contribute more effectively  to AI research and development.189 This increased access  both facilitates and diversifies AI research and develop ment, and helps ensure that development of AI systems  considers a diverse range of equities, perspectives, and  societal impacts.190, 191  A broader range of actors with varying areas of expertise  and perspectives can contribute to an existing model, col laborate, and experiment with different algorithmic solu tions and increase independent research reproduction  and validation.192 Open foundation models operate as  the foundational infrastructure to power a wide variety of  products, which allows the developers of these models to  benefit from community improvements, such as making  inference more efficient.  These models could help facilitate research and develop ment into safe, secure, and trustworthy AI (e.g., bias re search using open models, greater auditing capabilities);193  efficiency, scalability, and capability in AI (e.g., quantiza tion and memorization);194 and deployment of AI systems  into different sectors or for novel use cases.195 Models with  open weights have spurred the development of AI model  evaluation benchmarks.196 These models could also allow  for research that is generalizable to the foundation model  Dual-Use Foundation Models with Widely Available Model Weights 33  Importantly, new effects arise not from foundation mod els alone, but from where and how technology interacts  with people and systems, such as the economy or our  social relationships.209 Human creativity is a major driver  of these new effects, and openness allows more human  creativity to access dual-use foundation models. The eco nomic impacts of AI, and in particular of large foundation  models, become far more powerful when any company  can tap into them, diversifying the number of use cases of  these models.210 As is the case with any new technology,  societal risks can emerge. For example, the ability to use  dual-use foundation models to create deepfakes is prob lematic when a foreign agent can use it to disrupt Ameri can elections,211 but the risk is magnified when high school  children everywhere can make a fake video of their class mates.212 Bias encoded in foundation models becomes far  more powerful when those models are used to determine  lending,213 health care,214 and prison terms.215 AI has more  effects when it is more capable and when it interacts with  more people and systems. Technological testing and eval uation are useful for examining models based on technical  capabilities, but cannot anticipate the many ways that a  model might be used when set loose in society.  Openness tends to increase the number of new effects  and uses of technologies, including foundation models.  Thus, open foundation models will, generally speaking,  likely increase both benefits and risks posed by founda tion models. However, it is important to note that there is  significant uncertainty around the harms/benefits of any  specific use,216 and closed models carry their own unique  benefits and risks.  Though dual-use foundation models are relatively new  technologies, the challenge of adapting to unknown tech nological effects is not. As the World Wide Web became  a vast range of downstream applications and diverse use  cases; this homogeneity throughout the ecosystem could  lead to technological risks, such as black boxing, method ological uniformity, and systemic failures,205 and societal  concerns, including persistent exclusion or downgrading  of certain communities, centralized cultural power, and  further marginalization of underrepresented perspec tives.206 Algorithmic monoculture can result from mar ket concentration in a few select foundation models that  impact a range of downstream applications and users.207  Widely available model weights may mitigate algorithmic  monoculture by allowing for greater algorithmic diversity.  However, the extent of this mitigation to a monoculture  effect may itself be affected by their own number and vari ety; for example, a dual-use foundation model with widely  available model weights with sufficient adoption could it self create its own algorithmic monoculture based on the  widely adopted model.  Uncertainty in Future Risks and  Benefits  Many benefits and harms of foundation models are already  occurring. However, some of these risks are yet specula tive or unforeseen, while other risk/benefit areas can be  identified. The potential future outcomes are so uncertain  that effective, definitive, long-term AI strategy-setting is  difficult. Indeed, some effects may be considered harms  by some people and benefits by others.208 While this is true  for many of the benefits and risks discussed in this report,  this observation particularly complicates classifying un certain futures as beneficial or harmful, which can imply a  level of confidence that does not exist. popular, proponents claimed it was a place to collaboratively come together.217 Those proponents did not anticipate that this connection could, ironically, also lead to loneliness.218 Advanced AI technologies are a particularly challenging policy problem, because AI develops so quickly219 and business goals, rather than public interest, largely drive innovation.220 Future policymakers will need to monitor risks and be adaptive as technology and society changes. Methods for managing uncertain problems like advanced AI have been studied under a variety of frameworks.221 Approaches to deal with the deep uncertainty around AI specifically include broad stakeholder participation,222 explicit and repeated evaluation of values used for decision-making,223 a focus on identifying and understanding hidden or potentially emergent issues to inform policymakers,224 mapping out potential futures and scenarios,225 and setting up dynamic plans involving potential actions, evaluations, and timelines for reevaluation under changing conditions.226 Summary of Risks and Benefits Wide availability of open model weights for dual use foundation models could pose a range of marginal risks and benefits. But models are evolving too rapidly, and extrapolation based on current capabilities and limitations is too difficult, to conclude whether open foundation models, overall, pose more marginal risks than benefits (or vice versa), as well as the isolated trade-offs in specific sections. For instance, how much do open model weights lower the barrier to entry for the synthesis, dissemination, and use of CBRN material? Do open model weights propel safety research more than they introduce new misuse or control risks? Do they bolster offensive cyber attacks more than propel cyber defense research? Do they enable more discrimination in downstream systems than they promote bias research? And how do we weigh these considerations against the introduction and dissemination of CSAM/NCII content? The following policy approaches and recommendations consider these uncertain factors and outline how the U.S. government can work to assess this evolving landscape. “Wide availability of open model weights for dual use foundation models could pose a range of marginal risks and benefits. But models are evolving too rapidly, and extrapolation based on current capabilities and limitations is too difficult, to conclude whether open foundation models, overall, pose more marginal risks than benefits.” 34 Dual-Use Foundation Models with Widely Available Model Weights Policy Approaches and Recommendations 35 Dual-Use Foundation Models with Widely Available Model Weights  Dual-Use Foundation Models with Widely Available Model Weights 36  ability of model weights for specific classes of dual-use  foundation models through existing authorities or by  working to establish new authorities. Restrictions could  take a variety of forms, including prohibitions on the wide  distribution of model weights, controls on the exports of  widely available model weights, licensing requirements  for firms granted access to weights, or the limiting of ac cess to APIs or web interfaces. A structured access regime  would determine who can perform specific tasks, such  as inference, fine-tuning, and use in third-party applica tions.227 Another approach could involve mandating a  staged release, where progressively wider access is grant ed over time to certain individuals or the public as the  developer evaluates post-deployment risks and down stream effects.228 Additionally, a government agency could  require review and approval of model licenses prior to the  release of model weights or at other stages in a structured  access or staged release regime.  Pros: Proponents of restricting model weights argue that  such measures are essential for limiting nefarious actors’  ability to augment foundation models for harmful purpos es. For instance, restrictions could reduce the accessibili ty of specific models trained on biological data, possibly  creating a higher barrier to entry for the design, synthesis,  acquisition, and use of biological weapons.229 Additionally,  limiting the availability of specific advanced open-weight  models could potentially limit the ability of countries of  concern to build on these models and gain strategic AI re search advantages.230 Restricting the wide availability of  model weights could potentially limit the capabilities of  countries of concern, as well as non-state actors, from de veloping and deploying sophisticated AI systems in ways  that threaten national security and public safety.  Cons: Restrictions on the open publication of model  The U.S. government could pursue a range of approach es to governing the risks and benefits of dual-use foun dation models with widely available model weights.  This Report considers three main policy approaches:  1.  Restrict the availability of model weights for du al-use foundation models  2.  Continuously evaluate the dual-use foundation model ecosystem and build & maintain the capaci ty to effectively respond  3.  Accept or promote openness  This Report analyzes the pros and cons of these three ap proaches and ultimately concludes, as NTIA’s recommen dation, that the government should not restrict the wide  availability of model weights for dual-use foundation  models at this time. Instead, the U.S. government should  actively monitor and maintain the capacity to quickly re spond to specific risks across the foundation model eco system, by collecting evidence, evaluating that evidence,  and then acting on those evaluations. The government  should also continue to encourage innovation and leading  international coordination on open models, while pre serving the option to restrict the wide availability of cer tain classes of model weights in the future.  Policy Approaches  1.  RESTRICT THE AVAILABILITY OF MODEL  WEIGHTS FOR DUAL-USE FOUNDATION  MODELS  The U.S. government could seek to restrict the wide availweights would impede transparency into advanced AI models.231 The degree of this effect, and other negative eff ects in this section, depend on the types and magnitude of restrictions. Model weight restrictions could hinder collaborative efforts to understand and improve AI systems and slow progress in critical areas of research, including AI safety, security, and trustworthiness, such as bias mitigation and interpretability.232 Restrictions might also hamper research into foundation models, and stifle the growth of the field.233 This could force investment and talent to relocate to more permissive jurisdictions, enhance adversary and competitor capabilities, and limit U.S. and allied autonomy to control the distribution of specific model weights. Targeted restrictions on certain classes of models may impose less of these costs than broader restrictions. Restrictions that use specific benchmarks or are not carefully scoped may not address some key risks and concerns. “Restrictions on the open publication of model weights would impede transparency into advanced AI models...Model weight restrictions could hinder collaborative efforts to understand and improve AI systems and slow progress in critical areas of research, including AI safety, security, and trustworthiness, such as bias mitigation and interpretability.” For instance, AI-generated CSAM and NCII are created using models with widely available model weights that are well below the 10 billion parameter threshold of a dual-use foundation model.234 Further, if other countries with the current or future capacity to develop dual-use foundation models do not similarly restrict the wide availability of model weights, the risks will persist regardless of U.S. policy. Some commenters have argued that the sharing or open dissemination of model weights would be protected under the First Amendment, similar to protections that have been recognized by some courts for open-source software.235 2. CONTINUOUSLY EVALUATE THE DUAL-USE FOUNDATION MODEL ECOSYSTEM AND BUILD & MAINTAIN THE CAPACITY TO EFFECTIVELY RESPOND A second approach would require the U.S. government to build the capacity to continuously evaluate dual-use foundation models for evidence of unacceptable risk, and to bolster its capacity to respond to models that present such risk. The U.S. government can leverage the information and research that an open environment fosters to engage in ongoing monitoring of potential risks of dual-use foundation models. By staying up-to-date on model advancements, the U.S. government can respond to current and future risks in an agile and effective manner. Effective risk monitoring would require access to information on both open and proprietary foundation models, including dual-use foundation models and other advanced AI models, systems, and agents.236 Useful risk evaluation information could include data from foundation model developers, AI platforms, independent auditors, and other actors in the foundation model marketplace,237, 238 model evaluations and red-teaming results,239, 240 and standardized testing, evaluations, and risk benchmarks.241, 242 It could also include keeping track of key indicators in the economic and social systems that impact and interact with foundation models. 37 Dual-Use Foundation Models with Widely Available Model Weights Best practices around evaluation and transparency will change over time, as will society’s perceptions of the most pressing risks, so the U.S. government would need flexibility in future adaptations of evaluation standards and transparency requirements. Monitoring of specific risks, such as CBRN or cybersecurity risks, may require liaising between agencies with specific subject matter expertise.243 In addition, monitoring requires secure storage of the research, including for external research and internal research with proprietary data.244 The risks that arise from open and closed foundation models involve not just the technology itself, but how those models interact with social, legal, and economic systems post-deployment.245, 246 Consequently, effective monitoring and responsiveness would require combined technical, social, legal, and economic expertise. Research and evaluation methods would need to be developed, including benchmarking, evaluation of capabilities, risks, limitations, and mitigations, red-teaming standards, and methods for monitoring and responding when appropriate to the more social, long-term, and emergent risks. International cooperation would also be needed.247 As other nations develop their governance frameworks for foundation models, the U.S. could work to collaborate on interoperable standards and guidelines with like-minded partners. Pros: A monitoring approach gives time for the U.S. government to develop the staffing, knowledge, and infrastructure to respond to AI’s rapid developments.248 Monitoring allows for a more targeted approach to risk mitigation. If done well, it allows the United States to continue to benefit from the wide availability of model weights, such as through innovation and research, while protecting against both near- and long-term risks. The uses of AI will likely continue to change, as will the technology itself, and the marketplace of model developers, distribution platforms, companies using fine-tuned models, and end users.249 A monitoring approach would give time for the U.S. government to develop the staffing, knowledge, and infrastructure to respond appropriately.250 In addition, the increased AI capabilities that could come from this approach could support continued U.S. leadership on the international AI front. “Monitoring allows for a more targeted approach to risk mitigation...[Monitoring] allows the United States to continue to benefit from the wide availability of model weights, such as through innovation and research, while protecting against both near- and long-term risks.” Cons: Besides the potential risks of not restricting open model weights mentioned above, such as enabling innovation in countries of concern, one major drawback is the cost to the U.S. government. AI will impact many corners of government, so cross-sector monitoring capacity will likely require significant investment. Monitoring imposes obligations on companies, which could be costly, especially for smaller companies in the AI value chain, and burden the U.S. innovation ecosystem. Compelled disclosures to the government and public could also be intrusive and 38 Dual-Use Foundation Models with Widely Available Model Weights  Dual-Use Foundation Models with Widely Available Model Weights 39  ers of dual use foundation models. It is likely that the main  benefits of openness would arise from innovation and  research.254 Openness may provide more access for small  businesses to access foundation model resources.255 Open  resources are the norm among academic researchers,  who draw on previous work to build a collective, public  body of knowledge.256 In recent years, private companies  have overtaken academics in AI research.257, 258 Encourag ing openness could potentially reverse that trend. In addi tion, incentives for openness could support greater access  for researchers to examine models for safety, security, and  trustworthiness, including bias and interpretability.259  Cons: There are several significant drawbacks to a hands off or affirmative promotion approach. There has already  been significant involvement by both the U.S. and other  allied governments in obtaining industry commitments  and developing standards for AI risk management. Also,  as discussed, there are significant security, societal, and  strategic risks that may yet materialize from dual-use foun dation models. This option would constrain the ability of  the U.S. government to understand the developing risk  landscape or to develop mitigation measures. Incentiviz ing openness may well exacerbate many of the risks from  dual-use foundation models with widely available model  weights that have been outlined in this Report.260 For ex ample, without restrictions on sharing model weights,  dual-use foundation models that create novel biorisk or  cybersecurity threats could be used by a wide range of  actors, from foreign nations to amateur technologists. As  innovation leads to new uses, new and unexpected harms  will likely arise. Besides the negative societal effects that  these risks could create, the U.S. government may also in cur extra financial mitigation costs in areas such as cyber security defense.  would need to be carefully considered to avoid exposure  of proprietary information. If this approach is not done  well, it could be a drain on government expenditures with out substantially mitigating risks. For example, as innova tion leads to new uses, more unexpected harms will likely  arise that require a government response. The U.S. gov ernment may also incur extra financial mitigation costs in  areas such as cybersecurity defense.  3.  ACCEPT OR PROMOTE OPENNESS  The U.S. government has tended toward a laissez-faire  approach to many new technologies in order to promote  innovation and permit market forces to shape the devel opment of technology.251 On the one hand, a hands-off  approach to the wide availability of dual-use founda tion model weights can enable different competitive ap proaches to the development of foundation models252 but  would rely on industry and the research community to  develop methods for detecting and mitigating risks. Sev eral foundation model developers have already articulat ed risk detection and mitigation frameworks that could  serve as the focus for broader norm development across  the industry.253 On the other hand, the U.S. government  could further affirmatively promote the wide availability  of model weights for dual-use foundation models. Fur ther active steps could be taken, for example government  policy could be used to support open foundation models  through subsidies, procurement rules, or regulatory sup port for open models.  Pros: An approach involving minimal government action  would pose the least risk of regulatory burden on develop Dual-Use Foundation Models with Widely Available Model Weights 40  tem of dual-use foundation models with widely  available model weights, and monitoring specific  model-based and downstream indicators of risk  for potential risk cases, as well as the difference in  capabilities between open foundation models and  proprietary models;  2.  Evaluate that evidence by comparing indicators against specified thresholds, to determine when risks  are significant enough to change the federal govern ment’s approach to open-weight foundation model governance; and when appropriate,  3.  Act on those evaluations by adopting policy and reg ulatory measures targeted appropriately across the AI  value chain.  The United States government does not currently have  the capacity to monitor and effectively respond to many  of the risks arising from foundation models. A significant  component of our recommendation is to increase the  government’s capacity for evidence gathering, agile deci sion-making, and effective action.  STEP 1  Collect Evidence  We recommend that the federal government take steps  to ensure that policymakers have access to a high-quality  evidence base upon which to assess policy approaches to  dual-use foundation models with widely available model  weights going forward.261 To develop and promote that ev idence base, the federal government should:  A. Encourage, Standardize, and, if Appropriate, Compel Auditing and Transparency for Founda tion Models Recommendations  NTIA recommends that the federal government ac tively monitor and maintain the capacity to quickly  respond to specific risks across the foundation mod el ecosystem, by collecting evidence, evaluating that  evidence, and acting on those evaluations. The gov ernment should also continue encouraging innovation  and leading international coordination on topics related  to open foundation models. This recommendation pre serves the option to restrict the wide availability of certain  future classes of model weights if the U.S. government as sesses that the risks of their wide availability sufficiently  outweigh the benefits (option 1). This will allow the federal  government to build capacity to engage in effective over sight of the ecosystem and to develop a stronger evidence  base to evaluate any potential interventions in the future.  As of the time of publication of this Report, there is not  sufficient evidence on the marginal risks of dual-use foun dation models with widely available model weights to  conclude that restrictions on model weights are currently  appropriate, nor that restrictions will never be appropri ate in the future. Prohibiting the release of some or all du al-use foundation model weights now would limit the cru cial evidence-gathering necessary while also limiting the  ability of researchers, regulators, civil society, and industry  to learn more about the technology, as the balance of risks  and benefits may change over time.  Active monitoring by the federal government of the con tinued risks arising from dual-use foundation models with  widely available model weights should involve a risk-spe cific risk management approach that includes three steps:  1. Collect evidence about the capabilities, risks, and benefits of the present and future ecosys Dual-Use Foundation Models with Widely Available Model Weights 41  establish criteria to define the set of dual-use foundation  models that should undergo pre-release testing before  weights are made widely available, with the results of such  testing made publicly available to the extent possible. This  evaluation should be done with the complete spectrum of  model uses in mind, from deployment by model develop ers to distribution on platform/hosting intermediaries to  specific business uses.  B. Support and Conduct Safety, Security, and Trustworthiness Research into Foundation Mod els and High Risk Models, including Downstream Uses I. PERFORM INTERNAL GOVERNMENT RESEARCH  The U.S. government should engage in its own active re search and analysis. The government should also con tinue to build capacity for a broad array of expertise and  functions to conduct this research. Work being done by  a variety of agencies in their respective areas of subject  matter expertise could provide better insight into poten tial gaps that may need to be filled to promote an open  ecosystem while addressing risks. For example, the U.S.  Copyright Office is undergoing a comprehensive initia tive to examine copyright issues raised by AI.263 The De partment of Energy’s Frontiers in AI for Science, Security,  and Technology (FASST)264 initiative plans to leverage the  departments’ supercomputers to provide insights into  dual-use foundation models and better assess potential  risks. The outcome of initiatives such as these could create  a better sense of the state of play in different fields (e.g., for  the U.S. copyright system, a more comprehensive under standing of the interplay between the “fair use” doctrine  and the use of copyrighted works without permission  from the rights holder to train AI models). Consequently,  any research and data gathering should, where appropri It is difficult to understand the risks of dual-use foundation  models without transparency into AI model development  and deployment, including downstream uses. To the  extent reasonable, the federal government should stan dardize testing and auditing methods, which may vary  based on the capabilities, limitations, and contexts of use  of particular models and systems. The capabilities and  limitations of closed-weight foundation models are cur rently good indicators of the potential future capabilities  and limitations of open-weight models. The federal gov ernment should encourage, and where appropriate and  where authority exists, require either independent or gov ernment audits and assessments of certain closed-weight  foundation models262 – especially closed-weight models  whose capabilities exceed those of advanced dual-use  foundation models with widely available model weights  and can therefore serve as a leading indicator of the fu ture capabilities of those models. For instance, the U.S. AI  Safety Institute, housed in the National Institute for Stan dards and Technology, plans to perform pre-and post-de ployment safety tests of leading models. This work should  help the federal government understand and predict the  risks, benefits, capabilities, and limitations of dual-use  foundation models with widely available model weights.  The federal government should also aim to enable inde pendent researcher access, in addition to U.S. AI Safety  Institute access, to certain closed-weight foundation mod els, including downstream effects of AI on the information  individuals receive and how it affects their behavior. This  will help assess the risks and benefits that could arise from  future models.  As model capabilities and limitations change, so will the  appropriate testing and auditing procedures. The Unit ed States should stay actively engaged in updating those  methods and procedures. The federal government should ate, involve collaboration between relevant government agencies. Research into foundation models should not just include t echnical aspects of the models. It should also cover areas of research such as the effects of AI on human actions, privacy, legal ramifications, and downstream effects o f dual-use foundation models. This research should also address, for instance, the potential ability of these models t o increase CBRN risks, in particular, bio risks, as well as cybersecurity concerns, and risks of human deception. II. SUPPORT EXTERNAL RESEARCH The federal government should support external research on the risks and benefits related to dual-use foundation models. Research into available technical and non-technical mitigations for risks arising from dual-use foundation models with widely available model weights is also important to prioritize. This could include research into model explainability/interpretability and approachother “Research into foundation models should not just include technical aspects of the models. It should also cover areas of research such as the effects of AI on human actions, privacy, legal ramifications, and downstream effects of dual-use foundation models.” es identified by research communities. Support could take the form of direct research grants, including through the National AI Research Institutes, or it could be provided by prioritizing such research through compute resource support programs like the proposed NAIRR. C. Develop and Maintain Risk Portfolios, Indicators, and Thresholds The U.S. government should identify specific risks, and then, for each identified risk, maintain one or more risk indicators. These can be technical indicators, such as multi-modal capabilities or the ability of AI agents to manipulate the external environment, or measurements of confabulation or racial bias. They could also be societal indicators, such as the breadth of adoption of a particular AI system or the availability of certain physical materials which could be used in conjunction with AI to create a threat. One important class of metrics for open-weight foundation models is leading indicators. These are indicators of the risks, benefits, and capabilities that open-weight foundation models will – but do not currently – possess. It is important that the government maintain robust leading indicators of model capabilities, because harms from open models are difficult to undo once the weights are released. While the existing capabilities of closed-weight models are one leading indicator of the future capabilities, risks, and benefits of open-weight foundation models, they are not the only ones. Tracking the relative rate of advances between open- and closed-weight models, for example by comparing their performance on complex tasks over time, would help identify when a given open-weight model is poised to catch up to or surpass the capabilities of an existing closed-weight model. By creating these metrics, the government can better prepare for future risks and take advantage of future benefits as these technologies continue to rapidly evolve. To actively monitor the open-weight foundation model ecosystem, the federal government should maintain a portfolio of risk cases, including unlikely risks and soci42 Dual-Use Foundation Models with Widely Available Model Weights  Dual-Use Foundation Models with Widely Available Model Weights 43  STEP 2  Evaluate Evidence  Using a broad evidence base and specific risk indicators,  the federal government should assess whether the mar ginal risks from open-weight models in a particular sector  or use case warrants government action. Specifically, the  federal government should:  A.Assess the Difference in Capabilities, Limita tions, and Information Content between Closed and Open Models The government should assess and monitor the length of  the “policymaking runway”: the length of time between  when leading closed models achieve new capabilities and  when open-weight models achieve those same capabil ities, along with a wider set of indicators including per sistent limitations, and information about training data  and information content associated with open weight  models.  Once a capability appears in an open-weight model, it  may be impossible to wholly remove that capability from  the open-weight foundation model ecosystem. Therefore,  restrictions on open-weight models can be most effective  only before a particular capability is released in an open weight model. Likewise, a rich understanding of limita tions can help downstream integrators make informed  choices when selecting open models.  by (i) one or more leading indicators of risk, which can be  social and/or technological, (ii) thresholds for each indica tor, and (iii) a set of potential policy responses that could  mitigate the risk. Benefit indicators should also be taken  into account when risk-benefit calculations are important.  When the indicator(s) meet the threshold(s), the govern ment should consider intervening with one or more policy  responses. An example of this scenario is given in the Ap pendix.  The choice of thresholds and potential policy responses  should weigh current and predicted future technical ca pabilities, relevant legal considerations, and downstream  impacts.  In establishing thresholds and conducting assessments,  the government should recognize that the evidence base  for restrictions on dual-use foundation models with wide ly available model weights is evolving. The benefits that  such models produce should be fully considered in estab lishing those thresholds, as well as the legal and interna tional enforcement challenges in implementing restric tions.265 Additionally, consideration should be given to  whether each risk is better addressed with interventions  in downstream pathways through which those risks ma terialize rather than in the availability of model weights.266 otechnical risks, that might arise from future open foundation models. Each such risk should be accompanied “To actively monitor the open-weight foundation model ecosystem, the federal government should maintain a portfolio of risk cases, including unlikely risks and sociotechnical risks, that might arise from future open foundation models.” “Additionally, consideration should be given to whether each risk is better addressed with interventions in downstream pathways through which those risks materialize rather than in the availability of model weights.”  Dual-Use Foundation Models with Widely Available Model Weights 44  ble to today’s most advanced foundation models.271  Furthermore, the risks and benefits of AI arise in compli cated social and technical ways, which depend on the type  of information processed by the model and the potential  set of use cases. Evo, a state-of-the-art AI biological design  tool that can work with proteins, DNA, and RNA,272 seems  to fit most of the requirements for a dual-use foundation  model.273 However, some biological design tools current ly only involve approximately hundreds of millions of pa rameters – far less than in the dual-use foundation model  definition. Many text-to-image and text-to-video models  do not require more than 10 billion parameters.274 A giant  model is not required to make a deepfake video – it can be  done on a personal computer.275  In addition to the number of parameters, there are many  other features that make AI models potentially powerful  and which may be useful in establishing benchmarks and  definitions for monitoring and action. Policymakers and  researchers should take into consideration the following  non-exhaustive list of factors. The relative importance of  each factor will vary depending on the situation:  1. Number of parameters 2. Computing resources required to train a model 3. Training data – dataset size and quality, nature and  confidentiality of the data, difficulty of reproducing the data. 4. Model architecture and training methods 5. Versatility – the types of tasks a model can perform  6. Potential risks – explicitly identified use cases that create specific harms 7. Access and adoption – the number of people, or Many factors may affect the policymaking runway, and its  length will affect the speed with which policymakers will  need to respond to changes in capabilities, limitations,  and information content of open models available in the  open model ecosystem.  B.Develop benchmarks and definitions for mon itoring and action. There are a range of factors that should be considered  when developing monitoring benchmarks and definitions,  not only those listed in the EO definition of dual-use foun dation models. Numerical measures such as the number  of floating-point operations used in training provide rough  estimates of model capabilities, and can be used as a first  step to distinguish models that deserve further scrutiny.  But to properly calibrate monitoring and policy inter ventions to the appropriate models, the US government  should developing benchmarks and definitions for model  capabilities that incorporate other factors as well. One rea son for this is that, while numerical measures like the num ber of parameters/weights or floating point operations per  second (FLOPS) are often related to a model’s technical  capabilities,267 advances in algorithms, architectures, pro cessors, and the complexities posed by multi-modal mod els may gradually cause any numerical metric to become  outdated. For instance, the Executive Order refers to “tens  of billions of parameters” in the definition of dual-use  foundation model. However, Meta’s Llama 3 8B, which did  not exist at the time the Executive Order was written and  does not have enough parameters to meet this definition,  outperforms LLama 2 70B,268 which does meet the defini tion, on a number of benchmarks.269 With computing ca pabilities increasing exponentially over time,270 it is quite  possible that personal computers will someday be able to  train highly capable and generalizable models compara Dual-Use Foundation Models with Widely Available Model Weights 45  STEP 3  Act on Evaluations  Given the varied nature of risks that foundation models can  and will pose, the government should maintain the ability  to undertake interventions, which should be considered  once the risk thresholds described above are crossed such  that the marginal risks substantially outweigh the margin al benefit. These interventions include restrictions on ac cess to models (including model weights) and other risk  mitigation measures as appropriate to the specific con text when restrictions on widely available model weights  are not justified or legally permissible.  A.Model Access Restrictions One broad category of such interventions involves restrict ing access to, or requiring pre-release model licensing for,  certain classes of dual-use foundation models or systems.  At one end of this category is complete restriction of a  model from being publicly distributed, including model  weights and API access. A less extreme step would involve  restricting the open sharing of model weights and allow ing public access only to hosted models. These restrictions  would impose substantial burdens on the open-weight  model ecosystem and should require significant evidence  of risk. There are many different ways to implement a  structured access program that restricts access to model  weights,281 where government could set guidelines “for  what capabilities should be made available, in what form,  and to whom.”282 The government could also mandate that  intermediary AI platforms ensure that restricted weights  are not available on their platforms or are only available  in select instances. These restrictions could potentially be  effectuated through existing statutory authorities (such as  ganizations, and systems that use or are affected by  the model  8. Emergence – the ability of a model to perform tasks  that it was not explicitly trained for 9. Evaluated capabilities – performance on partic ular tasks, including non-technical tasks such as AI-human interactions276 10. Information modalities – the types of information the model can process, such as image, text, genet ic data277, biometric data278, real-world sensing279 or combinations of multiple types. C.Maintain and Bolster Cross-disciplinary Fed eral Government Capacity to Evaluate Evidence Effective monitoring, assessment, and decision-making  will require cross-disciplinary expertise and resources.  The U.S. government should encourage and hire this type  of talent. Technical specialists and access to AI models will  be necessary to stay current on model capabilities. But  social scientists will also be necessary to understand the  economic and social effects of dual-use foundation mod els with widely available model weights. Legal experts, in cluding privacy, First Amendment, copyright, foreign pol icy, as well as human and civil rights scholars, should be  consulted on the legal and constitutional implications of  intervening or failing to intervene. Domestic and interna tional policy analysts will help navigate the complexities of  government decision-making. The government has made  significant strides in increasing the Federal AI workforce  through the AI Talent Surge launched by EO 14110. The  United States should continue that trend by hiring top tal ent across the fields that foster AI-related skills.280 Particu lar care should be taken to maintain effective cross-agency  collaboration because the impacts of dual-use foundation  models do not fit neatly in any one category. the Export Administration Act) or through Congressional action, though this Report does not consider questions of legal authority in detail. Any consideration of the appropriate scope or nature of these restrictions would require legal and constitutional analysis.283 Intellectual property considerations, which are not the principal focus of this Report, would also inform the question of whether, and how far, to restrict. Importantly, the effects of AI and potential causes of AI risk are not bound to any single country, and the effectiveness of restrictions on the distribution of model weights depends in significant part on international alignment on “Given the varied nature of risks that foundation models can and will pose, the government should maintain the ability to undertake interventions, which should be considered once the risk thresholds described above are crossed such that the marginal risks substantially outweigh the marginal bene it.” the appropriate scope and nature of those restrictions. The federal government should prioritize international collaboration and engagement on its policy concerning the governance of dual-use foundation-models with widely available model weights. The United States should also retain the ability to promote certain types of openness in situations that have the potential to pose risk, but for which there is not enough information. This could include structured access for researchers,284 further information gathering on the part of the U.S. government, or funding for specific risk research. B. Other Risk Mitigation Measures Because the risks and benefits of dual-use foundation models are not solely derived from the model itself, appropriate policy measures may not concern the model weights specifically, depending on the nature of the risks and benefits.285 The government should maintain the ability to respond with a wide range of risk mitigations in accordance with its legal authority. The foundation model ecosystem has many components, and in many cases the most effective risk reduction will happen downstream of the model weights. It is important to note that several enforcement agencies have indicated that their authorities apply to the latest developments in AI technology, for example to address discrimination and bias.286 Whether and how regulations apply throughout the AI stack is beyond the scope of this Report, but it is an area worth exploring. These mitigations will likely depend on the specific risk. For example, in cases where dual-use foundation models with widely available model weights enable creation of dangerous physical objects, restrictions on physical materials may be warranted. Firm data privacy protections should be developed and adapted as foundation models continue to interact with, and draw data from, progressively larger data sets, processed at higher velocities, that touch on more parts of Americans’ lives. Other mitigation measures might include better content moderation on online platforms to limit illegal or abusive generated content, improved spear-phishing filters for emails, user interface designs to highlight possible misinformation and limited accessibility to CBRN datasets. Effective mitigations could also include making potentially impacted systems more robust and resilient to harmful effects of AI. This could include minimizing the reach of disinformation campaigns, and providing sup46 Dual-Use Foundation Models with Widely Available Model Weights port resources for human victims of AI-generated harms. Ultimately, a combination of education, experience, research, and proactive efforts by model creators will likely be necessary to help mitigate a broad array of risks. ADDITIONAL GOVERNMENT ACTION While actively monitoring risks, the government should also support openness in ways that enhance its benefits. This should include incentivizing social, technical, economic, and policy research on how to ensure open foundation models promote human well-being. Government agencies may be able to use their authorities or subject matter expertise to promote an open ecosystem while addressing risks. Fiscal policy could also be used to support open foundation models, for instance through subsidies for open models. One promising subsidy-based approach is the NAIRR, which has embedded open source and open science principles into its workplan. The U.S. government should also continue leading international diplomacy and norm-setting efforts around open foundation models. This should include engagement with a broad spectrum of international partners and fora to ensure the benefits of open artificial intelligence are shared, while limiting the ability of bad actors to cause harm. The U.S. government should also work with its allies to ensure that the uses of open-weight foundation models support the principles of democratic representation and freedom, rather than autocracy and oppression. Conclusion The current evidence base of the marginal risks and benefits of open-weight foundation models is not sufficient either to definitively conclude that restrictions on such open-weight models are warranted, or that restrictions will never be appropriate in the future. Accordingly, we recommend a three-part framework for the federal government to actively monitor the evidence base for the risks and benefits of dual-use foundation models with widely available model weights: collecting evidence about their capabilities, limitations, and information content, evaluating that evidence against thresholds of concern, and potentially acting upon those evaluations through appropriate policy measures. The government should also incentivize global and domestic research and innovation that harnesses the many benefits of open foundation models. 47 Dual-Use Foundation Models with Widely Available Model Weights Appendix: Monitoring Template 48 Dual-Use Foundation Models with Widely Available Model Weights  Dual-Use Foundation Models with Widely Available Model Weights 49  Risk: Foundation models increase the number of peo ple with the potential capability to create a weapon  and decrease team sizes and coordination costs re quired, thus increasing the chance that a domestic  malicious actor creates and uses one.  In this risk scenario the availability of foundation models  increases access for wider portions of the population, per haps through the use of an LLM that can walk an individual  through the steps required to create a weapon. This risk  is distinct from the risk posed by scientifically sophisticat ed actors creating new weapons with increased potency.  The discovery of a new weapon could also involve a model  specifically developed to handle specialized knowledge  (such as a biological design tool), which requires special ized expertise to use.  Collecting Evidence:  To create a weapon, an individual may need both spe cialized knowledge and appropriate materials. As model  capabilities change, evaluators would need to gather and  maintain information about the changing knowledge and  material needs of actors seeking to create specific cate gories of weapons, which would require expertise in both  science and machine learning. Evaluators may need to  keep multiple risk profiles for different risks. Specific risk  indicators might include, along with progressively less re strictive values of those indicators:  This template is meant to show how the decision-making process might work,  rather than suggest specific mitigation strategies and thresholds. Actual risk cases  should be maintained by subject-matter experts who can collectively understand,  monitor, and evaluate all details of a particular scenario. Notably, multiple  government agencies with specific domains are monitoring AI-related risks using  their own techniques and should be deferred to in those areas.  1. What level of specialized knowledge is required to use the foundation model to create the de sired weapon? a.Specialized doctoral degree or higher b. Specialized master’s degree c.Specialized bachelor’s degree or hobbyist “home scientists” d.Average adult 2.  Where can an individual get the materials to make  the desired weapon?  a.Specialty supplier, heavy regulation such as licensed sellers and buyers b. Specialty supplier, light regulation such as pur chase tracking c.Specialty supplier, no legal restrictions but typi cally has administrative barriers d.Local store or Internet search To gather this information, evaluators could begin by  red-teaming open and closed cutting-edge models. Sub ject-matter experts would consider the additional assis tance that the model provides in creation of a weapon.  They would also consider the equipment required, includ ing whether the model finds methods to use more easily  available materials than might be purchased through a  laboratory supplier. Other methods might be used, as de termined by the subject-matter experts.  Dual-Use Foundation Models with Widely Available Model Weights 50  Evaluating Evidence and Acting on Evaluations:  The grid below shows a set of potential mitigation options which are dependent on the risk indicators. The government  agency responsible for managing the risk scenario would choose from potential mitigation options, which could involve  technical restrictions or a variety of other non-model-oriented actions designed to reduce risk. Developing such a ma trix would require an understanding of different legal and regulatory authorities and may involve collaboration between  agencies. In the example decision matrix below, the value in each entry shows possible mitigation options, which the  agency may or may not decide to recommend.  Mitigation Options:  0. Do nothing 1. Restrict open model weights & access to closed models, for specific classes of models 2. Restrict access to specific materials 3. Security controls on API-based fine-tuning of closed models using specific types of data (biological, chemical, etc.) Example: If an individual with a specialized master’s degree can use an LLM to make a weapon with materials from a spe cialty supplier with no legal burden, then the government should consider either (1) restricting access to specific classes  of models/weights or (2) restricting access to specific materials.  Who is enabled by AI to create a weapon?  Where can an  individual get  the materials to  make a weapon?  Average  person  Specialized    bachelor’s degree /  hobbyist  Specialized  master’s degree  Specialized  doctoral degree  Local store/ Internet  search  1 or 2 or 3  1 or 2 or 3  1 or 2 or 3  2 or 3  Specialty supplier,  no legal restrictions  1 or 2 or 3  1 or 2 or 3  1 or 2  2  Specialty supplier,  light legal burden  1 or 2 or 3  1 or 2 or 3  1 or 2  0  Specialty supplier,  heavily regulated  1  1  0  0 Endnotes 1\t2\t3\t4\t5\t6\t7\t8\t9\tExec.\tOrder\tNo.\t14,110\t(2023).\thttps://www.whitehouse.gov/briefing-room/presidential-actions/2023/10/30/executive-order-on-the-safe-secure-andtrustworthy-development-and-use-of-artificial-intelligence/. We\tdefine\tlimited\taccess\tas\tAI\tmodels\tthat\tdo\tnot\tgive\taccess\tto\tmodel\tweights,\tsource\tcode,\tor\ttraining\tdata. All\tmentions\tof\tspecific\tcompanies\tor\tservices\tin\tthis\treport\tare\treferential\tand\tnot\tintended\tto\timply\tnormativity,\teither\tpositive\tor\tnegative,\tregarding\tthe\tcompany\tor\tservice. Dual\tUse\tFoundation\tArtificial\tIntelligence\tModels\twith\tWidely\tAvailable\tModel\tWeights,\t89\tFed.\tReg.\t14059\tPub\t(Feb.\t26,\t2024). Exec.\tOrder\tNo.\t14,110\t(2023).\thttps://www.whitehouse.gov/briefing-room/presidential-actions/2023/10/30/executive-order-on-the-safe-secure-andtrustworthy-development-and-use-of-artificial-intelligence/. Exec.\tOrder\tNo.\t14,110\t(2023).\thttps://www.whitehouse.gov/briefing-room/presidential-actions/2023/10/30/executive-order-on-the-safe-secure-andtrustworthy-development-and-use-of-artificial-intelligence/. This\tprovision\tof\tExecutive\tOrder\t14110\trefers\tto\tthe\tas-yet\tspeculative\trisk\tthat\tAI\tsystems\twill\tevade\thuman\tcontrol,\tfor\tinstance\tthrough\tdeception,\tobfuscation,\tor\tself-replication.\tHarms\tfrom\tloss\tof\tcontrol\twould\tlikely\trequire\tAI\tsystems\twhich\thave\tcapabilities\tbeyond\tthose\tknown\tin\tcurrent\tsystems\tand\thave\taccess\tto\ta\tbroader\trange\tof\tpermissions\tand\tresources\tthan\tcurrent\tAI\tsystems\tare\tgiven.\tHowever,\topen\tmodels\tintroduce\tunique\tconsiderations\tin\tthese\trisk\tcalculations,\tas\tactors\tcan\tremove\tsuperficial\tsafeguards\tthat\tprevent\tmodel\tmisuse.\tThey\tcan\talso\tcustomize,\texperiment\twith,\tand\tdeploy\tmodels\twith\tmore\tpermissions\tand\tin\tdifferent\tcontexts\tthan\tthe\tdeveloper\toriginally\tintended.\tCurrently,\tAI\tagents\twho\tcan\tinteract\twith\tthe\tworld\tlack\tcapabilities\tto\tindependently\tperform\tcomplex\tor\topen-ended\ttasks,\twhich\tlimits\ttheir\tpotential\tto\tcreate\tloss\tof\tcontrol\tharms.\tHague,\tD.\t(2024).\tMultimodality,\tTool\tUse,\tand\tAutonomous\tAgents:\tLarge\tLanguage\tModels\tExplained,\tPart\t3.\tCenter\tfor\tSecurity\tand\tEmerging\tTechnology.\thttps://cset.georgetown.edu/article/multimodality-tool-use-and-autonomous-agents/\t(“While\tLLM\tagents\thave\tbeen\tsuccessful\tin\tplaying\tMinecraft\tand\tinteracting\tin\tvirtual\tworlds,\tthey\thave\tlargely\tnot\tbeen\treliable\tenough\tto\tdeploy\tin\treal-life\tuse\tcases.\t.\t.\tToday,\tresearch\toften\tfocuses\ton\tgetting\tautonomous\tLLMs\tto\tperform\tspecific,\tdefined\ttasks\tlike\tbooking\tflights.”).\tDeveloping\tcapable\tAI\tagents\tremains\tan\tactive\tresearch\tgoal\tin\tthe\tAI\tcommunity.\tXi,\tZ.,\tet\tal.\t(2023).\tThe\tRise\tand\tPotential\tof\tLarge\tLanguage\tModel\tBased\tAgents:\tA\tSurvey.\tArXiv.org.\thttps://doi.org/10.48550/ arXiv.2309.07864.\tHowever,\tgiven\tthe\tnascent\tstage\tof\tthese\tefforts,\tthis\tReport\tcannot\tyet\tmeaningfully\tdiscuss\tthese\trisks\tin\tgreater\tdepth.\tExec.\tOrder\tNo.\t14,110\t(2023).\thttps://www.whitehouse.gov/briefing-room/presidential-actions/2023/10/30/executive-order-on-the-safe-secure-andtrustworthy-development-and-use-of-artificial-intelligence/. Bommasani,\tR.,\tHudson,\tD.A.,\tAdeli,\tE.\tet\tal\t(2021).\tOn\tthe\topportunities\tand\trisks\tof\tfoundation\tmodels.\tArXiv\tpreprint.\thttps://arxiv.org/abs/2108.07258. 10\tExec.\tOrder\tNo.\t14,110\t(2023).\thttps://www.whitehouse.gov/briefing-room/presidential-actions/2023/10/30/executive-order-on-the-safe-secure-andtrustworthy-development-and-use-of-artificial-intelligence/. 11\tThe\tReport\tdefines\tAI\tmodels\tas\t“a\tcomponent\tof\tan\tinformation\tsystem\tthat\timplements\tAI\ttechnology\tand\tuses\tcomputational,\tstatistical,\tor\tmachinelearning\ttechniques\tto\tproduce\toutputs\tfrom\ta\tgiven\tset\tof\tinputs,”\tas\tdefined\tin\tthe\t“Safe,\tSecure,\tand\tTrustworthy\tDevelopment\tand\tUse\tof\tArtificial\tIntelligence”\tExecutive\tOrder.\t(Exec.\tOrder\tNo.\t14,110\t(2023).\thttps://www.whitehouse.gov/briefing-room/presidential-actions/2023/10/30/executiveorder-on-the-safe-secure-and-trustworthy-development-and-use-of-artificial-intelligence/). 12\tYuksel,\tS.,\tet\tal.,\t(2012).\tTwenty\tYears\tof\tMixture\tof\tExperts.\tIEEE\tTransactions\ton\tNeural\tNetworks\tand\tLearning\tSystems,\t23(8),\t1177–1193;\tVaswani,\tA.\tet\tal.,\t(2017).\tAttention\tis\tAll\tYou\tNeed.\thttps://arxiv.org/abs/1706.03762\t;\tWhat\tIs\tRLHF?\t(n.d.).\taws.amazon.com.\thttps://aws.amazon.com/what-is/ reinforcement-learning-from-human-feedback/\t;\tWhang,\tO.\t(2024,\tApril\t30).\tFrom\tBaby\tTalk\tto\tBaby\tA.I.\tNYTimes.\thttps://www.nytimes.com/2024/04/30/ science/ai-infants-language-learning.html. 13\tLin,\tY.-T.,\t&\tChen,\tY.-N.\t(2023).\tTaiwan\tLLM:\tBridging\tthe\tLinguistic\tDivide\twith\ta\tCulturally\tAligned\tLanguage\tModel.\tArXiv\t(Cornell\tUniversity).\thttps://doi. org/10.48550/arxiv.2311.17487. 14\tRepresenting\tweights\twith\tlower-precision\tnumbers.\tSee,\te.g.,\tHugging\tFace.\tQuantization.\thttps://huggingface.co/docs/optimum/en/concept_guides/ quantization. 15\tVarious\tmethods\tthat\tend\tup\tremoving\tparameters\tfrom\tan\tAI\tmodel.\tSee,\te.g.,\tPruning\tTutorial.\tPyTorch.\thttps://pytorch.org/tutorials/intermediate/ pruning_tutorial.html. 16\tCriddle,\tC.\t&\tMadhumita\tM.\t(2024,\tMay\t8).\tArtificial\tintelligence\tcompanies\tseek\tbig\tprofits\tfrom\t‘small’\tlanguage\tmodels.\tFinancial\tTimes.\thttps://www. f t.com/content/359a5a31-1ab9-41ea-83aa-5b27d9b24ef9. 17\tA\tCNAS\treport\tfound\tthat,\tif\ttrends\tcontinue,\tfrontier\tAI\ttraining\tcould\trequire\t1,000\ttimes\tmore\tcompute\tpower\tthan\tGPT-4\tby\tthe\tlate\t2020s/early\t2030s,\tand\ttraining\tcosts\tfor\tleading\tmodels\tdouble\tapproximately\tevery\t10\tmonths.\tHowever,\tnote\tthat\tthe\tamount\tof\tcompute\tpower\tan\tactor\tsaves\tdepends\ton\tthe\tamount\ton\tinference\tthey\tneed\tto\tperform\ton\tthe\tmodel.\tFuture-Proofing\tFrontier\tAI\tRegulation.\t(2024,\tMarch\t13).\thttps://www.cnas.org/ publications/reports/future-proofing-frontier-ai-regulation. 51 Dual-Use Foundation Models with Widely Available Model Weights18\tSee\te.g.,Vincent,\tJ.\t(2023,\tMarch\t8).\tMeta’s\tpowerful\tAI\tlanguage\tmodel\thas\tleaked\tonline\t—\twhat\thappens\tnow?\tThe\tVerge.\thttps://www.theverge. com/2023/3/8/23629362/meta-ai-language-model-llama-leak-online-misuse\t;\tHubinger,\tE.,\tDenison,\tet\tal.\t(2024,\tJanuary\t17).\tSleeper\tAgents:\tTraining\tDeceptive\tLLMs\tthat\tPersist\tThrough\tSafety\tTraining.\tArXiv.\thttps://doi.org/10.48550/arXiv.2401.05566. 19\tZhan,\tQ.\tet\tal.,\t(2023).\tRemoving\tRLHF\tProtections\tin\tGPT-4\tvia\tFine-Tuning.\tUIUC,\tStanford.\thttps://openreview.net/pdf?id=NyGm96pC3n. 20\tSee,\te.g.,\tPartnership\ton\tAI\tComment\tat\t6,\t(“Some\tof\tthe\tfeatures\tof\topen\tmodels\tthat\tmay\tbe\trelevant\tto\tassessing\tdifferential\trisk\tinclude\tthat\topen\trelease\tof\tmodel\tweights\tis\tirreversible,\tand\tthat\tmoderation/monitoring\tof\topen\tmodels\tpost-release\tis\tchallenging.”). 21\tSee,\te.g.,\tHugging\tFace\tComment\tat\t3\t(“Model\tweights\tcan\tbe\tshared\tindividually\tbetween\tparties,\ton\tplatforms\twith\tor\twithout\tdocumentation\tand\twith\tor\twithout\taccess\tmanagement,\tand\tvia\tp2p/torrent.”).\t22\tSee\tGoldman,\tS.\t(2023,\tDecember\t8).\tMistral\tAI\tbucks\trelease\ttrend\tby\tdropping\ttorrent\tlink\tto\tnew\topen\tsource\tLLM.\tVentureBeat.\thttps://venturebeat. com/ai/mistral-ai-bucks-release-trend-by-dropping-torrent-link-to-new-open-source-llm/;\tColdewey,\tD.\t(2023,\tSeptember\t27).\tMistral\tAI\tmakes\tits\tfirst\tlarge\tlanguage\tmodel\tfree\tfor\teveryone.\tTechCrunch.\thttps://techcrunch.com/2023/09/27/mistral-ai-makes-its-first-large-language-model-free-foreveryone/.\tHowever,\tnote\tthat\tnot\tall\tAI\tmodels\treceive\tsuch\tattention\twhen\treleased.\tSee\tGitHub\tComment\tat\t3\t(“Wide\tavailability\tof\tmodel\tweights\tis\ta\tfunction\tof\tdiscovery,\tgoverned\tby\tonline\tplatforms.\tEven\tfor\tcontent\tposted\tpublicly\ton\tthe\tinternet,\tthe\tdefault\tstate\tis\tobscurity.\tWhether\tcontent\tis\twidely\tavailable\twill\tdepend\ton\tecosystem\tactivity,\tdistribution\tchannels,\tand,\tparticularly,\tsharing\ton\tplatforms\tthat\tenable\tvirality.\tEcosystem\tmonitoring\tand\tgovernance\tcan\thelp\tinform\tand\timplement\trisk-based\tmitigations\tfor\twidely\tavailable\tmodel\tweights.”). 23\tSee,\te.g.,\tHolistic\tAI\tComment\tat\t6\t(“The\tconversation\taround\topen\tand\tclosed\tfoundation\tmodels\tis\tnot\ta\tdichotomy\tbetween\tthe\ttwo,\tbut\trather\ta\tspectrum\talong\ta\tgradient\tof\ttheir\trelease.”);\tFuture\tof\tLife\tInstitute\tComment\tat\t7\t(“AI\tsystems\tclaimed\tto\tbe\t‘open’\tlie\tacross\ta\tspectrum\tof\taccess,\teach\tcarrying\tdifferent\tlevels\tof\tbenefits\tand\trisks.”)\t(quotation\tmarks\tin\toriginal);\tOTI\tComment\tat\t4\t(“There\tis\tno\teasy\tbinary\tthat\topposes\t‘open’\tand\t‘closed’\tAI\tmodels.\tCommentary\tand\tresearch\tthat\tsuggest\totherwise\tunhelpfully\tdistort\tthe\treality—which\tAI\ttechnical\tand\tgovernance\texperts\thave\trepeatedly\texplained—that\tAI\tmodels\tsit\tsomewhere\ton\ta\tspectrum\tor\t‘gradient’\tof\topenness.”)\t(internal\tcitation\tomitted)\t(quotation\tmark\tin\toriginal);\tSIIA\tComment\tat\t1\t(“First,\topenness\tshould\tbe\tviewed\tacross\ta\tgradient,\twith\tmodel\tweights\tas\tone\tcomponent\tof\tan\tAI\tsystem\tthat\tcan\tbe\tmade\tavailable\tto\tthird\tparties\tin\tvarying\tdegrees.”);\tIntel\tComments\tat\t4\t(“Release\tof\tfoundation\tmodels\tpresent\ta\tgradient\tof\topenness\toptions.”)\t(internal\tcitation\tomitted);\tCCIA\tComment\tat\t2\t(“[T]here\tis\ta\tbroad\tspectrum\tbased\ton\thow\tmuch\tof\tthe\tsystem\tis\topen\tand\thow\tthat\tsystem\tis\tmanaged.”);\tGoogle\tComment\tat\t4\t(“Assuming\t‘openness’\tin\tAI\tas\ta\tbinary\tchoice\tbetween\topen\tand\tclosed\tapproaches\tfails\tto\tcapture\timportant\tnuances.\tRather,\tit\tis\timportant\tto\tthink\tabout\t‘open’\tand\t‘widely\tavailable’\tas\texisting\ton\ta\tgradient\tof\taccess,\twhich\toffers\ta\tbetter\tconceptual\tframe.”)\t(internal\tfootnote\tomitted)\t(quotation\tmarks\tand\titalics\tin\toriginal);\tMichael\tWeinberg\tComment\tat\t4\t(“The\tdefinition\tof\t‘open’\tin\tthe\tcontext\tof\tfoundational\tmodels\tcontinues\tto\tevolve,\twith\tmany\tdifferent\tapproaches\tmaking\ta\treasonable\tclaim\tto\tbeing\topen\tdepending\ton\tthe\tcontext\tand\tintended\tuse.”)\t(internal\tcitation\tomitted)\t(quotation\tmarks\tin\toriginal).\tSee\talso\tgenerally\tSolaiman,\tI.\t(2023).\tThe\tGradient\tof\tGenerative\tAI\tRelease:\tMethods\tand\tConsiderations.\tHugging\tFace.\thttps://arxiv. org/pdf/2302.04844\t(discussing\tgradients\tof\tAI\tsystem\trelease);\tBommasani,\tR.,\tet\tal.,\t(2023).\tConsiderations\tfor\tGoverning\tOpen\tFoundation\tModels.\tStanford\tUniversity\tHuman-Centered\tArtificial\tIntelligence.\thttps://hai.stanford.edu/sites/default/files/2023-12/Governing-Open-Foundation-Models.pdf\t(similar). 24\tShevlane,\tT.\t(2022).\tStructured\tAccess:\tAn\tEmerging\tParadigm\tfor\tSafe\tAI\tDeployment.\tUniversity\tof\tOxford.\thttps://arxiv.org/abs/2201.05159. 25\tSolaiman,\tI.\t(2023).\tThe\tGradient\tof\tGenerative\tAI\tRelease:\tMethods\tand\tConsiderations.\tHugging\tFace.\thttps://arxiv.org/pdf/2302.04844. 26\tContractor,\tD.,\tet\tal.\t(2022,\tJune).\tBehavioral\tuse\tlicensing\tfor\tresponsible\tAI.\tIn\tProceedings\tof\tthe\t2022\tACM\tConference\ton\tFairness,\tAccountability,\tand\tTransparency\t(pp.\t778-788). 27\tMitchell,\tM.,\tet\tal.\t(2019,\tJanuary).\tModel\tcards\tfor\tmodel\treporting.\tIn\tProceedings\tof\tthe\tconference\ton\tfairness,\taccountability,\tand\ttransparency\t(pp.\t220-229). 28\tSee,\te.g.,\tAI\tAccountability\tPolicy\tReport,\tNational\tTelecommunications\tand\tInformation\tAdministration.\t(2024,\tMarch).\thttps://www.ntia.gov/sites/ default/files/2024-04/ntia-ai-report-print.pdf\tat\t28\t(noting\tthat\tdatasheets\t“provide\tsalient\tinformation\tabout\tthe\tdata\ton\twhich\tthe\tAI\tmodel\twas\ttrained,\tincluding\tthe\t‘motivation,\tcomposition,\tcollection\tprocess,\t[and]\trecommended\tuses’\tof\tthe\tdataset”). 29\tExec.\tOrder\tNo.\t14,110\t(2023).\thttps://www.whitehouse.gov/briefing-room/presidential-actions/2023/10/30/executive-order-on-the-safe-secure-andtrustworthy-development-and-use-of-artificial-intelligence/. 30\tSee,\te.g.,\tAI\tPolicy\tand\tGovernance\tWorking\tGroup\tComment\tat\t2\t(“The\tfederal\tgovernment\tshould\tprioritize\tunderstanding\tof\tmarginal\trisk.\tThe\trisks\tof\topen\tfoundation\tmodels\tdo\tnot\texist\tin\ta\tvacuum.\tTo\tproperly\tassess\tthe\trisks\tof\topen\tfoundation\tmodels,\tand\twhether\tregulations\tshould\tsingle\tout\topen\tfoundation\tmodels,\tthe\tfederal\tgovernment\tshould\tdirectly\tcompare\tthe\trisk\tprofile\tto\tthose\tof\tclosed\tfoundation\tmodels\tand\texisting\ttechnologies.\tIn\tits\treport,\tthe\tNTIA\tshould\tforeground\tthe\tmarginal\trisk\tof\topen\tfoundation\tmodels\tby\tdirecting\tgovernment\tagencies\tto\tconduct\tmarginal\trisk\tassessments,\tfund\tmarginal\trisk\tassessment\tresearch,\tand\tincorporate\tmarginal\trisk\tassessment\tinto\tprocurement\tprocesses.”);\tRishi\tBommasani\tet\tal.\tat\t1\t(“Foundation\tmodels\tpresent\ttremendous\tbenefits\tand\trisks\tto\tsociety\tas\tcentral\tartifacts\tin\tthe\tAI\tecosystem.\tIn\taddressing\tdual\tuse\tfoundation\tmodels\twith\twidely\tavailable\tweights,\tthe\tNational\tTelecommunications\tand\tInformation\tAdministration\t(NTIA)\tshould\tconsider\tthe\tmarginal\trisk\tof\topen\tfoundation\tmodels,\tdefined\tas\tthe\textent\tto\twhich\tthey\tincrease\trisk\trelative\tto\tclosed\tfoundation\tmodels\tor\tpreexisting\ttechnologies\tlike\tsearch\tengines.”)\t(internal\tfootnote\tomitted);\tCTA\tComment\tat\t6\t(“NTIA’s\tconsideration\tof\trisks\tassociated\twith\topen\tweight\tmodels\tshould\tfocus\ton\tmarginal\t52 Dual-Use Foundation Models with Widely Available Model Weightsrisks\tarising\tfrom\tsuch\tmodels.”);\tPublic\tKnowledge\tComment\tat\t3\t(“The\tconversation\taround\topen\tfoundation\tmodels\tis\tsignificantly\tenriched\tby\ta\tnuanced\tunderstanding\tof\tthe\tmarginal\trisks\tthey\tpose\tcompared\tto\ttheir\tclosed\tcounterparts\tand\texisting\ttechnologies.”);\tOTI\tComment\tat\t17\t(“NTIA\tand\tother\tU.S.\tgovernment\tagencies\tmust\tfocus\tvague\tdiscussions\tabout\tthe\trisks\tof\topen\tAI\tmodels\ton\tthe\tstudy\tand\tprecise\tarticulation\tof\tthe\tmarginal\trisk\tthese\tmodels\tpose.”);\tHolistic\tAI\tComment\tat\t10\t(“To\teffectively\tinterrogate\tand\tembed\tthese\tconsiderations,\tit\tis\tcrucial\tfor\tpolicy\tand\tgovernance\tdiscourses\ton\tresponsible\tmodel\trelease\tto\tbe\tanchored\taround\tthe\tconcept\tof\tthe\tmarginal\trisk\tposed\tby\topen\tfoundation\tmodels”)\t(internal\thyperlink\tomitted);\tCDT\tComment\tat\t14\t(“In\tevaluating\tthe\trisks\tof\t[open\tfoundation\tmodels],\twe\tmust\tconsider\tthem\tin\tcomparison\tto\tthe\texisting\trisks\tenabled\tby\tclosed\tmodels,\tby\taccess\tto\texisting\ttechnologies\tsuch\tas\tthe\tinternet,\tand\tby\tsmaller\tmodels\tthat\tcarry\tsimilar\trisks\tbut\tfor\twhich\tcontrolling\tproliferation\twould\tbe\tmuch\tharder\tif\tnot\timpossible.\tIn\tother\twords,\twe\tmust\tconsider\tthe\tmarginal\trisk\tof\t[open\tfoundation\tmodels].”)\t(italics\tin\toriginal)\t(internal\tcitation\tomitted);\tMozilla\tComment\tat\t13\t(“Debates\taround\tsafety\tand\t‘open\tsource’\tAI\tshould\tcenter\tmarginal\trisk[.]”)\t(quotation\tmarks\tin\toriginal);\tMicrosoft\tComment\tat\t1-2\t(“We\trecommend\t[.\t.\t.]\t[p]romoting\trisk\tand\timpact\tassessments\tthat\tare\tgrounded\tin\tthe\tspecific\tattributes\tof\twidely\tavailable\tmodel\tweights\tthat\tpresent\trisk,\tthe\tmarginal\trisk\tof\tsuch\tavailability\tcompared\tto\texisting\tsystems[.]\t[.\t.\t.]”);\tPAI\tComment\tat\t6\t(“In\tassessing\tthe\trisk\tposed\tby\topen\tfoundation\tmodels,\tand\tappropriate\tmeasures\tto\taddress\tthose\trisks,\tpolicy\tmakers\tshould\tfocus\ton\tthe\tmarginal\trisks\tassociated\twith\topen\taccess\trelease.”)\t(internal\thyperlink\tomitted);\tDatabricks\tComment\tat\t2\t(“The\tbenefits\tof\topen\tmodels\tsubstantially\toutweigh\tthe\tmarginal\trisks,\tso\topen\tweights\tshould\tbe\tallowed,\teven\tat\tthe\tfrontier\tlevel[.]”);\tMeta\tComment\tat\t16\t(“In\torder\tto\tprecisely\tidentify\tand\tassess\trisks\tuniquely\tpresented\tby\topen\tfoundation\tmodels,\tit\tis\timportant\tto\tapply\ta\t‘marginal\trisk\tanalysis’\tthat\ttakes\taccount\tof\tthe\trisks\tof\topen\tmodels\tcompared\tto:\t(1)\tpreexisting\ttechnologies,\tand\t(2)\tclosed\tmodels.”)\t(internal\tcitation\tomitted);\t(quotation\tmarks\tin\toriginal)\tGitHub\tComment\tat\t3\t(“Evidence\tof\tharmful\tcapabilities\tin\twidely\tavailable\tmodel\tweights\tand\ttheir\tuse\tshould\tconsider\tbaselines\tof\tclosed,\tproprietary\tAI\tcapabilities\tand\tthe\tavailability\tof\tpotentially\tdangerous\tinformation\tin\tbooks\tand\tvia\tinternet\tsearch.\t[.\t.\t.]\tToday,\tavailable\tevidence\tof\tthe\tmarginal\trisks\tof\topen\trelease\tdoes\tnot\tsubstantiate\tgovernment\trestrictions.”);\tBSA\tComment\tat\t3\t(“Any\tspecific\tpolicy\toptions\tfor\topen\tfoundation\tmodels\tshould\tbe\tconsidered\tonly\tas\tany\tmarginal\trisk\tposed\tby\tsuch\tmodels\tare\tbetter\tunderstood.”);\tU.S.\tChamber\tof\tCommerce\tat\t3\t(“As\tindicated\tin\tthe\tNIST\t[Risk\tManagement\tFramework]\t1.0,\t‘Risk\ttolerance\tand\tthe\tlevel\tof\trisk\tacceptable\tto\torganizations\tor\tsociety\tare\thighly\tcontextual\tand\tapplication\tand\tuse-case\tspecific.’\tThis\tis\twhy\twe\tbelieve\tit\tis\tessential\tfor\tNTIA\tto\tfocus\ton\tthe\tmarginal\trisk,\twhich\tis\tcontext-specific.”)\t(internal\tcitation\tomitted)\t(quotation\tmarks\tin\toriginal);\tAI\tHealthcare\tWorking\tGroup\tat\t1\t(“The\trisks\tof\ttechnology\tare\treal,\tbut\ttheir\tpromise\toutweighs\tthose\trisk\tand\tthose\trisks\tshould\tbe\tviewed\tand\tevaluated\tin\tthe\tcontext\tof\tmarginal\trisk.”);\tJohns\tHopkins\tCenter\tfor\tHealth\tSecurity\tComment\tat\t5\t(“As\tSayesh\tKapoor\tand\tcolleagues\tcaution,\tit\tis\timportant\tto\tconsider\tthe\tmarginal\trisk\tthat\topen\tmodels\tpose\tabove\tpreexisting\ttechnologies.”)\t(internal\tcitation\tomitted).\tSee\talso\tgenerally\tCenter\tfor\tDemocracy\t&\tTechnology,\t&\tet\tal.\t(March,\t25,\t2024).\tRE:\tOpenness\tand\tTransparency\tin\tAI\tProvide\tSignificant\tBenefits\tfor\tSociety.\thttps://cdt.org/wp-content/uploads/2024/03/Civil-Society-Letter-onOpenness-for-NTIA-Process-March-25-2024.pdf\t(letter\tfrom\tcivil\tsociety\torganizations\tpromoting\ta\tmarginal\trisk\tassessment);\tKapoor\tS.\tet\tal.,\t(2024).\tOn\tthe\tSocietal\tImpact\tof\tOpen\tFoundation\tModels.\tArXiv.\thttps://arxiv.org/pdf/2403.07918\t(presenting\ta\tmarginal\trisk\tframework). 31\tSee\tExecutive\tOrder\t14110,\tsection\t4.6. 32\tSee,\te.g.,\tCenter\tfor\tAI\tPolicy\tComment\tat\t6\t(“We\tfind\tthat\tthe\ttimeframe\tbetween\tclosed\tand\topen\tmodels\tright\tnow\tis\taround\t1.5\tyears.\tWe\tcan\tarrive\tat\tthis\tconclusion\tby\tanalyzing\tbenchmark\tperformance\tbetween\tcurrent\tleading\topen\tweight\tAI\tmodels\tand\tthe\tbest\tclosed\tsource\tAI\tmodels.”);\tUnlearn.AI\tComment\tat\t2\t(“Estimating\tthe\ttimeframe\tbetween\tthe\tdeployment\tof\ta\tclosed\tmodel\tand\tthe\tdeployment\tof\tan\topen\tfoundation\tmodel\tof\tsimilar\tperformance\ton\trelevant\ttasks\tis\tpossible\tby\tlooking\tat\tthe\tgaps\tin\thuman-evaluated\tperformance\tbetween\topen\tfoundation\tmodels\tand\tclosed\tcounterparts.\tWhile\tthis\tis\thighly\tdependent\ton\tthe\tspecific\tAI\tmodel\tand\tits\tapplication\tdomain,\twe\tcan\tlook\ttowards\ta\tfew\texamples.\tAt\tthe\tmoment,\tit\ttakes\tabout\t6\tmonths\tto\t1\tyear\tfor\tsimilarly\tperforming\topen\tmodels\tto\tbe\tsuccessfully\tdeployed\tafter\tthe\tdeployment\tof\tOpenAI’s\tclosed\tmodels.\tThe\ttime\tgap\tbetween\tproprietary\timage\trecognition\tmodels\tand\thigh-quality\topen-source\talternatives\thas\tnarrowed\trelatively\tquickly\tdue\tto\trobust\tcommunity\tengagement\tand\tsignificant\tpublic\tinterest.\tIn\tcontrast,\tmore\tniche\tor\tcomplex\tapplications,\tsuch\tas\tthose\trequiring\textensive\tdomainspecific\tknowledge\tor\tdata,\tmight\tsee\tlonger\ttimeframes\tbefore\tcompetitive\topen\tmodels\temerge.”);\tDatabricks\tComment\tat\t3\t(“Databricks\tbelieves\tthat\tmajor\topen\tsource\tmodel\tdevelopers\tare\tnot\tfar\tbehind\tthe\tclosed\tmodel\tdevelopers\tin\tcreating\tequally\thigh\tperformance\tmodels,\tand\tthat\tthe\tgap\tbetween\tthe\trespective\tdevelopment\tcycles\tmay\tbe\tclosing.”)\t(internal\tcitation\tomitted);\tStability\tAI\tComment\tat\t17\t(“There\tis\tample\tevidence\tthat\tclosed\tmodels\texhibiting\tcategory\tstate\tof\tthe\tart\tperformance\twill\tbe\tmatched\tby\topen\tmodels\tin\tdue\tcourse.\tPreviously,\tit\ttook\t~28\tmonths\tbefore\tan\topen\tmodel\tsuch\tas\tGPT-J\tfrom\tEleutherAI\tapproached\tthe\tperformance\tof\ta\tclosed\tmodel\tsuch\tas\tGPT-2\tfrom\tOpen\tAI\ton\tcommon\tbenchmarks.\tThat\tgap\tis\tclosing.\tOnly\t~eight\tmonths\telapsed\tbefore\topen\tmodels\tsuch\tas\tLlama\t2-70B\tfrom\tMeta\trivaled\tGPT-3.5\tfrom\tOpen\tAI,\tand\tonly\t~ten\tmonths\telapsed\tbefore\tFalcon-180B\tfrom\tthe\tTechnology\tInnovation\tInstitute\t(funded\tby\tthe\tAbu\tDhabi\tGovernment)\texceeded\tGPT-3.5\tperformance.”)\t(internal\tcitation\tomitted).\tBut\tsee\tMeta\tComment\tat\t13\t(“It\tis\tnot\tpossible\tto\tgenerally\testimate\tthis\ttimeframe\tgiven\tthe\tvariables\tinvolved,\tincluding\tthe\tmodel\tdeployment\tdevelopers’\tbusiness\tmodels\tand\twhether,\tin\tthe\tcase\tof\tLlama\t2,\tthey\tdownload\tthe\tmodel\tweights\tfrom\tMeta\tdirectly\tor\taccessed\tit\tthrough\tthird-party\tservices\tlike\tAzure\tor\tAWS.”);\tHugging\tFace\tComment\tat\t3\t(“Timelines\tvary\tand\testimates\twill\tchange\tby\tutility\tof\tthe\tmodel\tand\tcosts.”);\tEleutherAI\tComment\tat\t21\t(“The\ttimeframe\tbetween\tdeployment\tof\tan\topen\tand\tclosed\tequally-performing\tmodel\tis\tdifficult\tto\tpredict\treliably.\tThe\tprimary\tblocker\tfor\tthe\tcapabilities\tof\topen\tmodels\tis\tfunding,\twhich\tcan\tdisappear\tat\tthe\twhim\tof\ta\thandful\tof\twell-resourced\tindividuals.\t[.\t.\t.]”\tSee\talso\tCSET\tComment\tat\t2\t(“The\tbest\tway\tto\tgauge\tsuch\ttimeframes\tmay\tbe\tto\tdirectly\tcontact\torganizations\tdesigning\tfoundation\tmodels\tand\tacquire\tinformation\tregarding\ttheir\tmodel\tperformance\tand\trelease\tstrategies.\tThis\tis\tthe\tmost\tviable\tway\tto\tget\tthese\testimations,\talthough\tthese\torganizations\tmay\tnot\thave\tthe\twill\tor\tobligation\tto\tprovide\tsuch\tinformation.”). 33\tFine-tuning\taway\tLlama\t2-Chat\t13B’s\tsafety\tfeatures\twhile\tretaining\tmodel\tperformance\tcosts\tless\tthan\t$200.\tSee,\tGade,\tP.,\tet\tal.\t(2023,\tOctober\t31).\tBadLlama:\tcheaply\tremoving\tsafety\tfine-tuning\tfrom\tLlama\t2-Chat\t13B.\tArXiv.\thttps://doi.org/10.48550/arXiv.2311.00117. 53 Dual-Use Foundation Models with Widely Available Model Weights34\tFilters\tapplied\tto\tgenerated\tcontent\tthat\tprevent\tprohibited\tmaterial\tfrom\tbeing\treturned\tto\tthe\tuser. 35\tLists\tof\twords,\tphrases,\tand\ttopics\tthat\tcannot\tbe\tgenerated. 36\tMeasures\tintended\tto\tprohibit\tprompts\tthat\tattempt\tto\tcircumvent\tthe\taforementioned\tsafety\tfeatures.\tHowever,\tsee\tgenerally,\tCan\tFoundation\tModels\tBe\tSafe\tWhen\tAdversaries\tCan\tCustomize\tThem?\t(2023,\tNovember\t2).\tHai.stanford.edu.\thttps://hai.stanford.edu/news/can-foundation-models-be-safewhen-adversaries-can-customize-them;\tHenderson,\tP.,\tet\tal.\t(2023,\tAugust\t8).\tSelf-Destructing\tModels:\tIncreasing\tthe\tCosts\tof\tHarmful\tDual\tUses\tof\tFoundation\tModels.\tArXiv.\thttps://doi.org/10.48550/arXiv.2211.14946. 37\tSee,\tSeger,\tE.,\tet\tal.\t(2023,\tOctober\t9).\tOpen-Sourcing\tHighly\tCapable\tFoundation\tModels:\tAn\tEvaluation\tof\tRisks,\tBenefits,\tand\tAlternative\tMethods\tfor\tPursuing\tOpen-Source\tObjectives.\tSocial\tScience\tResearch\tNetwork.\thttps://doi.org/10.2139/ssrn.4596436;\tBoulanger,\tA.\t(2005).\tOpen-source\tversus\tproprietary\tsoftware:\tIs\tone\tmore\treliable\tand\tsecure\tthan\tthe\tother?\tIBM\tSystems\tJournal,\t44(2),\t239–248.\thttps://doi.org/10.1147/sj.442.0239;\tGade,\tP.,\tet\tal.\t(2023,\tOctober\t31).\tBadLlama:\tcheaply\tremoving\tsafety\tfine-tuning\tfrom\tLlama\t2-Chat\t13B.\tArXiv.\thttps://doi.org/10.48550/arXiv.2311.00117. 38\tMouton,\tChristopher\tA.,\tet\tal.,\tThe\tOperational\tRisks\tof\tAI\tin\tLarge-Scale\tBiological\tAttacks:\tResults\tof\ta\tRed-Team\tStudy.\tSanta\tMonica,\tCA:\tRAND\tCorporation,\t2024.\thttps://www.rand.org/pubs/research_reports/RRA2977-2.html.\tSee\talso\tCDT\tComment\tat\t19. 39\tZhan,\tQ.\tet\tal.,\t(2023).\tRemoving\tRLHF\tProtections\tin\tGPT-4\tvia\tFine-Tuning.\tUIUC,\tStanford.\thttps://openreview.net/pdf?id=NyGm96pC3n. 40\tSee,\tSeger,\tE.,\tet\tal.\t(2023,\tOctober\t9).\tOpen-Sourcing\tHighly\tCapable\tFoundation\tModels:\tAn\tEvaluation\tof\tRisks,\tBenefits,\tand\tAlternative\tMethods\tfor\tPursuing\tOpen-Source\tObjectives.\tSocial\tScience\tResearch\tNetwork.\thttps://doi.org/10.2139/ssrn.4596436. 41\tFor\tinstance,\ta\tmethod\tdiscovered\tto\tjailbreak\tMeta’s\tLlama\t2\tworks\ton\tother\tLLMs,\tsuch\tas\tGPT-4\tand\tClaude.\tSeger,\tE.,\tet\tal.\t(2023).\tOpen-Sourcing\tHighly\tCapable\tFoundation\tModels:\tAn\tEvaluation\tof\tRisks,\tBenefits,\tand\tAlternative\tMethods\tfor\tPursuing\tOpen-Source\tObjectives.\tSocial\tScience\tResearch\tNetwork.\thttps://doi.org/10.2139/ssrn.4596436. 42\tLi,\tN.,\tet\tal.\t(2024,\tMay\t15).\tThe\tWMDP\tBenchmark:\tMeasuring\tand\tReducing\tMalicious\tUse\tWith\tUnlearning.\tArXiv.\thttps://doi.org/10.48550/ arXiv.2403.03218;\tLynch,\tA.,\tet\tal.\t(2024,\tFebruary\t26).\tEight\tMethods\tto\tEvaluate\tRobust\tUnlearning\tin\tLLMs.\tArXiv.\thttps://doi.org/10.48550/ arXiv.2402.16835. 43\tMouton,\tC.\tA.,\tet\tal.\t(2024,\tJanuary\t25).\tThe\tOperational\tRisks\tof\tAI\tin\tLarge-Scale\tBiological\tAttacks:\tResults\tof\ta\tRed-Team\tStudy.\tRAND\tCorporation.\thttps://www.rand.org/pubs/research_reports/RRA2977-2.html. 44\tMouton,\tC.\tA.,\tet\tal.\t(2024,\tJanuary\t25).\tThe\tOperational\tRisks\tof\tAI\tin\tLarge-Scale\tBiological\tAttacks:\tResults\tof\ta\tRed-Team\tStudy.\tRAND\tCorporation.\thttps://www.rand.org/pubs/research_reports/RRA2977-2.html. 45\tCongressional\tResearch\tServices\t(2023,\tNovember\t23)\tArtificial\tIntelligence\tin\tthe\tBiological\tSciences:\tUses,\tSafety,\tSecurity,\tand\tOversight,\thttps:// crsreports.congress.gov/product/pdf/R/R47849. 46\tSee\tJohns\tHopkins\tCenter\tfor\tHealth\tSecurity\tComment\tat\t5.\tSee\tJohns\tHopkins\tCenter\tfor\tHealth\tSecurity\tComment\tat\t5\t(“Indeed,\tless\tthan\ta\tmonth\tafter\tEvo\twas\treleased,\tit\thad\talready\tbeen\tfine-tuned\ton\ta\tdataset\tof\tadeno-associated\tvirus\tcapsids,\tie,\tprotein\tshells\tused\tby\ta\tclass\tof\tviruses\tthat\tinfect\thumans.\tAs\tthis\tcase\tsuggests,\twhen\ta\tmodel’s\tweights\tare\tpublicly\tavailable,\ta\tdeveloper’s\tdecision\tnot\tto\tendow\tthe\tmodel\twith\tdangerous\tcapabilities\tis\tfar\tfrom\tfinal.”). 47\tSee\tgenerally,\tNguyen,\tE.,\tet\tal.\t(2024,\tFebruary\t27).\tEvo:\tDNA\tfoundation\tmodeling\tfrom\tmolecular\tto\tgenome\tscale.\tArc\tInstitute.\thttps://arcinstitute.org/ news/blog/evo;\tAbramson,\tJ.,\tet\tal.\tAccurate\tstructure\tprediction\tof\tbiomolecular\tinteractions\twith\tAlphaFold 3.\tNature\t(2024).\thttps://doi.org/10.1038/ s41586-024-07487-w. 48\tSandbrink,\tJ.\t(2023).\tArtificial\tintelligence\tand\tbiological\tmisuse:\tDifferentiating\trisks\tof\tlanguage\tmodels\tand\tbiological\tdesign\ttools.\tArXiv.\thttps://arxiv. org/abs/2306.13952. 49\tOne\tcounterpoint\tis\tGoogle\tAlpha\tfold\tand\tpredicting\tprotein\tfolding.\tPutting\tthe\tpower\tof\tAlphaFold\tinto\tthe\tworld’s\thands.\t(2024,\tMay\t14).\tGoogle\tDeepMind.\thttps://deepmind.google/discover/blog/putting-the-power-of-alphafold-into-the-worlds-hands/. 50\tSee\tEPIC\tComment\tat\t4\tfn\t15.\tSee\tLiwei\tSong\t&\tPrateek\tMittal,\tSystematic\tEvaluation\tof\tPrivacy\tRisks\tof\tMachine\tLearning\tModels,\t30\tProc.\tUSENIX\tSec.\tSymp.\t2615,\t2615\t(2021).\tHurdles\tto\tunlearning\tdata\tare\tat\tthe\tcore\tof\trecent\tFTC\tcases\trequiring\tAI\tmodel\tdeletion.\tSee\tJevan\tHutson\t&\tBen\tWinters,\tAmerica’s\tNext\t‘Stop\tModel!’:\tModel\tDeletion,\t8\tGeo.\tL.\tTech.\tRev.\t125,\t128–134\t(2022),\thttps://papers.ssrn.com/sol3/papers.cfm?abstract_id=4225003. 51\tMouton,\tC.\tA.,\tet\tal.\t(2024,\tJanuary\t25).\tThe\tOperational\tRisks\tof\tAI\tin\tLarge-Scale\tBiological\tAttacks:\tResults\tof\ta\tRed-Team\tStudy.\tRAND\tCorporation.\thttps://www.rand.org/pubs/research_reports/RRA2977-2.html. 52\tTerwilliger,\tT.\tC.,\tet\tal.\t(2023).\tAlphaFold\tpredictions\tare\tvaluable\thypotheses\tand\taccelerate\tbut\tdo\tnot\treplace\texperimental\tstructure\tdetermination.\tNature\tMethods,\t1–7.\thttps://doi.org/10.1038/s41592-023-02087-4. 53\tFang,\tR.,\tet\tal.\t(2024).\tLLM\tAgents\tcan\tAutonomously\tExploit\tOne-day\tVulnerabilities.\thttps://arxiv.org/html/2404.08144v1. 54\tState\tof\tOpen\tSource\tAI\tBook\t2023\tEdition.\t(2024).\thttps://book.premai.io/state-of-open-source-ai/#contributing. 55\tCyber\tattackers\tcould\tuse\tfoundation\tmodels\tin\tassisting\tin\tthe\tdesign\tor\tdeployment\tof\tsophisticated\tmalware,\tincluding\tviruses,\transomware,\tand\t54 Dual-Use Foundation Models with Widely Available Model WeightsTrojans.\tFor\tinstance,\tLlama\t2,\ta\tfoundation\tmodel\twith\twidely\tavailable\tmodel\tweights\tdeveloped\tby\tMeta,\thas\talready\thelped\tcyber-attackers\tdesign\ttools\tto\tillicitly\tdownload\temployees’\tlogin\tinformation.\tRay,\tT.\t(2024,\tFebruary\t21).\tCybercriminals\tare\tusing\tMeta’s\tLlama\t2\tAI,\taccording\tto\tCrowdStrike.\tZdnet,\thttps://www.zdnet.com/article/cybercriminals-are-using-metas-llama-2-ai-according-to-crowdstrike/.\tInitial\tevidence\tsuggests\tthat\tsome\tclosedweight\tfoundation\tmodels\tcan\tbe\tused\tto\t“autonomously\thack\twebsites,\tperforming\ttasks\tas\tcomplex\tas\tblind\tdatabase\tschema\textraction\tand\tSQL\tinjections\twithout\thuman\tfeedback”\tand\tto\t“autonomously\tfind[]\t[cybersecurity]\tvulnerabilities\tin\twebsites\tin\tthe\twild.”\tFang,\tR.,\tet\tal.\t(2024,\tFebruary\t15).\tLLM\tAgents\tcan\tAutonomously\tHack\tWebsites.\tArXiv.org.\thttps://doi.org/10.48550/arXiv.2402.06664.\tThe\tNational\tCyber\tSecurity\tCentre\tof\tthe\tGovernment\tof\tthe\tUnited\tKingdom\tassesses\tthat\t“in\tthe\tnear\tterm,\t[vulnerability\tdetection\tand\texploitation]\twill\tcontinue\tto\trely\ton\thuman\texpertise,\tmeaning\tthat\tany\tlimited\tuplift\t[in\tcyberattack\tthreat]\twill\thighly\tlikely\tbe\trestricted\tto\texisting\tthreat\tactors\tthat\tare\talready\tcapable.\t.\t.\t.\tHowever,\tit\tis\ta\trealistic\tpossibility\tthat\t[constraints\ton\texpertise,\tequipment,\ttime,\tand\tfinancial\tresourcing]\tmay\tbecome\tless\timportant\tover\ttime,\tas\tmore\tsophisticated\tAI\tmodels\tproliferate\tand\tuptake\tincreases.”\tNational\tCyber\tSecurity\tCentre.\t(2024,\tJanuary\t24).\tThe\tnear-term\timpact\tof\tAI\ton\tthe\tcyber\tthreat.\twww.ncsc. gov.uk.\thttps://www.ncsc.gov.uk/report/impact-of-ai-on-cyber-threat.\tShould\tthese\tattacks\tsuccessfully\ttarget\telectrical\tgrids,\tfinancial\tinfrastructures,\tgovernment\tagencies,\tand\tother\tentities\tcritical\tto\tpublic\tsafety\tand\tnational\tsecurity,\tthe\tsecurity\timplications\tcould\tbe\tsignificant. 56\tA\tvirus\treplicates\titself\tby\tmodifying\tother\tprograms\tand\tinserting\tits\tcode\tinto\tthose\tprograms. 57\tRansomware\tis\tmalware\tthat\tholds\ta\tdevice\tor\tdata\thostage\tuntil\tthe\tvictim\tpays\ta\transom\tto\tthe\thacker. 58\tA\tTrojan\tmalware\tattack\tmisleads\tusers\tby\tdisguising\titself\tas\ta\tstandard\tprogram. 59\tCybercriminals\tare\tusing\tMeta’s\tLlama\t2\tAI,\taccording\tto\tCrowdStrike.\t(n.d.).\tZDNET.\thttps://www.zdnet.com/article/cybercriminals-are-using-metasllama-2-ai-according-to-crowdstrike. 60\tCyber\tattackers\tcould\tpossibly\tuse\tdual-use\tfoundation\tmodels\twith\twidely\tavailable\tmodel\tweights\tto\tperform\tcyberattacks\ton\tclosed\tmodels\tor\textract\tdata\tfrom\tthem.\tActors\tcould\t(i)\tpoison\tmodels’\ttraining\tdata\twith\tan\tinflux\tof\tsynthetically\tgenerated\tcontent,\t(ii)\tsteal\tmodel\tweights\tand\tother\tproprietary\tmodel\tinfrastructure\tcontent\tthrough\tgenerated\t“jailbreaking”\tprompts,\tand\t(iii)\tleverage\topen\tmodels\tto\taccess\tindividual\tdata\tfrom\tclosed\tmodels\ttrained\ton\tprivate\tdata,\twhich\tintroduces\tprivacy\tand\tautonomy\tconcerns.\tSee\tNasr,\tM.\t(2023).\tScalable\tExtraction\tof\tTraining\tData\tfrom\t(Production)\tLanguage\tModels.\tGoogle\tDeepMind,\tUniversity\tof\tWashington,\tCornell,\tCMU,\tUC\tBerkeley,\tand\tETH\tZurich. 61\tSee\tNational\tCyber\tSecurity\tCentre.\t(2024,\tJanuary\t24).\tThe\tnear-term\timpact\tof\tAI\ton\tthe\tcyber\tthreat.\twww.ncsc.gov.uk.\thttps://www.ncsc.gov.uk/ report/impact-of-ai-on-cyber-threat\t(“The\timpact\tof\tAI\ton\tthe\tcyber\tthreat\tis\tuneven;\tboth\tin\tterms\tof\tits\tuse\tby\tcyber\tthreat\tactors\tand\tin\tterms\tof\tuplift\tin\tcapability.”). 62\tSee\tNational\tCyber\tSecurity\tCentre.\t(2024,\tJanuary\t24).\tThe\tnear-term\timpact\tof\tAI\ton\tthe\tcyber\tthreat.\thttps://www.ncsc.gov.uk/report/impact-of-ai-oncyber-threat\tat\t5-7. 63\tMITRE\tATT&CK.\t(n.d.).\thttps://attack.mitre.org/;\tKapoor,\tS.\tet\tal.,\t(2024).\tOn\tthe\tSocietal\tImpact\tof\tOpen\tFoundation\tModels.\tArXiv.\thttps://arxiv.org/ pdf/2403.07918. 64\tKapoor,\tS.\tet\tal.,\t(2024).\tOn\tthe\tSocietal\tImpact\tof\tOpen\tFoundation\tModels.\tArXiv.\thttps://arxiv.org/pdf/2403.07918. 65\tU.S.\tCybersecurity\tand\tInfrastructure\tSecurity\tAgency.\tMay\t2024.\tCISA\tResponse\tto\tNTIA\tRequest\tfor\tInformation\ton\tDual\tUse\tFoundation\tArtificial\tIntelligence\tModels\tWith\tWidely\tAvailable\tModel\tWeights\t(“Foundational\tmodels\thave\tat\tleast\ttwo\tclasses\tof\tpotential\tharms.\t[…]\tThe\tsecond\tclass\tinvolves\timpacts\tthat\tare\tundesired\tby\tthose\tdeploying\tthe\tmodels\t(e.g.,\tcybersecurity\tvulnerability\tin\ta\tmodel\tdeployed\tby\ta\tcritical\tinfrastructure\tentity).\t[…]\tCreators\tand\tdeployers\tof\topen\tfoundation\tmodels\tcan\ttake\tsteps\tto\tmitigate\tthe\tsecond\tclass\tof\tharms\tby\tusing\ta\t“safe\tby\tdesign”\tapproach\tand\tbuilding\tin\tprotections\tto\ttheir\tmodel.\tThis\tmay\taddress\tcybersecurity\tvulnerabilities\tor\tother\tforms\tof\tharms\tsuch\tas\tbiases.\tResponsibly\tdeveloped\topen\tfoundation\tmodels\tare\tlikely\tto\tbe\tless\tsusceptible\tto\tharms\tand\tmisuse,\ton\tthe\twhole,\tthan\tmodels\tthat\tcannot\tbe\tpublicly\taudited.”). 66\tU.S.\tCybersecurity\tand\tInfrastructure\tSecurity\tAgency.\tMay\t2024.\tCISA\tResponse\tto\tNTIA\tRequest\tfor\tInformation\ton\tDual\tUse\tFoundation\tArtificial\tIntelligence\tModels\tWith\tWidely\tAvailable\tModel\tWeights\t(“Foundational\tmodels\thave\tat\tleast\ttwo\tclasses\tof\tpotential\tharms.\t[…]\tThe\tsecond\tclass\tinvolves\timpacts\tthat\tare\tundesired\tby\tthose\tdeploying\tthe\tmodels\t(e.g.,\tcybersecurity\tvulnerability\tin\ta\tmodel\tdeployed\tby\ta\tcritical\tinfrastructure\tentity).\t[…]\tCreators\tand\tdeployers\tof\topen\tfoundation\tmodels\tcan\ttake\tsteps\tto\tmitigate\tthe\tsecond\tclass\tof\tharms\tby\tusing\ta\t“safe\tby\tdesign”\tapproach\tand\tbuilding\tin\tprotections\tto\ttheir\tmodel.\tThis\tmay\taddress\tcybersecurity\tvulnerabilities\tor\tother\tforms\tof\tharms\tsuch\tas\tbiases.\tResponsibly\tdeveloped\topen\tfoundation\tmodels\tare\tlikely\tto\tbe\tless\tsusceptible\tto\tharms\tand\tmisuse,\ton\tthe\twhole,\tthan\tmodels\tthat\tcannot\tbe\tpublicly\taudited.”). 67\tCISA.\t(n.d.)\tSecure\tby\tDesign.\thttps://www.cisa.gov/securebydesign. 68\tWhile\tSecurity-BERT\tis\tnot\ta\tdual-use\tfoundation\tmodel\tbecause\tit\tdoes\tnot\thave\t“at\tleast\ttens\tof\tbillions\tof\tparameters,”\tas\trequired\tby\tSection\t3(k)\tof\tExecutive\tOrder\t14110,\tits\tcapabilities\tmay\tbe\tindicative\tof\tthe\tcapabilities\tof\tdual-use\tfoundation\tmodels. 69\tFerrag,\tM.\tet\tal.,\t(2024).\tRevolutionizing\tCyber\tThreat\tDetection\twith\tLarge\tLanguage\tModels:\tA\tprivacy-preserving\tBERT-based\tLightweight\tModel\tfor\tIoT/ IIoT\tDevices.\tTechnology\tInnovation\tInstitute. 70\tAlam,\tM.\t(2023).\tRecasting\tSelf-Attention\twith\tHolographic\tReduced\tRepresentations.\tArXiv.\thttps://arxiv.org/abs/2305.19534.\tDeng,\tY.\t(2022).\tLarge\tLanguage\tModels\tare\tZero-Shot\tFuzzers:\tFuzzing\tDeep-Learning\tLibraries\tvia\tLarge\tLanguage\tModels.\tArXiv.\thttps://arxiv.org/abs/2212.14834. 71\tRotlevi,\tS.\t(2024,\tFebruary\t15).\tAI\tSecurity\tTools:\tThe\tOpen-Source\tToolkit.\thttps://www.wiz.io/academy/ai-security-tools;\tHughes,\tC.\t(2024,\tJanuary\t16).\t55 Dual-Use Foundation Models with Widely Available Model WeightsThe\tOWASP\tAI\tExchange:\tAn\topen-source\tcybersecurity\tguide\tto\tAI\tcomponents.\thttps://www.csoonline.com/article/1290876/the-owasp-ai-exchange-anopen-source-cybersecurity-guide-to-ai-components.html. 72\tVulnerability\tdetection\tand\tscanning\tis\tan\timportant\ttool\tin\tthe\tcybersecurity\tdefense\ttoolbox,\tas\tis\twell\tdemonstrated\tby\tthe\tCybersecurity\tand\tInfrastructure\tSecurity\tAgency’s\tfocus\ton\tvulnerability\tscanning\tin\tits\t“Cyber\tHygiene”\tservices,\twhich\tare\tdesigned\tto\timprove\tthe\tcybersecurity\tof\tgovernment\tand\tcritical\tinfrastructure\torganizations.\tSee\tCISA.\t(n.d.).\tCyber\tHygiene\tServices.\thttps://www.cisa.gov/cyber-hygiene-services.\tSee\talso\tThe\tWhite\tHouse.\t(2023).\tNational\tCybersecurity\tStrategy.\thttps://www.whitehouse.gov/wp-content/uploads/2023/03/National-Cybersecurity-Strategy-2023. pdf\tat\t21\t(describing\t“new\ttools\tfor\tsecure\tsoftware\tdevelopment,\tsoftware\ttransparency,\tand\tvulnerability\tdiscovery”\tas\timportant\tfactors\tin\tdefining\tcybersecurity\tobligations\tfor\tsoftware\tdevelopers). 73\tZellers,\tR.\tet\tal.,\t(2020).\tDefending\tAgainst\tNeural\tFake\tNews.\tArXiv\tpreprint.\thttps://arxiv.org/pdf/1905.12616;\tKirchenbauer,\tJ.\t(2024);\tOn\tthe\tReliability\tof\tWatermarks\tfor\tLarge\tLanguage\tModels.\tInternational\tConference\ton\tLearning\tRepresentations\t(ICLR).\thttps://doi.org/10.48550/arXiv.2306.04634;\tLiu,\tH.,\tet\tal.\t(2023).\tChain\tof\tHindsight\tAligns\tLanguage\tModels\twith\tFeedback.\tArXiv.\thttps://doi.org/10.48550/arXiv.2302.02676;\tBelrose,\tN.\tLEACE:\tPerfect\tlinear\tconcept\terasure\tin\tclosed\tform.\t37th\tConference\ton\tNeural\tInformation\tProcessing\tSystems\t(NeurIPS\t2023).\thttps://proceedings.neurips.cc/paper_files/ paper/2023/file/d066d21c619d0a78c5b557fa3291a8f4-Paper-Conference.pdf;\tBhardwaj,\tR.\t&\tPoria,\tS.\t(2023).\tRed-Teaming\tLarge\tLanguage\tModels\tusing\tChain\tof\tUtterances\tfor\tSafety-Alignment.\tArXiv\tpreprint.\thttps://doi.org/10.48550/arXiv.2308.09662;\tZou,\tA.\tet\tal\t(2023).\tUniversal\tand\tTransferable\tAdversarial\tAttacks\ton\tAligned\tLanguage\tModels.\tArXiv\tpreprint.\thttps://doi.org/10.48550/arXiv.2307.15043. 74\tSee\tCCIA\tComment\tat\t1-2\t(“Open\tmodels\talso\tpresent\tadvantages\tin\tAI\tgovernance,\tbeing\teasier\tto\tunderstand\tand\ttest.”). 75\tMozilla\tOpen\tSource\tAudit\tTooling\t(OAT)\tProject.\t(n.d.).\tMozilla.\thttps://foundation.mozilla.org/en/what-we-fund/oat/. 76\tLambert,\tN.\tet\tal.\t(2024,\tJune\t8).\tRewardBench:\tEvaluating\tReward\tModels\tfor\tLanguage\tModeling.\tArXiv.\thttps://doi.org/10.48550/arXiv.2403.13787. 77\tHubinger,\tE.,\tet\tal.\t(2024,\tJanuary\t17).\tSleeper\tAgents:\tTraining\tDeceptive\tLLMs\tthat\tPersist\tThrough\tSafety\tTraining.\tArXiv.\thttps://doi.org/10.48550/ arXiv.2401.05566;\tMeasuring\tthe\timpact\tof\tpost-training\tenhancements.\t(n.d.).\tMETR’s\tAutonomy\tEvaluation\tResources.\thttps://metr.github.io/autonomyevals-guide/elicitation-gap/. 78\tSee\talso\tAI\tAccountability\tPolicy\tReport,\tNational\tTelecommunications\tand\tInformation\tAdministration.\t(2024,\tMarch).\thttps://www.ntia.gov/sites/ default/files/2024-04/ntia-ai-report-print.pdf\tat\t70\t(noting\tthat\t“[i]ndependent\tAI\taudits\tand\tevaluations\tare\tcentral\tto\tany\taccountability\tstructure\t[]”).\t79\tSee\tAnthony\tBarret\tComment\tat\t2\t(“Although\tboth\tclosed\tand\topen\tmodels\tcan\tpose\tsome\tsuch\trisks,\tunsecured\tmodels\tpose\tunique\trisks\tin\tthat\tsafety\tand\tethical\tsafeguards\tthat\twere\timplemented\tby\tdevelopers\tcan\tbe\tremoved\trelatively\teasily\tfrom\tmodels\twith\twidely\tavailable\tweights\t(e.g.,\tvia\tfine\ttuning).”)\t(citation\tomitted). 80\tSee\tgenerally,\tHofmann,\tet\tal.\t(2024,\tMarch\t1).\tDialect\tprejudice\tpredicts\tAI\tdecisions\tabout\tpeople’s\tcharacter,\temployability,\tand\tcriminality.\tArXiv.\thttps://doi.org/10.48550/arXiv.2403.00742. 81\tExamining\tMalicious\tHugging\tFace\tML\tModels\twith\tSilent\tBackdoor.\t(2024,\tFebruary\t27).\tJFrog.\thttps://jfrog.com/blog/data-scientists-targeted-bymalicious-hugging-face-ml-models-with-silent-backdoor/. 82\tNestor\tMaslej,\tet\tal.\tArtificial\tIntelligence\tIndex\tReport\t2024.\t(2024\tApril).\thttps://aiindex.stanford.edu/wp-content/uploads/2024/04/HAI_2024_AI-IndexReport.pdf. 83\tMarkus\tAnderljung\tet\tal.\t(2023,\tJuly\t6).\tFrontier\tAI\tRegulation:\tManaging\tEmerging\tRisks\tto\tPublic\tSafety.\tArXiv.\thttps://doi.org/10.48550/arXiv.2307.03718. 84\tCenter\tfor\ta\tNew\tAmerican\tSecurity\tComment\tat\t9\t(“[L]eading\tChinese\tlabs\thave\tapplied\tthe\tarchitecture\tand\ttraining\tprocess\tof\tMeta’s\tLlama\tmodels\tto\ttrain\ttheir\town\tmodels\twith\tsimilar\tlevels\tof\tperformance.”). 85\tSzabadföldi,\tIstván.\t(2021).\tArtificial\tIntelligence\tin\tMilitary\tApplication\t–\tOpportunities\tand\tChallenges.\tLand\tForces\tAcademy\tReview.\t26.\t157-165.\t10.2478/raft-2021-0022. 86\tMozur,\tP.\tet\tal.\t(2024,\tFebruary\t21).\tChina’s\tRush\tto\tDominate\tA.I.\tComes\tWith\ta\tTwist:\tIt\tDepends\ton\tU.S.\tTechnology.\tNYTimes.\thttps://www.nytimes. com/2024/02/21/technology/china-united-states-artificial-intelligence.html. 87\tSee\tTransformative\tFutures\tInstitute\tcomment\tat\t2\t(“To\tmaximize\tthe\tbenefit\tof\tfoundation\tmodels,\tdevelopers\tcan\toffer\tstructured\taccess\tto\tmodels\t(e.g.,\tthrough\tAPIs)\tto\ta\twide\trange\tof\tusers\twhile\tmaintaining\tthe\tcapability\tto\tprevent\tharmful\tmisuse,\tblock\tor\tfilter\tdangerous\tcontent,\tand\tconduct\tcontinual\tsafety\tevaluations.”). 88\tSee\tOpenAI\tComment\tat\t2\t(”\tFor\texample,\twe\trecently\tpartnered\twith\tMicrosoft\tto\tdetect,\tstudy,\tand\tdisrupt\tthe\toperations\tof\ta\tnumber\tof\tnation-state\tcyber\tthreat\tactors\twho\twere\tabusing\tour\tGPT-3.5-Turbo\tand\tGPT-4\tmodels\tto\tassist\tin\tcyber-offensive\toperations.\tDisrupting\tthese\tthreat\tactors\twould\tnot\thave\tbeen\tpossible\tif\tthe\tweights\tof\tthese\tat-the-time\tleading\tmodels\thad\tbeen\treleased\twidely,\tas\tthe\tsame\tcyber\tthreat\tactors\tcould\thave\thosted\tthe\tmodel\ton\ttheir\town\thardware,\tnever\tinteracting\twith\tthe\toriginal\tdeveloper.”). 89\tSee\tRAND\tcomment\tat\t4\t(“Coordination\twith\tforeign\tpartners\twill\tbe\tcritical\tto\tmanage\trisks\tfrom\topen\tfoundation\tmodels.\tAttempts\tto\tcontrol\tany\topen\tfoundation\tmodels\tshould\tconsider\twhich\tforeign\tactors\tcould\tre-create\tequally\tcapable\tmodels,\tas\tthose\toperating\tin”)\t;\tSee\tConnected\tHealth\tInitiative\tat\t4\t(“Support\tinternational\tharmonization.\tWe\turge\tNTIA\tto\tmaintain\ta\tpriority\tfor\tsupporting\trisk-based\tapproaches\tto\tAI\tgovernance\tin\tmarkets\tabroad\t56 Dual-Use Foundation Models with Widely Available Model Weightsand\tthrough\tbilateral\tand\tmultilateral\tagreements.”). 90\tKapoor,\tS.\tet\tal.,\t(2024).\tOn\tthe\tSocietal\tImpact\tof\tOpen\tFoundation\tModels.\tat\t2.\tArXiv.\thttps://arxiv.org/pdf/2403.07918. 91\tNestor\tMaslej,\tet\tal.\tArtificial\tIntelligence\tIndex\tReport\t2024.\t(2024\tApril).\thttps://aiindex.stanford.edu/wp-content/uploads/2024/04/HAI_2024_AI-IndexReport.pdf. 92\tSee,\te.g.,\tJohns\tHopkins\tCenter\tfor\tHealth\tSecurity\tComments\tat\t2\t(Acknowledging\tthat\tat\tcurrent,\tEvo,\ta\tbiological\tdesign\ttool\t(BDT)\t“…is\ta\t7-billion\tparameter\tmodel\tand\tso\t[is]\tbelow\tthe\tEO\tsize\tthreshold\tfor\ta\tdual-use\tfoundation\tmodel…”);\tand\tThe\tFuture\tSociety\tComments\tat\tfn.\t20,\tciting\tKapoor,\tS.\tet\tal.,\t(2024).\tOn\tthe\tSocietal\tImpact\tof\tOpen\tFoundation\tModels.\tArXiv.\thttps://arxiv.org/pdf/2403.07918.\t(”AI-generated\tpornography\tbased\ton\tStable\tDiffusion\toffshoots\tquickly\tspread\tacross\tthe\tinternet,\tincluding\timages\tresembling\treal\tpeople\tgenerated\twithout\ttheir\tconsent.”).\tThe\tlargest\tversion\tof\tStable\tDiffusion,\tStable\tDiffusion\tXL,\thas\tonly\t3B\tparameters\t(HuggingFace). 93\tEcosystem\tGraphs\tfor\tFoundation\tModels.\t(n.d.).\thttps://crfm.stanford.edu/ecosystem-graphs/index.html?mode=table. 94\tSee\tgenerally,\tFoundation\tModel\tPrivacy.\tIBM\tResearch.\thttps://research.ibm.com/projects/foundation-model-privacy\t(“Language\tmodels\thave\tan\tinherent\ttendency\tto\tmemorize\tand\teven\treproduce\tin\ttheir\toutputs\ttext\tsequences\tlearned\tduring\ttraining,\tmay\tthis\tbe\tpre-training,\tfine-tuning\tor\teven\tprompt-tuning.\tIf\tthis\ttraining\tdata\tcontained\tsensitive\tor\tpersonal\tinformation,\tthis\tcould\tresult\tin\ta\tmajor\tprivacy\tbreach.”);\tHartmann,\tV.,\tet\tal.\t(2023).\tSoK:\tMemorization\tin\tGeneral-Purpose\tLarge\tLanguage\tModels.\thttps://arxiv.org/pdf/2310.18362\t(“In\tmany\tcases\tof\tinterest,\tsuch\tas\tpersonal\tidentifiers,\tsocial\tsecurity\tnumbers\tor\tlong\tpassages\tof\tverbatim\ttext,\tit\tis\tunlikely\tthat\ta\tmodel\tcould\thallucinate\tthe\ttarget\tinformation\tor\tgain\tknowledge\tof\tit\tthrough\treasoning.”);\tHuang,\tJ.,\tShao,\tH.,\t&\tChang,\tK.\tC.-C.\t(2022,\tMay\t25).\tAre\tLarge\tPre-Trained\tLanguage\tModels\tLeaking\tYour\tPersonal\tInformation?\tArXiv.\thttps://arxiv.org/abs/2205.12628\t(“We\tfind\tthat\tPLMs\tdo\tleak\tpersonal\tinformation\tdue\tto\tmemorization.”). 95\tSee\tThorn\tComments\tat\t1\t(“One\tconcrete\trisk\tthat\tis\talready\tmanifesting\tas\ta\tharm\toccurring\ttoday,\tis\tthe\tmisuse\tof\tbroadly\tshared\tand\topen\tsource\tfoundation\tmodels\tto\tmake\tAI-generation\tchild\tsexual\tabuse\tmaterial.\tThis\ttechnology\tis\tused\tto\tnewly\tvictimize\tchildren,\tas\tbad\tactors\tcan\tnow\teasily\tsexualize\tbenign\timagery\tof\ta\tchild\tto\tscale\ttheir\tsexual\textortion\tefforts…\t.\tThis\ttechnology\tis\tfurther\tused\tin\tbullying\tscenarios,\twhere\tsexually\texplicit\tAI-generated\timagery\tis\tbeing\tused\tby\tchildren\tto\tbully\tand\tharass\tothers.”)\t(citations\tomitted). 96\t2023\tState\tof\tDeep\tFakes.\t(2023).\thttps://www.homesecurityheroes.com/state-of-deepfakes/. 97\tHome\tSecurity\tHeroes.\t(2023).\t2023\tState\tof\tDeepfakes:\tRealities,\tThreats,\tand\tImpacts.\tHome\tSecurity\tHeroes.\thttps://www.homesecurityheroes.com/ state-of-deepfakes/\t(“99%\tof\tthe\tindividuals\ttargeted\tin\tdeepfake\tpornography\tare\twomen.”),\tand\tEaton,\tA.\tA.,\tRamjee,\tD.,\t&\tSaunders,\tJ.\tF.\t(2023).\tThe\tRelationship\tbetween\tSextortion\tduring\tCOVID-19\tand\tPre-pandemic\tIntimate\tPartner\tViolence:\tA\tLarge\tStudy\tof\tVictimization\tamong\tDiverse\tU.S\tMen\tand\tWomen.\tVictims\t&\tOffenders,\t18(2),\t338–355.\thttps://doi.org/10.1080/15564886.2021.2022057.\t98\tInternet\tWatch\tFoundation\t(2023).\tHow\tAI\tis\tbeing\tabused\tto\tcreate\tchild\tsexual\tabuse\timagery.\thttps://www.iwf.org.uk/media/q4zll2ya/iwf-ai-csamreport_public-oct23v1.pdf\t;\tKang,\tC.\t(2024).\tA.I.-Generated\tChild\tSexual\tAbuse\tMaterial\tMay\tOverwhelm\tTip\tLine.\tNYTimes.\thttps://www.nytimes. com/2024/04/22/technology/ai-csam-cybertipline.html.\t99\tHow\tAI\tis\tbeing\tabused\tto\tcreate\tchild\tsexual\tabuse\timagery.\t(2023).\tInternet\tWatch\tFoundation.\thttps://www.iwf.org.uk/media/q4zll2ya/iwf-ai-csamreport_public-oct23v1.pdf. 100  Kapoor\tS.\tet\tal.,\t(2024).\tOn\tthe\tSocietal\tImpact\tof\tOpen\tFoundation\tModels.\tArXiv.\thttps://arxiv.org/pdf/2403.07918. 101\t Thiel,\tD.,\tStroebel,\tM.,\t&\tPortnoff,\tR.\t(2023,\tJune\t24).\tGenerative\tML\tand\tCSAM:\tImplications\tand\tmitigations.\tFSI.\thttps://fsi.stanford.edu/publication/ generative-ml-and-csam-implications-and-mitigations. 102\t\tHow\tAI\tis\tbeing\tabused\tto\tcreate\tchild\tsexual\tabuse\timagery.\t(2023).\tInternet\tWatch\tFoundation.\thttps://www.iwf.org.uk/media/q4zll2ya/iwf-ai-csamreport_public-oct23v1.pdf. 103\t\tThiel,\tD.\t(2023).\tGenerative\tML\tand\tCSAM:\tImplications\tand\tMitigations.\tStanford.\thttps://stacks.stanford.edu/file/druid:jv206yg3793/20230624-sio-cgcsam-report.pdf. 104\t\tStable\tDiffusion\tPublic\tRelease.\t(2023,\tAugust\t22).\tStability\tAI.\thttps://stability.ai/news/stable-diffusion-public-release. 105\t\tThiel,\tD.\t(2023).\tIdentifying\tand\tEliminating\tCSAM\tin\tGenerative\tML\tTraining\tData\tand\tModels.\tStanford\tInternet\tObservatory.\thttps://stacks.stanford.edu/ file/druid:kh752sm9123/ml_training_data_csam_report-2023-12-23.pdf. 106\t\tOpen\tTechnology\tInstitute\tComments\tat\t11,\tciting\tThiel,\tD.\t(2023).\tGenerative\tML\tand\tCSAM:\tImplications\tand\tMitigations.\tStanford\tInternet\tObservatory.\thttps://purl.stanford.edu/jv206yg3793. 107\t\tSee\tKapoor,\tS.\tet\tal.,\t(2024).\tOn\tthe\tSocietal\tImpact\tof\tOpen\tFoundation\tModels.\tArXiv.\thttps://arxiv.org/pdf/2403.07918.\t(discussion\tof\twhy\topen\tfoundation\tmodels\tpresent\tan\tincrease\tin\tmarginal\trisk\tspecifically\tfor\tNCII). 108\t\tKeller,\tM.,\t&\tDance,\tG.\t(2019,\tSeptember\t29).\tLast\tyear,\ttech\tcompanies\treported\tover\t45\tmillion\tonline\tphotos\tand\tvideos\tof\tchildren\tbeing\tsexually\tabused—More\tthan\tdouble\twhat\tthey\tfound\tthe\tprevious\tyear.\tNYTimes.\thttps://www.nytimes.com/interactive/2019/09/28/us/child-sex-abuse.html. 109\t\tGendered\tDisinformation:\tTactics,\tThemes,\tand\tTrends\tby\tForeign\tMalign\tActors\t-\tUnited\tStates\tDepartment\tof\tState.\t(2023,\tApril\t12).\tUnited\tStates\tDepartment\tof\tState.\thttps://www.state.gov/gendered-disinformation-tactics-themes-and-trends-by-foreign-malign-actors/. 57 Dual-Use Foundation Models with Widely Available Model Weights110\t\tEPIC\tcomment\tattachment\tp.\t3-4. 111\t\tKnibbs,\tK.\t(2024,\tJanuary\t26).\tResearchers\tSay\tthe\tDeepfake\tBiden\tRobocall\tWas\tLikely\tMade\tWith\tTools\tFrom\tAI\tStartup\tElevenLabs.\thttps://www.wired. com/story/biden-robocall-deepfake-elevenlabs/. 112\t\tElliott,\tV.,\t&\tKelly,\tM.\t(2024,\tJanuary\t23).\tThe\tBiden\tDeepfake\tRobocall\tis\tOnly\tthe\tBeginning.\thttps://www.wired.com/story/biden-robocall-deepfakedanger/. 113\t\tSuhasini,\tR.\t(2024,\tApril\t18).\tHow\tA.I.\tTools\tCould\tChange\tIndia’s\tElections.\thttps://www.nytimes.com/2024/04/18/world/asia/india-election-ai.html. 114\t\tChristopher,\tN.\t(2024,\tJune\t5).\t“The\tNear\tFuture\tof\tDeepfakes\tJust\tGot\tWay\tClearer.”\tThe\tAtlantic.\thttps://www.theatlantic.com/technology/ archive/2024/06/india-election-deepfakes-generative-ai/678597/.\t115\t\tAllyn,\tB.\t(2022,\tMarch\t16).\tDeepfake\tvideo\tof\tZelenskyy\tcould\tbe\t“tip\tof\tthe\ticeberg”\tin\tinfo\twar,\texperts\twarn.\thttps://www.npr. org/2022/03/16/1087062648/deepfake-video-zelenskyy-experts-war-manipulation-ukraine-russia. 116\t\tSee,\t“Deepfake”\tof\tBiden’s\tVoice\tCalled\tEarly\tExample\tof\tUS\tElection\tDisinformation.\t(2024,\tJanuary\t24).\tVoice\tof\tAmerica.\thttps://learningenglish. voanews.com/a/deepfake-of-biden-s-voice-called-early-example-of-us-election-disinformation/7455392.html;\tHartmann,\tT.\t(2024,\tApril\t16).\tViral\tdeepfake\tvideos\tof\tLe\tPen\tfamily\treminder\tthat\tcontent\tmoderation\tis\tstill\tnot\tup\tto\tpar\tahead\tof\tEU\telections.\twww.euractiv.com.\thttps://www.euractiv. com/section/artificial-intelligence/news/viral-deepfake-videos-of-le-pen-family-reminder-that-content-moderation-is-still-not-up-to-par-ahead-ofeu-elections/;\tMisinformation\tand\tdisinformation.\tAPA.\t(n.d.).\thttps://www.apa.org/topics/journalism-facts/misinformation-disinformation.\t“False\tinformation\tdeliberately\tintended\tto\tmislead.” 117\t\tLohn,\tA.\t(2024,\tJanuary\t23).\tDeepfakes,\tElections,\tand\tShrinking\tthe\tLiar’s\tDividend.\thttps://www.brennancenter.org/our-work/research-reports/ deepfakes-elections-and-shrinking-liars-dividend. 118\t\tJosh\tA\tGoldstein,\tet\tal.\t(2024\tFebruary)\tHow\tpersuasive\tis\tAI-generated\tpropaganda?.\tPNAS\tNexus.\thttps://doi.org/10.1093/pnasnexus/pgae034. 119\tJ.A.\tGoldstein,\tet\tal.\t(2023)\tGenerative\tLanguage\tModels\tand\tAutomated\tInfluence\tOperations:\tEmerging\tThreats\tand\tPotential\tMitigations.\tArXiv.\thttps:// arxiv.org/abs/2301.04246. 120\t\tSee\tAccess\tNow\tComment\tat\t2\t“Nefarious\tactors\tcan\taccess\tthem,\tremove\tbuilt-in\tsafety\tfeatures,\tand\tpotentially\tmisuse\tthem\tfor\tmalicious\tpurposes,\tfrom\tmalevolent\tactors\tcreating\tdisinformation\tto\tgenerate\tharmful\timagery\tand\tdeceptive,\tbiased,\tand\tabusive\tlanguage\tat\tscale.” 121\t\tJ.A.\tGoldstein,\tet\tal.\t(2023)\tGenerative\tLanguage\tModels\tand\tAutomated\tInfluence\tOperations:\tEmerging\tThreats\tand\tPotential\tMitigations.\tArXiv.\thttps:// arxiv.org/abs/2301.04246. 122\t\tFergusson,\tG.,\t&\tet\tal.\t(2023).\tGenerative\tHarms\tGenerative\tAI’s\tImpact\t&\tPaths\tForward\t(p.\t3).\tEPIC.\thttps://epic.org/documents/generating-harmsgenerative-ais-impact-paths-forward/.\tComment\tNTIA-**************. 123\t\tKapoor,\tS.,\t&\tNarayanan,\tA.\t(2023,\tJune\t16).\tHow\tto\tPrepare\tfor\tthe\tDeluge\tof\tGenerative\tAI\ton\tSocial\tMedia.\thttps://knightcolumbia.org/content/how-toprepare-for-the-deluge-of-generative-ai-on-social-media. 124\t\tKapoor,\tS.\tet\tal.,\t(2024).\tOn\tthe\tSocietal\tImpact\tof\tOpen\tFoundation\tModels.\tArXiv.\thttps://arxiv.org/pdf/2403.07918. 125\t\tBateman,\tJ.,\t&\tJackson,\tD.\t(2024).\tCountering\tDisinformation:\tEffectively\tAn\tEvidence\tBased\tPolicy\tGuide\t(p.\t87).\tCarnegie\tEndowment.\thttps:// carnegieendowment.org/research/2024/01/countering-disinformation-effectively-an-evidence-based-policy-guide. 126\t\tAmerican\tPsychological\tAssociation.\tMisinformation\tand\tdisinformation.\thttps://www.apa.org/topics/journalism-facts/misinformation-disinformation. 127\t\tPerrigo,\tB.\t(2023,\tOctober\t26).\tThe\tScientists\tBreaking\tAI\tto\tMake\tIt\tSafer.\tTime.\thttps://time.com/6328851/scientists-training-ai-safety/.\t128\t\tHeikkilä,\tM.\t(2023,\tFebruary\t14).\tWhy\tyou\tshouldn’t\ttrust\tAI\tsearch\tengines.\tTechnology\tReview.\thttps://www.technologyreview.com/2023/02/14/1068498/ why-you-shouldnt-trust-ai-search-engines/. 129\t\tBender,\tE.,\tet\tal.\t(2021).\tOn\tthe\tDangers\tof\tStochastic\tParrots:\tCan\tLanguage\tModels\tBe\tToo\tBig?\thttps://dl.acm.org/doi/pdf/10.1145/3442188.3445922. 130\t\tChen,\tC.\t&\tShu,\tK.\t(2023).\tCombating\tMisinformation\tin\tthe\tAge\tof\tLLMs:\tOpportunities\tand\tChallenges.\tArXiv\tpreprint.\thttps://doi.org/10.48550/ arXiv.2311.05656. 131\t\tSimon,\tF.\tM.,\tAltay,\tS.,\t&\tMercier,\tH.\t(2023).\tMisinformation\treloaded?\tFears\tabout\tthe\timpact\tof\tgenerative\tAI\ton\tmisinformation\tare\toverblown.\tHarvard\tKennedy\tSchool\t(HKS)\tMisinformation\tReview.\thttps://doi.org/10.37016/mr-2020-127. 132\t\tSimon,\tF.\tM.,\tAltay,\tS.,\t&\tMercier,\tH.\t(2023).\tMisinformation\treloaded?\tFears\tabout\tthe\timpact\tof\tgenerative\tAI\ton\tmisinformation\tare\toverblown.\tHarvard\tKennedy\tSchool\t(HKS)\tMisinformation\tReview.\thttps://doi.org/10.37016/mr-2020-127. 133\t\tSee\tgenerally:\tFlorian\tLeiser,\tet\tal.\t(2023)\tFrom\tChatGPT\tto\tFactGPT:\tA\tParticipatory\tDesign\tStudy\tto\tMitigate\tthe\tEffects\tof\tLarge\tLanguage\tModel\tHallucinations\ton\tUsers.\tIn\tProceedings\tof\tMensch\tund\tComputer\t2023\t(MuC\t‘23).\tAssociation\tfor\tComputing\tMachinery,\tNew\tYork,\tNY,\tUSA,\t81–90.\thttps://doi.org/10.1145/3603555.3603565;\tMarita\tSkjuve,\tAsbjørn\tFølstad,\tand\tPetter\tBae\tBrandtzaeg.\t2023.\tThe\tUser\tExperience\tof\tChatGPT:\tFindings\tfrom\ta\tQuestionnaire\tStudy\tof\tEarly\tUsers.\tIn\tProceedings\tof\tthe\t5th\tInternational\tConference\ton\tConversational\tUser\tInterfaces\t(CUI\t‘23).\tAssociation\tfor\tComputing\tMachinery,\tNew\tYork,\tNY,\tUSA,\tArticle\t2,\t1–10.\thttps://doi.org/10.1145/3571884.3597144. 58 Dual-Use Foundation Models with Widely Available Model Weights134\t\tNeumeister,\tL.\t(2023,\tJune\t8).\tLawyers\tblame\tChatGPT\tfor\ttricking\tthem\tinto\tciting\tbogus\tcase\tlaw.\thttps://apnews.com/article/artificial-intelligencechatgpt-courts-e15023d7e6fdf4f099aa122437dbb59b. 135\t\tHsu,\tT.\t(2023,\tAugust\t3).\tWhat\tCan\tYou\tDo\tWhen\tA.I.\tLies\tAbout\tYou? 136\t\tSee\tgenerally,\tWex\tLegal\tDictionary\tand\tEncyclopedia.\tDiscrimination.\tLegal\tInformation\tInstitute.\t(Last\tUpdated\tNovember\t2023)\thttps://www.law. cornell.edu/wex/discrimination. 137\t\tSee\tAmerican\tCivil\tLiberties\tUnion,\tCenter\tfor\tAmerican\tProgress,\tand\tthe\tLeadership\tConference\ton\tCivil\tand\tHuman\tRights\tComment\tat\t3\t“These\texamples\tspan\tthe\trange\tof\ttechnology\tthat\tmay\tbe\tdeemed\t“artificial\tintelligence,”\tand\tthe\temergence\tof\tapplications\tsuch\tas\tchatbots\tand\tfacial\trecognition\ttechnology\tunderscores\tthat\tboth\trudimentary\tand\tthe\tmost\tsophisticated\tAI\ttechnologies\tare\talready\taffecting\tcivil\trights,\tsafety,\tand\taccess\tto\topportunities.” 138\t\tSee\tgenerally:\tXiang,\tC.\t(2023,\tMarch\t22).\tThe\tAmateurs\tJailbreaking\tGPT\tSay\tThey’re\tPreventing\ta\tClosed-Source\tAI\tDystopia.\thttps://www.vice.com/en/ article/5d9z55/jailbreak-gpt-openai-closed-source;\tKnight,\tW.\t(2023,\tDecember\t5).\tA\tNew\tTrick\tUses\tAI\tto\tJailbreak\tAI\tModels-Including\tGPT-4.\thttps:// www.wired.com/story/automated-ai-attack-gpt-4/;\tAnanya.\t(2024,\tMarch\t19).\tAI\timage\tgenerators\toften\tgive\tracist\tand\tsexist\tresults:\tCan\tthey\tbe\tfixed?\thttps://www.nature.com/articles/d41586-024-00674-9;\tHofmann,\tV.\t(2024).\tDialect\tprejudice\tpredicts\tAI\tdecisions\tabout\tpeople’s\tcharacter,\temployability,\tand\tcriminality.\thttps://arxiv.org/pdf/2403.00742. 139\t\tWiessner,\tD.\t(2024,\tFebruary\t21).\tWorkday\taccused\tof\tfacilitating\twidespread\tbias\tin\tnovel\tAI\tlawsuit.\thttps://www.reuters.com/legal/transactional/ workday-accused-facilitating-widespread-bias-novel-ai-lawsuit-2024-02-21/. 140\t\tSadok,\tH.,\tSakka,\tF.\t&\tEl\tMaknouzi,\tM.\t(2022).\tArtificial\tintelligence\tand\tbank\tcredit\tanalysis:\tA\tReview.\tCogent\tEconomics\tFinance,\t10(1).\thttps://doi.org/1 0.1080/********.2021.2023262. 141\t\tJuhn,\tY.\tet\tal\t(2022).\tAssessing\tsocioeconomic\tbias\tin\tmachine\tlearning\talgorithms\tin\thealth\tcare\t:\ta\tcase\tstudy\tof\tthe\tHOUSES\tindex.\tJournal\tof\tthe\tAmerican\tMedical\tInformatics\tAssociation,\t29(7),\t1142-1151.\thttps://doi.org/10.1093/jamia/ocac052. 142\t\tFTC\tChair\tKhan\tand\tOfficials\tfrom\tDOJ,\tCFPB\tand\tEEOC\tRelease\tJoint\tStatement\ton\tAI.\t(2023,\tApril\t25).\thttps://www.ftc.gov/news-events/news/pressreleases/2023/04/ftc-chair-khan-officials-doj-cfpb-eeoc-release-joint-statement-ai. 143\t\tSee\tConnected\tHealth\tInitiative\tComment\t(“Successful\tcreation\tand\tdeployment\tof\tAI-enabled\ttechnologies\twhich\thelp\tcare\tproviders\tmeet\tthe\tneeds\tof\tall\tpatients\twill\tbe\tan\tessential\tpart\tof\taddressing\tthis\tprojected\tshortage\tof\tcare\tworkers.\tPolicymakers\tand\tstakeholders\twill\tneed\tto\twork\ttogether\tto\tcreate\tthe\tappropriate\tbalance\tbetween\thuman\tcare\tand\tdecision-making\tand\taugmented\tcapabilities\tfrom\tAI-enabled\ttechnologies\tand\ttools.”). 144\t\tSee,\tBlumenfeld,\tJ.\t(2023,\tDecember\t22).\tNASA\tand\tIBM\topenly\trelease\tGeospatial\tAI\tFoundation\tModel\tfor\tNASA\tEarth\tobservation\tdata.\tEarthdata.\thttps://www.earthdata.nasa.gov/news/impact-ibm-hls-foundation-model.\t145\t\tSee\tCaleb\tWithers\tComment\tat\t9\t(“Illustratively,\tthe\tbest\tcoding\tmodels\thave\teither\tbeen,\tor\tbeen\tderived\tfrom,\tthe\tmost\tcapable\tgeneral-purpose\tfoundation\tmodels,\twhich\tare\ttypically\ttrained\ton\tcurated\tdatasets\tof\tcoding\tdata\tin\taddition\tto\tgeneral\ttraining.”). 146\t\tNational\tInstitutes\tof\tHealth\t(n.d.).\tMission\tand\tGoals.\tDepartment\tof\tHealth\tand\tHuman\tServices.\thttps://www.nih.gov/about-nih/what-we-do/missiongoals. 147\t\tBozeman,\tB.\t&\tYoutie,\tJ.\t(2017).\tSocio-economic\timpacts\tand\tpublic\tvalue\tof\tgovernment-funded\tresearch:\tLessons\tfrom\tfour\tUN\tNational\tScience\tFoundation\tinitiatives.\tResearch\tPolicy\t46(8)\t1387-1389.\thttps://doi.org/10.1016/j.respol.2017.06.003. 148\t\tSee\tCenter\tfor\tDemocracy\t&\tTechnology,\tet\tal.\t(2024,\tMarch\t25).\tRE:\tOpenness\tand\tTransparency\tin\tAI\tProvide\tSignificant\tBenefits\tfor\tSociety.\thttps://cdt. org/wp-content/uploads/2024/03/Civil-Society-Letter-on-Openness-for-NTIA-Process-March-25-2024.pdf\t(“Open\tmodels\tpromote\teconomic\tgrowth\tby\tlowering\tthe\tbarrier\tfor\tinnovators,\tstartups,\tand\tsmall\tbusinesses\tfrom\tmore\tdiverse\tcommunities\tto\tbuild\tand\tuse\tAI.\tOpen\tmodels\talso\thelp\taccelerate\tscientific\tresearch\tbecause\tthey\tcan\tbe\tless\texpensive,\teasier\tto\tfine-tune,\tand\tsupportive\tof\treproducible\tresearch.”). 149\t\tSee\tACT\tAssociation\tComment\t(“For\texample,\thealthcare\ttreatments\tand\tpatient\toutcomes\tstand\tpoised\tto\timprove\tdisease\tprevention\tand\tconditions,\tas\twell\tas\tefficiently\tand\teffectively\ttreat\tdiseases\tthrough\tautomated\tanalysis\tof\tx-rays\tand\tother\tmedical\timaging.”)\tand\tAI\tPolicy\tand\tGovernance\tWorking\tGroup\tat\t5\t(“Making\tfoundation\tmodels\tmore\twidely\taccessible,\twith\tappropriate\tsafeguards,\tcould\tdrive\tinnovation\tin\tresearch\tand\tbusinesscapitalizing\ton\tthe\tpromise\tof\tpublic\tbenefit.\tStudy\tand\tuse\tof\tstate-of-the-art\tAI\tmodels,\tincluding\tLarge\tLanguage\tModels\tand\tother\tmodels\tlike\tAlphaFold,\tmay\tlead\tto\timprovements\tin\tperformance,\tsafety,\tand\tscientific\tbreakthroughs\tacross\tvarious\tdomains.\tThese\tpotential\tbenefits\tcan\tbest\tbe\trealized\tif\tother\tAI\tmodel\tassets,\tsuch\tas\tmodel\ttraining\tdata,\tare\talso\tmade\twidely\tavailable,\tand\tif\tmodels\tare\tnot\tsubject\tto\trestrictive\tlicenses.\tAreas\tthat\tstand\tto\tpotentially\tgain\tfrom\ta\tcommitment\tof\tensuring\tthe\twide\tavailability\tof\tAI\ttools\tand\tsystems\tinclude,\tbut\tare\tnot\tlimited\tto,\tinnovation\tand\tnovel\tapplications\tin\tpublic\thealth,\tbiomedical\tresearch,\tand\tclimate\tscience\tthat\tmight\tbe\tscaled\tin\tthe\tpublic\tinterest.\tAny\tdecision\tto\tconstrain\tthe\tavailability\tof\tdual-use\topen\tfoundation\tmodels\tmust\tcarefully\tweigh\tand\tconsider\tthese\tpotential\tsocietal\tand\teconomic\tbenefits.”). 150\t\tA\tgenerative\tAI\ttool\tto\tinspire\tcreative\tworkers.\t(2024,\tFebruary\t14).\tMIT\tSloan.\thttps://mitsloan.mit.edu/ideas-made-to-matter/a-generative-ai-tool-toinspire-creative-workers.\t151\t\tCriddle,\tC.\t&\tMadhumita\tM.\t(2024,\tMay\t8).\tArtificial\tintelligence\tcompanies\tseek\tbig\tprofits\tfrom\t‘small’\tlanguage\tmodels.\tFinancial\tTimes.\thttps://www. f t.com/content/359a5a31-1ab9-41ea-83aa-5b27d9b24ef9. 59 Dual-Use Foundation Models with Widely Available Model Weights152\t\tAmerican\tCivil\tLiberties\tUnion,\tCenter\tfor\tAmerican\tProgress,\tand\tthe\tLeadership\tConference\ton\tCivil\tand\tHuman\tRights\tComment\tat\t4\t“In\taddressing\tAI’s\trisks\tfor\tcivil\trights,\tsafety,\tand\taccess\tto\topportunity,\tadvocates,\taffected\tcommunities,\tand\tpolicymakers\thave\tchampioned\ta\tnumber\tof\tregulatory\tgoals,\tincluding\tauditing\tand\tassessments,\ttransparency,\tand\texplainability.”;\tHugging\tFace\tComment\tat\t6\t“Maximally\topen\tsystems,\tincluding\ttraining\tdata,\tweights,\tand\tevaluation\tprotocol,\tcan\taid\tin\tidentifying\tflaws\tand\tbiases.\tInsufficient\tdocumentation\tcan\treduce\teffectiveness”;\tGoogle\tComment\tat\t7\t“Openly\tavailable\tmodels\talso\tenable\timportant\tAI\tsafety\tresearch\tand\tcommunity\tinnovation.\tA\tdiverse\tpool\tof\tavailable\tmodels\tensures\tthat\tdevelopers\tcan\tcontinue\tto\tadvance\tcritical\ttransparency\tand\tinterpretability\tevaluations\tfrom\twhich\tthe\tdeveloper\tcommunity\thas\talready\tbenefited.\tFor\texample,\tresearchers\thave\tdemonstrated\ta\tmethod\tfor\treducing\tgender\tbias\tin\tBERT\tembeddings.” 153\t\tFair\tHousing\tTesting\tProgram.\t(2015,\tAugust\t6).\tJustice.gov.\thttps://www.justice.gov/crt/fair-housing-testing-program-1\t(“In\t1991,\tthe\tCivil\tRights\tDivision\testablished\tthe\tFair\tHousing\tTesting\tProgram\twithin\tthe\tHousing\tand\tCivil\tEnforcement\tSection,\twhich\tcommenced\ttesting\tin\t1992.”). 154\t\tOther\tsections\tin\tthis\tReport\talso\tcontain\treferences\tto\tthese\ttopics.\t155\t\tOpenAI’s\tGPT-4,\tfor\texample,\tcost\taround\t$100\tmillion\tUSD\tto\ttrain.\tSee\tKnight,\tW.\t(2023,\tApril\t17).\tOpenAI’s\tCEO\tSays\tthe\tAge\tof\tGiant\tAI\tModels\tIs\tAlready\tOver.\thttps://www.wired.com/story/openai-ceo-sam-altman-the-age-of-giant-ai-models-is-already-over/. 156\t\tBirchler,\tU.,\t&\tBütler,\tM.\t(2007).\tInformation\tEconomics\t(Routledge\tAdvanced\tTexts\tin\tEconomics\tand\tFinance)\t(1st\tEdition). 157\t\tJones,\tR.,\t&\tMendelson,\tH.\t(2011).\tInformation\tGoods\tvs.\tIndustrial\tGoods:\tCost\tStructure\tand\tCompetition.\tManagement\tScience,\t57(1),\t164–176.\thttp:// www.jstor.org/stable/41060707. 158\t\tNix,\tN.,\t&\tet\tal.\t(2024,\tMarch\t10).\tSilicon\tValley\tis\tpricing\tacademics\tout\tof\tAI\tresearch.\thttps://www.washingtonpost.com/technology/2024/03/10/bigtech-companies-ai-research/. 159\t\tLee,\tK.,\t&\tet\tal.\t(2024,\tMarch\t12).\tBuilding\tMeta’s\tGenAI\tInfrastructure.\thttps://engineering.fb.com/2024/03/12/data-center-engineering/building-metasgenai-infrastructure/.\tSee\talso,\tClark,\tJ.\t(2024,\tMarch\t25).\tImport\tAI\t366:\t500bn\ttext\ttokens;\tFacebook\tvs\tPrinceton;\twhy\tsmall\tgovernment\ttypes\thate\tthe\tBiden\tEO.\thttps://jack-clark.net/2024/03/25/import-ai-366-500bn-text-tokens-facebook-vs-princeton-why-small-government-types-hate-the-biden-eo/. 160\t\tHays,\tK.\t(2024,\tJanuary\t19).\tZuck’s\tGPU\tFlex\tWill\tCost\tMeta\tas\tMuch\tas\t18\tBillion\tby\tthe\tend\tof\t2024.\thttps://www.businessinsider.com/mark-zuckerbergai-flex-meta-nvidia-gpus-2024-1. 161\t\tMeta\taims\tto\thave\t350,000\tNVIDIA\tH100\tGPUs\tby\tthe\tend\tof\tthe\tyear.\tIf\teach\tone\tcosts\t$20,000\t(a\tmodest\testimate\taccording\tto\tthe\tBusiness\tInsider\tarticle\tabove),\tthe\ttotal\tcost\twill\tbe\t$7B.\tThis\tdoes\tnot\tinclude\tMeta’s\tother\tcomputing\tresources,\tor\tthe\tmoney\trequired\tfor\tdatasets,\thuman\tresources,\tor\tother\trequirements\tlike\tenergy. 162\t\tSee,\te.g.,\tVipra,\tJ.,\t&\tKorinek,\tA.\t(2023).\tMarket\tconcentration\timplications\tof\tfoundation\tmodels:\tThe\tInvisible\tHand\tof\tChatGPT.\tBrookings\tat\t9-24\t(analyzing\tthe\teconomics\tof\tfoundation\tmodels).\tSee\talso\tCDT\tComment\tat\t5.\tCf.\tEconomic\tReport\tof\tthe\tPresident.\tp.280\t(2024).\tThe\tWhite\tHouse.\thttps://www.whitehouse.gov/wp-content/uploads/2024/03/ERP-2024.pdf.\t(“In\tother\tcases,\thowever,\tsome\tcombination\tof\thigh\tentry\tcosts,\tdata\tavailability,\tand\tnetwork\teffects\tmay\tdrive\tmarkets\ttoward\thaving\tonly\ta\tsmall\tnumber\tof\tplayers.\tMarkets\tfor\tgenerative\tAI\tproducts,\twhich\trequire\thuge\tamounts\tof\tdata\tand\tcomputing\tpower\tto\ttrain,\tmay\tbe\tparticularly\tprone\tto\tthis\tissue,\twith\tsome\teven\tsuggesting\tthat\tsuch\tmarkets\tmay\tnaturally\ttrend\ttoward\tmonopoly[.\t.\t.].”\t(internal\tcitation\tomitted). 163\t\tSee,\tKapoor,\tS.et\tal.,\t(2024).\tOn\tthe\tSocietal\tImpact\tof\tOpen\tFoundation\tModels.\tat\t5.\tArXiv.\thttps://arxiv.org/pdf/2403.07918. 164\t\tSee\tgenerally\tSolaiman,\tI.\t(2023).\tThe\tGradient\tof\tGenerative\tAI\tRelease:\tMethods\tand\tConsiderations.\tHugging\tFace.\thttps://arxiv.org/pdf/2302.04844.\tSee\talso\tHugging\tFace\tComment\tat\t10-15. 165\t\tSee,\te.g.,\tAlliance\tfor\tTrust\tin\tAI\tComment\tat\t5\t(“While\tavailable\tmodel\tweights\tmay\tmake\tit\teasier\tto\tdevelop\tadvanced\tAI,\tthere\tare\tstill\tsignificant\tbarriers\tto\trun\tand\tmodify\tlarge\tor\tadvanced\tmodels.\tIt\tis\tnot\tclear\twhether\tthe\tmodel\tweights\tthemselves\tprovide\tenough\tinformation\tto\tend\tusers\tto\tsignificantly\tchange\twhat\tthey\tcan\tdo\tor\tdevelop\tthemselves.”);\tIntel\tComment\tat\t8\t(“[A]lmost\tall\tinnovation\tin\tAI\tto-date\thas\tbeen\tdue\tto\topenly\tavailable\tinfrastructure\t[.\t.\t.]”\t(beyond\tjust\tmodel\tweights\tto\tinclude\t“architecture\tand\tdataset\ttransparency.”)).\tSee\talso\tRAND\tComment\tat\t2\t(“Whether\t[access\tto\topen\tfoundation\tmodels]\twill\tbe\tenough\tto\tmaintain\ta\tcompetitive\tmarket\tfor\tfoundation\tmodel\tbased\tproducts\tor\tservices\tin\tgeneral\twill\tdepend\ton\tthe\tprice\tto\tdevelop\tand\tthe\tperformance\tof\topen\tmodels\tcompared\twith\tclosed\tmodels\tand\ton\thow\tthe\teconomics\tof\tfine-tuning,\tadapting,\tand\tserving\tfoundation\tmodels\tdiffers\tin\ta\tparticular\tbusiness\tapplication\tbetween\tlarge\tand\tsmall\tcompanies.”)\t(internal\tcitation\tomitted). 166\t\tSee,\te.g.,\tEngine\tComment\tat\t3\t(“Moreover,\tthe\textent\tof\topenness\tmatters.\tWhether\topen\tsource\tAI\tresources,\tfor\texample,\tinclude\tdetailed\tdocumentation,\thave\tpublicly\tavailable\tmodel\tweights,\tor\tlicense-based\trestrictions\tcan\timpact\thow\tuseful\tthose\tresources\tare\tfor\tstartups.\tPolicymakers\tshould\tbe\tvery\tclear-eyed\tabout\tconsequences\tfor\tstartups\tand\tinnovation\tof\tadding\tpolicy-related\tbarriers\tto\tthese\tresources.”);\tPublic\tKnowledge\tComment\tat\t11\t(“Open\tsource\tmodel\tweights,\tcommercially\tavailable\tdata\twarehouses,\tand\tpublic\tcompute\tresources\twould\tenable\tmany\tnew\tmodel\tdevelopers\tto\tuse\tthe\tdata\tto\tdevelop\tand\ttrain\tnew\tmodels.\tIn\taddition,\tfoundation\tmodels\tand\tAPIs\tcould\talso\tbe\topened,\tso\tthat\tdevelopers\thave\treliable\taccess\tto\tthese\tresources.”)\t(internal\tcitation\tomitted);\tACLU\tet\tal.\tComment\tat\t9\t(“The\tpotential\tpromise\tof\t‘open’\tAI\tis\tthat\tit\tmay\tallow\tincreased\tcompetition\tand\tcustomization\tof\tAI\tmodels,\tdisrupting\tthe\tpotential\tconcentration\tdeveloping\tin\tthe\tadvanced\tAI\tmarket.\tHowever,\tthis\tcompetition\twill\tonly\texist\tif\t‘open’\tAI\tmodels\tare\table\tto\tbe\thosted\tat\tthe\tscale\tnecessary\tfor\tsuccess.”)\t(quotation\tmarks\tin\toriginal).\tCf.\tIBM\tComment\tat\t5\t(“The\tmost\tobvious\tbenefit\tof\tan\topen\tecosystem\tis\tthat\tit\tlowers\tthe\tbarrier\tto\tentry\tfor\tcompetition\tand\tinnovation.\tBy\tmaking\tmany\tof\tthe\ttechnical\tresources\tnecessary\tto\tdevelop\tand\tdeploy\tAI\tmore\treadily\tavailable,\tincluding\tmodel\tweights,\topen\tecosystems\tenable\tsmall\tand\tlarge\tfirms\talike,\tas\twell\tas\t60 Dual-Use Foundation Models with Widely Available Model Weightsresearch\tinstitutions,\tto\tdevelop\tnew\tand\tcompetitive\tproducts\tand\tservices\twithout\tsteep,\tand\tpotentially\tprohibitive,\tupfront\tcosts.”).\tSee\talso\tWidder,\tD.,\t&\tet\tal.\t(2023).\tOpen\t(For\tBusiness):\tBig\tTech,\tConcentrated\tPower,\tand\tthe\tPolitical\tEconomy\tof\tOpen\tAI.\tat\t7.\thttps://papers.ssrn.com/sol3/papers. cfm?abstract_id=4543807.\t(“Access\tto\tcompute\tpresents\ta\tsignificant\tbarrier\tto\treusability\tfor\teven\tthe\tmost\tmaximally\t‘open’\tAI\tsystems,\tbecause\tof\tthe\thigh\tcost\tinvolved\tin\tboth\ttraining\tand\trunning\tinferences\ton\tlarge-scale\tAI\tmodels\tat\tscale\t(i.e.\tinstrumenting\tthem\tin\ta\tproduct\tor\tAPI\tfor\twidespread\tpublic\tuse).”)\t(quotation\tmarks\tin\toriginal);\tStrengthening\tand\tDemocratizing\tthe\tU.S.\tArtificial\tIntelligence\tInnovation\tEcosystem:\tAn\tImplementation\tPlan\tfor\ta\tNational\tArtificial\tIntelligence\tResearch\tResource.\tat\tv.\t(2023).\thttps://www.ai.gov/wp-content/uploads/2023/01/NAIRR-TF-Final-Report-2023. pdf.\t(“The\t[National\tAI\tResearch\tResource]\tshould\tcomprise\ta\tfederated\tset\tof\tcomputational,\tdata,\ttestbed,\tand\tsoftware\tresources\tfrom\ta\tvariety\tof\tproviders,\talong\twith\ttechnical\tsupport\tand\ttraining,\tto\tmeet\tthe\tneeds\tof\t[its]\ttarget\tuser\tbase.”).\tCf.\tEconomic\tReport\tof\tthe\tPresident.\tat\t281.\t(2024).\tThe\tWhite\tHouse.\thttps://www.whitehouse.gov/wp-content/uploads/2024/03/ERP-2024.pdf.\t(“Similarly,\tfreely\tavailable\tand\tportable\tdata\tmay\tencourage\ta\tcompetitive\tlandscape\tand\tensure\tthat\tgains\tfrom\tdata\tare\twidely\tdistributed.”). 167\t\tSee,\te.g.,\tACLU\tet\tal.\tComment\tat\t9\t(“Currently,\tthe\tmajor\tcommercial\tcloud\tcomputing\tvendors\tallow\tother\tAI\tmodels,\tincluding\t‘open’\tAI\tmodels,\tto\tbe\thosted\ton\ttheir\tcloud\tcomputing\tservices.\tBut\tthere\tis\tno\trequirement\tfor\tany\tmajor\tcommercial\tcloud\tcomputing\tvendors\tto\tallow\t‘open’\tAI\tmodels\tto\tbe\thosted\ton\ttheir\tservices,\tand\tthe\tpotential\tfor\tself-preferencing\tmay\tmake\tthe\tuse\tof\tnon-native\tAI\tmodels\tmore\tdifficult\tor\texpensive.”)\t(internal\tcitation\tomitted)\t(quotation\tmarks\tin\toriginal).\tOpen\tmodels\talso\tbenefit\tthe\tcloud\tcomputing\tmarket,\tdominated\tby\tAmazon,\tGoogle,\tand\tMicrosoft,\twhich\talso\tshows\tanticompetitive\tand\tcumulative\tadvantage\tfeatures.\tSee\tgenerally,\te.g.,\tNarechania,\tT.,\t&\tSitaraman,\tG.\t(2023).\t\tWorking\tPaper\tNumber\t24-8.\thttps://papers.ssrn.com/sol3/papers.cfm?abstract_id=4597080.\tSee\talso\tPaul,\tK.\t(2023,\tJuly\t18).\tMeta\topens\tAI\tmodel\tto\tcommercial\tuse,\tthrowing\tnascent\tmarket\tinto\tflux.\thttps://www.reuters.com/technology/meta-opens-ai-model-commercial-use-throwing-nascent-market-into-flux-2023-07-18/.\t(“Asked\twhy\tMicrosoft\twould\tsupport\tan\toffering\tthat\tmight\tdegrade\tOpenAI’s\tvalue,\ta\tMicrosoft\tspokesperson\tsaid\tgiving\tdevelopers\tchoice\tin\tthe\ttypes\tof\tmodels\tthey\tuse\twould\thelp\textend\tits\tposition\tas\tthe\tgo-to\tcloud\tplatform\tfor\tAI\twork.”). 168\t\tThe\tOpen\tSource\tDefinition.\t(2024,\tFebruary\t16).\thttps://opensource.org/osd. 169\t\tHoffmann,\tM.,\t&\tet\tal.\t(2024).\tThe\tValue\tof\tOpen\tSource\tSoftware\t(Harvard\tBusiness\tSchool\tStrategy\tUnit\tWorking\tPaper\tNo.\t24-038).\tHarvard\tBusiness\tSchool.\thttps://papers.ssrn.com/sol3/papers.cfm?abstract_id=4693148. 170\t\tSee,\te.g.,\tCDT\tComment\tat\t2-4. 171\t\tBlind,\tK.,\t&\tSchubert,\tT.\t(2023).\tEstimating\tthe\tGDP\teffect\tof\tOpen\tSource\tSoftware\tand\tits\tcomplementarities\twith\tR&D\tand\tpatents:\tEvidence\tand\tpolicy\timplications.\tThe\tJournal\tof\tTechnology\tTransfer,\t49:466–491.\thttps://link.springer.com/article/10.1007/s10961-023-09993-x. 172\t\tWest,\tJ.,\t&\tGallagher,\tS.\t(2006).\tChallenges\tof\topen\tinnovation:\tThe\tparadox\tof\tfirm\tinvestment\tin\topen-source\tsoftware.\t36(3),\t319–331. 173\t\tSee,\te.g.,\tMozilla\tComment\tat\t12\t(“As\tWidder,\tWest,\tand\tWhittaker\thave\targued,\tpromoting\topenness\tin\tAI\talone\tis\tnot\tsufficient\tfor\tcreating\ta\tmore\tcompetitive\tecosystem.\tThere\tare\talso\trisks\tof\topenness\tbeing\tco-opted\tby\tbig\tindustry\tplayers,\tand\ta\tlong\ttrack\trecord\tof\tcompanies\tdrawing\tsignificant\tbenefits\tfrom\topen\tsource\ttechnology\twithout\tre-investing\tinto\tthe\tcommunities\tthat\thave\tdeveloped\tthose\ttechnologies.”),\treferencing\tWidder,\tD.,\t&\tet\tal.\t(2023).\tOpen\t(For\tBusiness):\tBig\tTech,\tConcentrated\tPower,\tand\tthe\tPolitical\tEconomy\tof\tOpen\tAI.\tat\t6.\thttps://papers.ssrn.com/sol3/papers. cfm?abstract_id=4543807.\tSee\talso\tACLU\tet\tal.\tComment\tat\t6\t(“Further\tcompounding\tthe\tcomplexity\taround\t‘open’\tAI\tis\tthe\tfact\tthat\tit\tis\tnot\talways\teasy\tto\tseparate\t‘openness’\tfrom\tthe\tbusiness\tinterests\tof\tlarge\tAI\tdevelopers,\twho\tmay\tbenefit\tfrom\topen\tinnovation\ton\ttheir\tplatforms\tand\tmay\tlater\twithdraw\tcommitments\tto\topenness\tafter\tthe\tbenefits\thave\treached\ta\tcritical\tmass,\tknowing\tthat\tsmaller\tdevelopers\tare\tunlikely\tto\thave\tthe\tresources\tnecessary\tto\tindependently\tcompete.”)\t(quotation\tmarks\tin\toriginal)\t(internal\tcitations\tomitted). 174\t\tPaul,\tK.\t(2023,\tJuly\t18).\tMeta\topens\tAI\tmodel\tto\tcommercial\tuse,\tthrowing\tnascent\tmarket\tinto\tflux.\thttps://www.reuters.com/technology/meta-opens-aimodel-commercial-use-throwing-nascent-market-into-flux-2023-07-18/. 175\t\tYao,\tD.\t(2023,\tJuly\t27).\tMeta\tto\tCharge\tfor\tLlama\t2\tAfter\tAll\t–\tIf\tYou’re\ta\tHyperscaler.\thttps://aibusiness.com/nlp/meta-to-charge-for-llama-2-after-all-ifyou-re-a-hyperscaler. 176\t\tWidder,\tD.,\t&\tet\tal.\t(2023).\tOpen\t(For\tBusiness):\tBig\tTech,\tConcentrated\tPower,\tand\tthe\tPolitical\tEconomy\tof\tOpen\tAI.\tat\t13.\thttps://papers.ssrn.com/sol3/ papers.cfm?abstract_id=4543807. 177\t\tSee\tEngler,\tA.\t(2021,\tAugust\t10).\tHow\topen-source\tsoftware\tshapes\tAI\tpolicy.\thttps://www.brookings.edu/articles/how-open-source-software-shapesai-policy/.\t(“In\tfact,\tfor\tGoogle\tand\tFacebook,\tthe\topen\tsourcing\tof\ttheir\tdeep\tlearning\ttools\t(Tensorflow\tand\tPyTorch,\trespectively),\tmay\thave\tthe\texact\topposite\teffect,\tfurther\tentrenching\tthem\tin\ttheir\talready\tfortified\tpositions.\tWhile\t[open\tsource\tsoftware]\tis\toften\tassociated\twith\tcommunity\tinvolvement\tand\tmore\tdistributed\tinfluence,\tGoogle\tand\tFacebook\tappear\tto\tbe\tholding\ton\ttightly\tto\ttheir\tsoftware.\t[.\t.\t.]\t[T]\tthese\tcompanies\tare\tgaining\tinfluence\tover\tthe\tAI\tmarket\tthrough\tOSS,\twhile\tthe\tOSS\tAI\ttools\tnot\tbacked\tby\tcompanies,\tsuch\tas\tCaffe\tand\tTheano,\tseem\tto\tbe\tlosing\tsignificance\tin\tboth\tAI\tresearch\tand\tindustry.\tBy\tmaking\ttheir\ttools\tthe\tmost\tcommon\tin\tindustry\tand\tacademia,\tGoogle\tand\tFacebook\tbenefit\tfrom\tthe\tpublic\tresearch\tconducted\twith\tthose\ttools,\tand,\tfurther,\tthey\tmanifest\ta\tpipeline\tof\tdata\tscientists\tand\tmachine\tlearning\tengineers\ttrained\tin\ttheir\tsystems.”)\t(internal\thyperlink\tomitted). 178\t\tStaff\tin\tthe\tFederal\tTrade\tCommission\tOffice\tof\tTechnology.\t(2024,\tJuly\t10).\tOn\tOpen-Weights\tFoundation\tModels.\thttps://www.ftc.gov/policy/advocacyresearch/tech-at-ftc/2024/07/open-weights-foundation-models. 179\t\tScharre,\tP.\t(2024,\tMarch\t13).\tFuture-Proofing\tFrontier\tAI\tRegulation.\thttps://www.cnas.org/publications/reports/future-proofing-frontier-ai-regulation. 61 Dual-Use Foundation Models with Widely Available Model Weights180\t\tSee,\tMelton,\tM.\t(2024,\tFebruary).\tGenerative\tAI\tstartup\tLatimer,\tknown\tas\tthe\t“BlackGPT”,\twill\tlaunch\ta\tnew\tbias\tdetection\ttool\tand\tAPI.\tBusiness\tInsider.\thttps://www.businessinsider.com/latimer-ai-api-launch-bias-detection-tools-llm-2024-2. 181\t\tSee,\tRishi\tBommasani\tet\tal.\tComment\tat\t5\t(“Open\tfoundation\tmodels\tpromote\tcompetition\tin\tsome\tlayers\tof\tthe\tAI\tstack.\tGiven\tthe\tsignificant\tcapital\tcosts\tof\tdeveloping\tfoundation\tmodels,\tbroad\taccess\tto\tmodel\tweights\tand\tgreater\tcustomizability\tcan\talso\treduce\tmarket\tconcentration\tby\tenabling\tgreater\tcompetition\tin\tdownstream\tmarkets.\tHowever,\topen\tfoundation\tmodels\tare\tunlikely\tto\treduce\tmarket\tconcentration\tin\tthe\thighly\tconcentrated\tupstream\tmarkets\tof\tcomputing\tand\tspecialized\thardware\tproviders.”)\t(internal\tcitation\tomitted);\tRAND\tComment\tat\t2\t(“Whether\t[access\tto\topen\tfoundation\tmodels]\twill\tbe\tenough\tto\tmaintain\ta\tcompetitive\tmarket\tfor\tfoundation\tmodel\tbased\tproducts\tor\tservices\tin\tgeneral\twill\tdepend\ton\tthe\tprice\tto\tdevelop\tand\tthe\tperformance\tof\topen\tmodels\tcompared\twith\tclosed\tmodels\tand\ton\thow\tthe\teconomics\tof\tfine-tuning,\tadapting,\tand\tserving\tfoundation\tmodels\tdiffers\tin\ta\tparticular\tbusiness\tapplication\tbetween\tlarge\tand\tsmall\tcompanies.”)\t(internal\tcitation\tomitted).\tSee\talso,\tHugging\tFace\tComment\tat\t9\t(“Additionally,\topen-weight\tmodels\thave\tbeen\tcustomized\tand\tadapted\tto\trun\ton\ta\tgreater\tvariety\tof\tinfrastructure,\tincluding\tindividual\tGPUs\tand\teven\tCPUs,\treducing\tpoints\tof\tmarket\tconcentration\twith\tcloud\tproviders\tand\treducing\tcosts\tfor\tprocurement.”)\t(referencing\tGgerganov\t/\tllama. cpp.\t(n.d.).\thttps://github.com/ggerganov/llama.cpp\tand\tHood,\tS.\t(2023,\tDecember\t14).\tllamafile:\tBringing\tLLMs\tto\tthe\tpeople,\tand\tto\tyour\town\tcomputer.\thttps://future.mozilla.org/news/introducing-llamafile/);\tCDT\tComment\tat\t9\t(“Importantly,\tthe\tinnovation\tin\tdeveloping\tsmaller\tand\tmore\tpowerful\tmodels,\toften\tbased\tdirectly\ton\tmuch\tlarger\tmodels,\tis\tnot\tjust\timportant\tin\tterms\tof\tcompetition\tand\tinnovation.\tIt\tis\talso\timportant\tbecause\tsome\tmodels\tsuch\tas\tMistral\t7B\tare\tnow\tsmall\tenough\tto\trun\tlocally\ton\tan\tend-user’s\tlaptop\tor\teven\ta\tphone,\tmitigating\tthe\tneed\tfor\ta\tcloud-based\tprovider\tat\tall.”)\t(internal\tcitation\tomitted). 182\t\tSee,\te.g.,\tRAND\tComment\tat\tat\t2\t(“Open\tfoundation\tmodels\tmay\treduce\tmarket\tconcentration.\tWhen\tsmaller\tactors\tcan\taccess\topen\tfoundation\tmodels,\tthey\tcan\tavoid\tthe\tlarge\texpense\tof\tdeveloping\ttheir\town\tmodels\tand\tcan\ttherefore\tcompete\twith\tlarge\ttech\tcompanies\tin\tadapting\tthe\tfoundation\tmodel\tto\ta\tparticular\tbusiness\tcontext.”)\t(internal\tcitation\tomitted),\tEngine\tComment\tat\t3\t(“Openness\tin\tAI\thelps\talleviate\tcosts\tassociated\twith\tthe\texpensive\tparts\tof\tbuilding\tmodels,\tleaving\tstartups\tto\tfocus\ttheir\tlimited\tresources\ton\ttheir\tcore\tand\tdifferentiating\tinnovation.”);\tThe\tAbundance\tInstitute\tComment\tat\t4\t(“In\tparticular,\tthe\thigh\tcosts\tof\tcompiling\tdata\tand\tpurchasing\tcompute\tto\ttrain\tfoundational\tmodels\tare\ta\tsignificant\tbarrier\tto\tmodel\ttraining.\tSharing\tmodel\tweights\teliminates\tthis\tcost\tbarrier,\tbroadening\taccess\tand\tenabling\tusers\tthat\twould\totherwise\tsimply\tbe\tpriced\tout\tof\tbuilding\ttheir\town\tAI\tstack.”). 183\t\tSee,\te.g.,\ta16z\tComment\tat\t11\t(“Open\tModels\tincrease\tcompetition\tin\tthe\tdevelopment\tand\timprovement\tof\tfoundation\tmodels\tbecause\tthey\tdo\tnot\trestrict\tthe\tuse\tof\tAI\tto\tgatekeeper\tcompanies\twith\tthe\tmost\tmarket\tpower\tor\tresources.\tThis\taccessibility\tincreases\tthe\tprospect\tof\tcompetition\tand\tallows\tfor\tparticipation\tby\tdevelopers\twho\tmay\totherwise\thave\tbeen\tboxed\tout\tof\tworking\twith\tAI\tdue\tto\ttheir\tlacking\tthe\trequisite\taccess\tor\tresources\tthat\tare\tnecessary\tcomponents\tto\tworking\twithin\ta\tclosed\tecosystem.”);\tCSET\tComment\tat\t4\t(“Downloadable\tweights\t[.\t.\t.]\tmay\treduce\tthe\tconcentration\tof\tpower\tin\tthe\tAI\tindustry\tbecause\tthe\toriginal\tdevelopers\tdo\tnot\tcontrol\taccess\tto\tthe\tmodels.”);\tIntel\tComment\tat\t9\t(“Open\tmodel\tweights\talso\tspur\tmore\tstartups\tand\tinnovations,\tenabling\tstartups\tto\tquickly\tprototype\twithout\taccess\tto\timmense\tcapital,\tfostering\ta\tmore\tcompetitive\tlandscape.”).\tCf.\tPublic\tKnowledge\tComment\tat\t10\t(“Dominant\tcompanies\toften\tutilize\tgatekeeper\tpower\tto\tfurther\ttheir\town\tmarket\tpower\tand\tcut\toff\tnew\tentrants\tfrom\tthe\tchance\tto\tcompete.\tOpen\ttechnologies\tmay\tserve\tto\tcounteract\tthis\texclusionary\tconduct\tand\tlower\tbarriers\tto\tentry\tfor\tinnovative,\tup-and-coming\trivals.\tHistorically,\twe’ve\talready\tseen\thow\topen\taccess\tto\ttechnology\tpatents\thad\tcompetitive\tbenefits,\tleading\tto\ta\twellspring\tof\tinnovative\tproducts.”). 184\t\tSee,\te.g.,\tMozilla\tComment\tat\t12\t(“The\tincreased\tavailability\tof\t‘open’\talternatives\tin\tthe\tAI\tmarket\tcan\tsupport\tcompetition\tby\treducing\tswitching\tcosts\tas\trelying\ton\tspecific\tproprietary\tmodel\tAPIs\tor\tplatform\tecosystems\t(like\tthose\toffered\tby\tthe\tleading\tcloud\tservice\tproviders)\tcan\tcreate\tlock-in\teffects\tfor\tcustomers,\tboth\tin\tthe\tprivate\tand\tpublic\tsector.”)\t(quotation\tmarks\tin\toriginal).\tOpen\tmodels\tallow\tcompanies\tto\tswitch\tseamlessly\tbetween\tbaseline\tmodels\twithout\tadded\tcosts.\tProprietary\tmodels\tintroduce\tthe\tthreat\tof\t“lock-in\teffects,”\twhere\ta\tcompany\thas\tbuilt\ta\tproduct\taround\ta\tcertain\tAPI\tor\tprovider\tand\tthen\tcannot\ttransfer\tto\ta\tnew\tmodel\twithout\trebuilding\ttheir\tentire\tproduct.\tMany\ttechnology\tcorporations\thave\testablished\tverticals\twith\tcertain\tcloud\tservice\tproviders\tand\tdata\tcollection\tinfrastructures,\tand\ta\tcompany\tcannot\teasily\texit\tthis\tvertical.\tConversely,\twhen\tcompanies\tbuild\ton\topen\tmodels,\tthey\thave\taccess\tto\tthose\tmodel\tweights\tforever\tand\tcan\tswitch\tbetween\tcloud\tproviders\tand\tother\tvendors\twith\tease. 185\t\tSee\tKapoor,\tS.\t(2024).\tOn\tthe\tSocietal\tImpact\tof\tOpen\tFoundation\tModels.\tat\t5.\tArXiv.\thttps://arxiv.org/pdf/2403.07918. 186\t\tUnited\tNations\tGeneral\tAssembly,\tSeizing\tthe\topportunities\tof\tsafe,\tsecure\tand\ttrustworthy\tartificial\tintelligence\tsystems\tfor\tsustainable\tdevelopment.\tMarch\t11,\t2024.\tA/78/L.49.\thttps://www.undocs.org/A/78/L.49. 187\t\tSee\tACLU\tet\tal.\tComment\tat\t10\t(“As\tseen\tin\tother\ttechnological\tcontexts,\tdiffusing\tmarket\tconcentration,\tespecially\tover\tgateway\tor\tbottleneck\tfacilities,\tcan\tincrease\tthe\tdiversity\tof\tvoices,\tincluding\tfor\tmarginalized\tcommunities.”)\t(internal\tcitation\tomitted);\tGitHub\tComment\tat\t10\t(“An\texpanded\tdeveloper\tbase,\tparticularly\toutside\tof\ta\tsmall\tset\tof\tcompanies\tlocated\tin\ta\tfew\tmajor\ttech\thubs,\tsupports\tdiversity\tof\tidentity\tand\tperspective\tin\tthe\tecosystem.”). 188\t\tSee\tCDT\tComment\tat\t7\t(“[Open\tFoundation\tModels]\tare\talready\tdriving\tinnovation\tacross\tthe\tecosystem\tas\ttens\tor\thundreds\tof\tthousands\tof\tbusinesses\tbegin\tadapting\tmodel\tcapabilities\tto\ttheir\town\tuse\tcases\tand\tcustomer\tneeds\tin\ta\twide\tvariety\tof\tcontexts.”);\tPhase\t2\tat\t3\t(“Making\tfoundation\tmodel\tweights\twidely\tavailable\tlowers\tbarriers\tto\tentry\tand\tenables\ta\tbroader\trange\tof\tcompanies\tto\tdevelop\tAI\tapplications.\tThis\tis\tparticularly\tbeneficial\tfor\tstartups\tand\tsmall\tbusinesses\tthat\tlack\tthe\tresources\tto\tdevelop\tfoundation\tmodels\tfrom\tscratch.\tOpen\tmodels\tlevel\tthe\tplaying\tfield\tand\tensure\tthe\teconomic\tgains\tfrom\tAI\tare\twidely\tdistributed.\tWe\texpect\tthis\tto\tdrive\tcompetition\tand\tinnovation\tin\tsectors\tlike\thealthcare,\teducation,\tand\tmarketing\tas\tmore\tplayers\tare\table\tto\tleverage\tAI\tto\tbuild\tgroundbreaking\tproducts\tand\tservices.”). 189\t\tKapoor,\tS.\tet\tal.,\t(2024).\tOn\tthe\tSocietal\tImpact\tof\tOpen\tFoundation\tModels.\tArXiv.\thttps://arxiv.org/pdf/2403.07918. 62 Dual-Use Foundation Models with Widely Available Model Weights190\t\tSee,\te.g.,\tCTA\tComment\tat\t5\t“Because\t[open\tweight\tmodels]\thave\tlower\tbarriers\tto\tentry\t(e.g.,\tcost,\texpertise),\tthey\tare\tmore\taccessible\tto\tthe\tgeneral\tpublic.\tLeveraging\tinput\tand\tfeedback\tfrom\tthe\tbroader\tAI\tcommunity\tof\tresearchers\tand\tusers\tcan\thelp\tidentify\tand\tmitigate\tbugs,\tbiases,\tand\tsafety\tissues\tthat\tmay\totherwise\tgo\tunnoticed,\tultimately\tleading\tto\tbetter\tperforming\tand\tsafer\tAI\tproducts.\tThis\tlower\tbarrier\tto\tentry\tcan\thelp\tto\tdrive\tAI\tresearch\tand\tdevelopment\tby\tacademics\tor\tother\tsubject\tmatter\texperts,\tenabling\tcommunities\twith\tbespoke\tdatasets\tand\tunique\tneeds\tto\tform\taround\tspecific\tplatforms\tor\tindustry\tsectors.”\t(citing\tElizabeth\tSeger,\tet\tal.\t(Sept.\t29,\t2023).\tOpen-Sourcing\tHighly\tCapable\tFoundation\tModels.\thttps://arxiv. org/pdf/2311.09227.pdf;\tLy,\tJ.\t(2024,\tMarch\t12).\tOpen\tFoundation\tModels:\tImplications\tof\tContemporary\tArtificial\tIntelligence.\tCenter\tfor\tSecurity\tand\tEmerging\tTechnology.\thttps://cset.georgetown.edu/article/open-foundation-models-implications-of-contemporary-artificial-intelligence/. 191\t\tThere\tmay\talso\tbe\tways\tto\tachieve\tsimilar\tbenefits\tto\tresearch\tand\tdevelopment\tthrough\tmethods\tother\tthan\tmaking\tmodel\tweights\twidely\tavailable.\tSee,\te.g.,\tRAND\tComment\tat\t4\t(“Structured\taccess\tis\tan\talternative\tapproach\tthat\tcan\tprovide\tusers\twith\tmany\tof\tthe\tbenefits\tof\tmaking\tfoundation\tmodel\tweights\twidely\taccessible\twhile\treducing\tsome\tof\tthe\trisks.”);\tAnthony\tBarrett\tComment\tat\t15\t(“[B]enefits\tof\tbroad\tindependent\tevaluation\tfor\timproving\tthe\tsafety,\tsecurity,\tand\ttrustworthiness\tof\tAI\tare\tnot\tnecessarily\tbest\tsupported\tby\tmaking\tmodel\tweights\twidely\tavailable.\tThose\tbenefits\tcan\talso\tbe\tachieved\tby\tfacilitating\tsafe\tand\tprotected\tindependent\tresearcher\taccess.”). 192\t\tSee,\te.g.,\tHugging\tFace\tComment\tat\t9\t(“Robust\tinnovation\ton\tboth\tperformance\tand\tsafety\tquestions\trequires\tscientific\trigor\tand\tscrutiny,\twhich\tis\tenabled\tby\topenness\tand\texternal\treproducibility.\tSupporting\tthat\tresearch\trequires\tsharing\tmodels\tto\tvalidate\tfindings\tand\tlower\tthe\tbarrier\tto\tentry\tfor\tparticipation\tgiven\tthe\tgrowing\tresource\tgap\tbetween\tresearchers\tin\tdifferent\tinstitutions.”)\t(internal\tcitations\tomitted);\tCenter\tfor\tDemocracy\t&\tTechnology,\t&\tet\tal.\t(March,\t25,\t2024).\tRE:\tOpenness\tand\tTransparency\tin\tAI\tProvide\tSignificant\tBenefits\tfor\tSociety\tat\t2.\thttps://cdt.org/wp-content/ uploads/2024/03/Civil-Society-Letter-on-Openness-for-NTIA-Process-March-25-2024.pdf.\t(“Open\tmodels\talso\thelp\taccelerate\tscientific\tresearch\tbecause\tthey\tcan\tbe\tless\texpensive,\teasier\tto\tfine-tune,\tand\tsupportive\tof\treproducible\tresearch.”).\tSee\tKapoor,\tS.\tet\tal.,\t(2024).\tOn\tthe\tSocietal\tImpact\tof\tOpen\tFoundation\tModels.\tat\t19.\tArXiv.\thttps://arxiv.org/pdf/2403.07918\t(listing\texamples\tof\tresearch\tdone\tusing\topen\tfoundation\tmodels).\t193\t\tSee,\te.g.,\tRAND\tComment\tat\t3\t(“Making\tfoundation\tmodel\tweights\taccessible\thelps\tuncover\tvulnerabilities,\tbiases,\tand\tpotentially\tdangerous\tcapabilities.\tWith\ta\twider\tset\tof\teyes\texamining\tthese\tmodels,\tthere\tis\ta\thigher\tlikelihood\tof\tidentifying\tand\taddressing\tissues\tthat\tmight\thave\tbeen\toverlooked\tby\tthe\toriginal\tdevelopers,\tas\tis\tthe\tcase\twith\topen-source\tsoftware\tbroadly.\tThis\tscrutiny\tis\tuseful\tfor\tdeveloping\tAI\tsystems\tthat\tare\tsecure,\tfair,\tand\taligned\twith\tsocietal\tvalues.\tThe\tdetection\tand\tmitigation\tof\tbiases\tin\tAI\tmodels,\tfor\tinstance,\tare\tcritical\tsteps\ttoward\tensuring\tthat\tAI\ttechnologies\tdo\tnot\tperpetuate\tor\texacerbate\tsocial\tinequalities.”);\tIBM\tComment\tat\t6\t(‘In\tsome\tcontexts,\tAI\tsafety\tcan\talso\tdepend\ton\tthe\tability\tfor\tdiverse\tstakeholders\tto\tscrutinize\tand\tevaluate\tmodels\tto\tidentify\tany\tvulnerabilities,\tidentify\tundesirable\tbehaviors,\tand\tensure\tthey\tare\tfunctioning\tproperly.\tHowever,\twithout\t‘deep\taccess,’\twhich\tincludes\taccess\tto\tmodel\tweights,\tthese\tevaluations\twill\tbe\tseverely\tlimited\tin\ttheir\teffectiveness.”)\t(internal\tcitation\tomitted);\tCDT\tComment\tat\t10-14\t(explaining\tthe\t“black-box”\tmethods\tof\tauditing\tfor\tclosed\tfoundation\tmodels\tversus\t“white-box”\tmethods\tof\tauditing\tfor\topen\tfoundation\tmodels).\tCf.\tEngler,\tA.\t(2021,\tAugust\t10).\tHow\topen-source\tsoftware\tshapes\tAI\tpolicy.\thttps://www.brookings.edu/articles/how-open-sourcesoftware-shapes-ai-policy/\t(“Similarly,\topen-source\tAI\ttools\tcan\tenable\tthe\tbroader\tand\tbetter\tuse\tof\tethical\tAI.\tOpen-source\ttools\tlike\tOSS\tlike\tIBM’s\tAI\tFairness\t360,\tMicrosoft’s\tFairlearn,\tand\tthe\tUniversity\tof\tChicago’s\tAequitas\tease\ttechnical\tbarriers\tto\tdetecting\tand\tmitigating\tAI\tbias.\tThere\tare\talso\topensource\ttools\tfor\tinterpretable\tand\texplainable\tAI,\tsuch\tas\tIBM’s\tAI\tExplainability\t360\tor\tChris\tMolnar’s\tinterpretable\tmachine\tlearning\ttool\tand\tbook,\twhich\tmake\tit\teasier\tfor\tdata\tscientists\tto\tinterrogate\tthe\tinner\tworkings\tof\ttheir\tmodels.”). 194\t\tSee\tGitHub\tComment\tat\t9\t(“To-date,\tresearchers\thave\tcredited\t[open\tsource\tand\twidely\tavailable\tAI]\tmodels\twith\tsupporting\twork\tto\t[.\t.\t.]\tadvance\tthe\tefficiency\tof\tAI\tmodels\tenabling\tthem\tto\tuse\tless\tresources\tand\trun\ton\tmore\taccessible\thardware.”)\t(citing\tTim\tDettmers,\tet\tal.,\t“QLoRA:\tEfficient\tFinetuning\tof\tQuantized\tLLMs,”\tArXiv,\tMay\t23,\t2023,\thttps://arxiv.org/abs/2305.14314\tand\tits\tassociated\trepository\tat\tArtidoro\t/\tqlora.\t(n.d.).\thttps://github. com/artidoro/qlora)..\thttps://doi.org/10.48550/arXiv.2202.07646). 195\t\tSee,\te.g.,\tIntel\tComment\tat\t8-9\t(“Open\tmodel\tweights\tare\tlikely\tgoing\tto\taid\tresearchers\tto\tfind\timpactful\tand\tbeneficial\tuse\tcases\tof\tAI\tthat\twill\tbe\toverlooked\tby\tnarrow\tand\timmediate\tcommercial\tinterests\tof\tproprietary\tmodel\tvendors.\tAn\texample\tof\tthis\tis\tapplying\tleading-edge\tAI\tprinciples\tto\topen\tscientific\tproblems.”);\tOTI\tComment\tat\t16\t(“One\tof\tthe\tkey\tbenefits\tof\ta\thealthy\tecosystem\tcharacterized\tby\ta\tprevalence\tof\topen\tmodels\tis\tthat\tmany\tpeople\tcan\tlearn\thow\tthe\ttechnology\tworks.\tThis\tenables\ttechnologists\tand\tcommunity\tleaders\tto\tpartner\tin\tways\tthat\tare\ttailored\tto\taddress\tspecific\tcommunity\tneeds\tand\timplement\tcommunity-driven\tsolutions.\tRelatedly,\topen-source\tprojects\tcan\talso\tbe\tused\tto\tfill\ttechnological\tgaps\tthat\taren’t\tbeing\tmet\tin\tthe\tprivate\tsector.”)\t(internal\tcitations\tomitted). 196\t\tSee\tMLCommons\tComment\tat\t3\t(“Models\twith\topen\tweights\thave\tplayed\ta\tcentral\trole\tin\tdeveloping\twidely\ttrusted\tbenchmarks\tthat\thave\tbeen\tused\tto\tevaluate\tand\tmeasure\tAI\tmodels,\tand\tin\tdoing\tso\thave\thelped\tdrive\tprogress\tin\tAI.\tGLUE,\tBigBench,\tHarness,\tHELM\tand\topenCLIP\tBenchmark\tare\tall\texamples\tof\twidely\tused\tbenchmarks\tthat\thave\thelped\tresearchers\tand\tdevelopers\tmeasure\tprogress\tin\tthe\tdevelopment\tof\tAI\tmodels.”)\t(internal\tcitations\tomitted). 197\t\tSee\tEleutherAI\tComment\tat\t24\t(“Even\tfor\tresearchers\tin\tindustrial\tlabs\tsuch\tas\tGoogle,\topen\tmodels\tcan\tenable\tresearch\ton\tmodel\tsafety\tthat\twould\tnot\totherwise\tbe\tpossible:\tin\tan\tearlier\trevision\tof\tQuantifying\tMemorization\tAcross\tNeural\tLanguage\tModels,\tCarlini\tet\tal.\tstate\tthat\ttheir\tresearch\ton\tharmful\tmemorization\tin\tlanguage\tmodels\t“would\tnot\thave\tbeen\tpossible\twithout\tEleutherAI’s\tcomplete\tpublic\trelease\tof\tThe\tPile\tdataset\tand\ttheir\tGPTNeo\tfamily\tof\tmodels.”)\t(internal\tcitations\tomitted).\tSee\talso\tZou,\tA.\t(2023).\tUniversal\tand\tTransferable\tAdversarial\tAttacks\ton\tAligned\tLanguage\tModels.\thttps://arxiv.org/abs/2307.15043. 198\t\tSee\tKapoor,\tS.\tet\tal.,\t(2024).\tOn\tthe\tSocietal\tImpact\tof\tOpen\tFoundation\tModels.\tat\t4.\tArXiv.\thttps://arxiv.org/pdf/2403.07918.\t(referencing\tresearch\tthat\t63 Dual-Use Foundation Models with Widely Available Model Weightsrequires\tno\tsafety\tfilters).\tSee\talso\tCDT\tComment\tat\t9\t(“Furthermore,\t[open\tfoundation\tmodels]\tenable\ta\tvariety\tof\tAI\tresearch\tnot\tenabled\tby\tclosed\tfoundation\tmodels,\tincluding\tresearch\taround\tAI\tinterpretability\tmethods,\tsecurity,\tmodel\ttraining\tand\tinference\tefficiency,\tand\tthe\tpublic\tdevelopment\tof\trobust\twatermarking\ttechniques.”)\t(listing\texamples)\t(internal\tcitations\tomitted).\tSee\talso\tKapoor,\tS.,\t&\tNarayanan,\tA.\t(2023,\tMarch\t22).\tOpenAI’s\tpolicies\thinder\treproducible\tresearch\ton\tlanguage\tmodels.\thttps://www.aisnakeoil.com/p/openais-policies-hinder-reproducible.\tTo\tbe\tclear,\tthe\tbenefits\tin\tthis\tspace\tare\tnot\ta\tzero-sum\tgame.\tOne\tmay\tneed\taccess\tto\tboth\topen\tweight\tmodels\tand\t“closed”\tfoundation\tmodels.\tSee,\te.g.\tMLCommons\tComment\tat\t3\t(describing\tthe\tlimitations\tof\trelying\tsolely\ton\tmodels\twith\topen\tweights\tor\tmodels\twith\tclosed\tweights\tto\tevaluate\tmodels\tand\turging\tsimultaneous\tuse).\t199\t\tSee,\te.g.,\tCSET\tComment\tat\t11\t(“Most\tcurrent\t[Biological\tDesign\tTools]\tare\topen\tmodels\tdeveloped\tby\tacademic\tlabs.\tThe\tlife\tsciences\tcommunity\tplaces\ta\thigh\tvalue\ton\tscientific\ttransparency\tand\topenness,\tand\ttends\tto\tfavor\topen\tsharing\tof\tresources\t[.\t.\t.]\tShifting\taway\tfrom\tthe\topen\tsharing\tof\tmodel\tweights\twould\talso\trequire\tadditional\tresources,\tas\tmany\tacademic\tresearchers\tdo\tnot\thave\tthe\ttime,\tfunding,\tand\tinfrastructure\tto\tset\tup\tand\tmaintain\tan\tAPI.”)\t(internal\thyperlink\tomitted). 200\t\tSee,\te.g.,\tMiller,\tK.\t(2024,\tMarch\t12).\tOpen\tFoundation\tModels:\tImplications\tof\tContemporary\tArtificial\tIntelligence.\thttps://cset.georgetown.edu/article/ open-foundation-models-implications-of-contemporary-artificial-intelligence/.\t(“Actors\tmay\topt\tto\tuse\topen\tmodels\tinstead\tof\tpaying\tfor\taccess\tto\tclosed\tmodels,\twhich\tmay\treduce\tthe\trevenue\tof\tdevelopers\tand\tdisincentivize\tinvestments\tin\tcapital-intensive\tR&D.”). 201\t\tSee,\te.g.,\tCSET\tComment\tat\t10\t(“More\tresearch\tis\tneeded\tto\tdetermine\twhat\ttypes\tof\tresearch\tare\tenabled\tby\topen\tweights,\tand\thow\tthat\tmay\tallow\tmore\tentrants\tinto\tthe\tmarket.\tMany\tprospective\tentrants\tmay\tlack\tresources,\tand\tit\tis\tunclear\tthe\textent\tto\twhich\tresource\tconstraints\tmay\tlimit\tthe\tbenefits\tof\topen\tmodels\tto\tR&D.\tActors\tmay\tlack\tthe\tdata\tto\tfine-tune\topen\tmodels,\tor\tlack\tthe\tcompute\tto\tuse\tor\texperiment\twith\topen\tmodels\trigorously\tand\tat\tscale\t(although\tresources\tprovided\tthrough\tthe\tNAIRR\tpilot\tmay\thelp\talleviate\tresource\tconstraints).”)\t(internal\thyperlinks\tomitted). 202\t\tSee\tKapoor,\tS.\tet\tal.,\t(2024).\tOn\tthe\tSocietal\tImpact\tof\tOpen\tFoundation\tModels.\tat\t4.\tArXiv.\thttps://arxiv.org/pdf/2403.07918. 203\t\tSee\tKapoor,\tS.\tet\tal.,\t(2024).\tOn\tthe\tSocietal\tImpact\tof\tOpen\tFoundation\tModels.\tat\t4.\tArXiv.\thttps://arxiv.org/pdf/2403.07918.\tThese\tresearchers\talso\tnote\tthat\t“new\tresearch\tdirections\tsuch\tas\tmerging\tmodels\tmight\tallow\topen\tfoundation\tmodel\tdevelopers\tto\treap\tsome\tof\tthese\tbenefits\t(akin\tto\topen\tsource\tsoftware).”\t(internal\tcitation\tomitted). 204\t\tSee\tKleinberg,\tJ.,\t&\tRaghavan,\tM.\t(2020).\tAlgorithmic\tmonoculture\tand\tsocial\twelfare.\tProceedings\tof\tthe\tNational\tAcademy\tof\tSciences\tof\tthe\tUnited\tStates\tof\tAmerica,\t118(22)\tat\t1.\thttps://www.pnas.org/doi/epdf/10.1073/pnas.2018340118. 205\t\tSee\tgenerally\tid.\tKleinberg\tand\tRaghavan\thighlight\tseveral\tconcerns\twith\talgorithmic\tmonoculture:\t1)\trisk\tof\tsevere\tharm\tin\tmonoculture\tsystems\tdue\tto\tunexpected\tshocks,\tand\t2)\tdecrease\tin\tdecision-making\tquality\tacross\tthe\tboard.\tSee\talso,\te.g.,\ta16z\tComment\tat\t20\t(“Algorithmic\tmonocultures\tresulting\tfrom\treliance\ton\ta\tfew\tClosed\tModels\tcan\tcreate\tresilience\tproblems\tand\tgenerate\tsystemic\trisk.\tIf\tthose\tmodels\tare\tcompromised,\tthe\timpacts\tcould\tbe\twidespread\tand\tpervasive.”).\tCf.\tVipra,\tJ.,\t&\tKorinek,\tA.\t(2023).\tMarket\tconcentration\timplications\tof\tfoundation\tmodels:\tThe\tInvisible\tHand\tof\tChatGPT.\tat\t25.\tBrookings.\t(“Foundation\tmodels\twill\tlikely\tbe\tintegrated\tinto\tproduction\tand\tdelivery\tprocesses\tfor\tgoods\tand\tservices\tacross\tmany\tsectors\tof\tthe\teconomy.\tWe\tcan\timagine\tone\tfoundation\tmodel\tin\tits\tfine-tuned\tversions\tpowering\tdecision-making\tprocesses\tin\tsearch,\tmarket\tresearch,\tcustomer\tservice,\tadvertising,\tdesign,\tmanufacturing,\tand\tmany\tmore.\tIf\tfoundation\tmodels\tare\tintegrated\tinto\ta\tgrowing\tnumber\tof\teconomic\tactivities,\tthen\twidespread,\tcross-industrial\tapplications\tmean\tthat\tany\terrors,\tvulnerabilities,\tor\tfailures\tin\ta\tfoundation\tmodel\tcan\tthreaten\ta\tsignificant\tamount\tof\teconomic\tactivity,\tproducing\tthe\trisk\tof\tsystemic\teconomic\teffects.”). 206\t\tSee,\te.g.,\tCDT\tComment\tat\t5\t(“[W]hen\tmany\tdifferent\tdecisionmakers\tand\tservice\tproviders\trely\ton\tthe\tsame\tsystems,\tthere\tcan\tbe\ta\ttrend\ttoward\t‘algorithmic\tmonoculture’\twhereby\tsystemic\texclusion\tof\tindividuals\tor\tgroups\tin\tAI-driven\tdecisionmaking\toccurs\tacross\tthe\tecosystem”)\t(citing\tRishi\tBommasani\tet\tal.,\t“Picking\ton\tthe\tSame\tPerson:\tDoes\tAlgorithmic\tMonoculture\tLead\tto\tOutcome\tHomogenization?,”\tArXiv,\tNovember\t25,\t2022,\thttps:// arxiv.org/abs/2211.13972.\t[perma.cc/F7JB-3AK3]). 207\t\tSee,\te.g.,\tMozilla\tComment\tat\t12\t(“Additionally,\tconcentrating\tcutting-edge\tresearch\tin\tever-fewer\tresearch\tlabs\tmay\talso\texacerbate\tphenomena\tlike\talgorithmic\tmonoculture\tand\tentrench\t(or\tincrease\tthe\t‘stickiness’\tof)\texisting\ttechnological\tparadigms\tat\tthe\texpense\tof\tpursuing\tnew\tresearch\tdirections)\t(quotation\tmarks\tin\toriginal),\tciting\tFishman,\tN.,\t&\tHancox-Li,\tL.\t(2022).\tShould\tattention\tbe\tall\twe\tneed?\tThe\tepistemic\tand\tethical\timplications\tof\tunification\tin\tmachine\tlearning.\thttps://dl.acm.org/doi/abs/10.1145/3531146.3533206.\tand\tHooker,\tS.\t(2020).\tThe\tHardware\tLottery.\tArXiv.\thttps://arxiv.org/abs/2009.06489.\tSee\talso\tFishman,\tN.,\t&\tHancox-Li,\tL.\t(2022).\tShould\tattention\tbe\tall\twe\tneed?\tThe\tepistemic\tand\tethical\timplications\tof\tunification\tin\tmachine\tlearning.\tat\t14.\thttps://dl.acm.org/doi/abs/10.1145/3531146.3533206. 208\t\tFaverio,\tM.\tand\tTyson,\tA.\t(2023).\tWhat\tthe\tdata\tsays\tabout\tAmericans’\tviews\tof\tartificial\tintelligence.\tPew\tResearch\tCenter.\thttps://www.pewresearch.org/ short-reads/2023/11/21/what-the-data-says-about-americans-views-of-artificial-intelligence/.\t209\tSartori,\tL.\tand\tTheodorou,\tA\t(2022).\tA\tsociotechnical\tperspective\tfor\tthe\tfuture\tof\tAI:\tnarratives,\tinequalities,\tand\thuman\tcontrol.\tEthics\tand\tInformation\tTechnology,\tVol\t24,\t4.\thttps://link.springer.com/10.1007/s10676-022-09624-3. 210\t\tSee,\te.g.,\tUber\tComment\tat\t2\t(“...open-source\tmodels\tcreate\ta\tmore\tlevel\tplaying\tfield\tand\tlower\tbarriers\tto\tentry\tfor\tAI\tuse,\tensuring\tthat\tmore\tindividuals\tand\torganizations\tcan\taccess\tand\timprove\tupon\texisting\ttechnology.”). 211\t\tVerma,\tP.\t&\tZakrzewski,\tC.\t(April\t23,\t2023).\tAI\tdeepfakes\tthreaten\tto\tupend\tglobal\telections.\tNo\tone\tcan\tstop\tthem.\tWashington\tPost.\thttps://www. washingtonpost.com/technology/2024/04/23/ai-deepfake-election-2024-us-india/. 64 Dual-Use Foundation Models with Widely Available Model Weights212\t\tSinger,\tN.\t(April\t8,\t2023).\tTeen\tGirls\tConfront\tan\tEpidemic\tof\tDeepfake\tNudes\tin\tSchools.\tNew\tYork\tTimes.\thttps://www.nytimes.com/2024/04/08/ technology/deepfake-ai-nudes-westfield-high-school.html. 213\t\tSadok,\tH.,\tSakka,\tF.\t&\tEl\tMaknouzi,\tM.\t(2022).\tArtificial\tintelligence\tand\tbank\tcredit\tanalysis:\tA\tReview.\tCogent\tEconomics\tFinance,\t10(1).\thttps://doi.org/1 0.1080/********.2021.2023262. 214\t\tJuhn,\tY.\tet\tal\t(2022).\tAssessing\tsocioeconomic\tbias\tin\tmachine\tlearning\talgorithms\tin\thealth\tcare:\ta\tcase\tstudy\tof\tthe\tHOUSES\tindex.\tJournal\tof\tthe\tAmerican\tMedical\tInformatics\tAssociation,\t29(7),\t1142-1151.\thttps://doi.org/10.1093/jamia/ocac052. 215\t\tPolonski,\tV.\t(2018).\tAI\tis\tconvicting\tcriminals\tand\tdetermining\tjail\ttime,\tbut\tis\tit\tfair?\tWorld\tEconomic\tForum:\tEmerging\tTechnologies.\thttps://www. weforum.org/agenda/2018/11/algorithms-court-criminals-jail-time-fair/. 216\t\tSee,\te.g.,\tHugging\tFace\tComment\tat\t10\t(“In\tmost\tcases,\tthe\trisks\tassociated\twith\topen-weight\tmodels\tare\tbroadly\tsimilar\tto\tany\tother\tpart\tof\ta\tsoftware\tsystem\t(with\tor\twithout\tAI\tcomponents),\tand\tare\tsimilarly\tcontext-dependent.”)\t(citations\tand\temphasis\tomitted);\tOpenAI\tComment\tat\t3\t(“As\tAI\tmodels\tbecome\teven\tmore\tpowerful\tand\tthe\tbenefits\tand\trisks\tof\ttheir\tdeployment\tor\trelease\tbecome\tgreater,\tit\tis\talso\timportant\tthat\twe\tbe\tincreasingly\tsophisticated\tin\tdeciding\twhether\tand\thow\tto\tdeploy\ta\tmodel.\tThis\tis\tparticularly\ttrue\tif\tAI\tcapabilities\tcome\tto\thave\tsignificant\timplications\tfor\tpublic\tsafety\tor\tnational\tsecurity.\tThe\tfuture\tpresence\tof\tsuch\t‘catastrophic’\trisks\tfrom\tmore\tadvanced\tAI\tsystems\tis\tinherently\tuncertain,\tand\tthere\tis\tscholarly\tdisagreement\ton\thow\tlikely\tand\thow\tsoon\tsuch\trisks\twill\tarise.”)\t(quotation\tmarks\tin\toriginal);\tMozilla\tComment\tat\t11\t(“There\tis\tso\tmuch\tunknown\tabout\tthe\tbenefits\tof\tAI,\tand\tpolicymakers\tmust\tnot\tignore\tthis.”);\tMicrosoft\tComment\tat\t15\t(“Moreover,\teven\twhen\tmodel\tand\tapplication\tdevelopers\ttake\tall\treasonable\tprecautions\tto\tassess\tand\tmitigate\trisks,\tmitigations\twill\tfail,\tunmitigated\trisks\twill\tbe\trealized,\tand\tunknown\trisks\twill\temerge.\tThese\trisks\tcould\trange\tfrom\tgenerating\tharmful\tcontent\tin\tresponse\tto\ta\tmalicious\tprompt\tto\tthe\tintentional\texfiltration\tof\tan\tadvanced\tAI\tmodel\tby\ta\tnation\tstate\tactor.”).\tCf.\tJHU\tComment\tat\t9\t(“Given\tthe\tuncertain\tnature\tof\tcurrent\tand\tfuture\topen\tmodel\tcapabilities,\tand\tthe\timportance\tof\topen\tsoftware,\twe\tare\tnot\tsuggesting\tthat\tthe\tDOC\tshould\timpose\texport\tcontrols\ton\tdual-use\tfoundation\tmodels\ttoday.\tRather,\tthe\trisks\tposed\tby\topen,\tbiologically\tcapable\tdual-use\tfoundation\tmodels\tare\tgrave\tenough\tfor\tthe\tUS\tgovernment\tto\tprepare\tsuch\tpolicy\toptions\tso\tthey\tcan\tbe\tdeployed\twhen\tand\tif\tthey\tbecome\trelevant.”). 217\t\tWeb’s\tinventor\tgets\ta\tknighthood.\t(December\t31,\t2003).\tBBC\tNews.\thttp://news.bbc.co.uk/2/hi/technology/3357073.stm. 218\t\tCai,\tZ.\tet\tal.\t(2023).\tAssociations\tBetween\tProblematic\tInternet\tUse\tand\tMental\tHealth\tOutcomes\tof\tStudents\t:\tA\tMeta-analytic\tReview.\tQuantitative\tReview\t8(45).\thttps://doi.org/10.1007/s40894-022-00201-9;\tKatz,\tL.\t(June\t18,\t2020).\tHow\ttech\tand\tsocial\tmedia\tare\tmaking\tus\tfeel\tlonelier\tthan\tever.\tCNET.\thttps://www.cnet.com/culture/features/how-tech-and-social-media-are-making-us-feel-lonelier-than-ever/. 219\t\tNestor\tMaslej,\tet\tal.\tArtificial\tIntelligence\tIndex\tReport\t2024.\t(2024\tApril).\thttps://aiindex.stanford.edu/wp-content/uploads/2024/04/HAI_2024_AI-IndexReport.pdf at 14 (“Between 2010 and 2022, the total number of AI publications nearly tripled, rising from approximately 88,000 in 2010 to more than 240,000 in 2022.”). 220  Nestor Maslej, et al. Artificial Intelligence Index Report 2024. (2024 April). https://aiindex.stanford.edu/wp-content/uploads/2024/04/HAI_2024_AI-IndexReport.pdf at 46 (“Until 2014, academia led in the release of machine learning models. Since then, industry has taken the lead.”). 221 Marchant, G. (2020). Governance of Emerging Technologies as a Wicked Problem. Vanderbilt Law Review 73(6). https://scholarship.law.vanderbilt. edu/vlr/vol73/iss6/8/; Holtel, S. (2016). Artificial Intelligence Creates a Wicked Problem for the Enterprise. Procedia Computer Science 99, 171-180. https://doi.org/10.1016/j.procs.2016.09.109; Also see generally, Head, B. & Alford, J. Wicked Problems: Implications for Public Policy and Management. Administration & Society 47(6). https://doi.org/10.1177/0095399713481601; Walker, W., Marchau, V. & Swanson, D. (2010). Addressing deep uncertainty using adaptive policies: Introduction to section 2. Technological Forecasting and Social Change 77(6), 917-923. https://doi.org/10.1016/j. techfore.2010.04.004. 222\t\tThe\tNational\tArtificial\tIntelligence\tAdvisory\tCommittee\t(NAIAC)\t(2023).\tRECOMMENDATIONS:\tGenerative\tAI\tAway\tfrom\tthe\tFrontier.\thttps://ai.gov/wpcontent/uploads/2023/11/Recommendations_Generative-AI-Away-from-the-Frontier.pdf;\tKwakkel,\tJ.,\tWalker,\tW.,&\tHaasnoot,\tM.\t(2016).\tCoping\twith\tthe\tWickedness\tof\tPublic\tPolicy\tProblems:\tApproaches\tfor\tDecision\tMaking\tunder\tDeep\tUncertainty.\tJournal\tof\tWater\tResources\tPlanning\tand\tManagement\t142(3).\thttps://doi.org/10.1061/(ASCE)WR.1943-5452.0000626. 223\tHoltel,\tS.\t(2016).\tArtificial\tIntelligence\tCreates\ta\tWicked\tProblem\tfor\tthe\tEnterprise.\tProcedia\tComputer\tScience\t99,\t171-180.\thttps://doi.org/10.1016/j. procs.2016.09.109;\tBerente,\tN.,\tKormylo,\tC.,\t&\tRosenkranz,\tC.\t(2024).\tTest-Driven\tEthics\tfor\tMachine\tLearning.\tCommunications\tof\tthe\tACM\t67(5),\t45-49.\thttps://cacm.acm.org/opinion/test-driven-ethics-for-machine-learning/. 224\t\tLiu,\tH.\t&\tMaas,\tM.\t(2021).\t‘Solving\tfor\tX?’\tTowards\ta\tProblem-Finding\tFramework\tto\tGround\tLong-Term\tGovernance\tStrategies\tfor\tArtificial\tIntelligence.\tFutures\t126(22).\thttps://doi.org/10.1016/j.futures.2020.102672. 225\t\tMaier,\tH.R.\tet\tal\t(2016).\tAn\tuncertain\tfuture,\tdeep\tuncertainty,\tscenarios,\trobustness\tand\tadaptation\t:\tHow\tdo\tthey\tfit\ttogether?\tEnvironmental\tModelling\t&\tSoftware\t81,\t154-164.\thttps://doi.org/10.1016/j.envsoft.2016.03.014;\tRAND.\tRobust\tDecision\tMaking.\tWater\tPlanning\tfor\tthe\tUncertain\tFuture.\thttps:// www.rand.org/pubs/tools/TL320/tool/robust-decision-making.html. 226\t\tHaasnoot,\tM.,\tKwakkel,\tJ.\tH.,\tWalker,\tW.\tE.,\t&\tter\tMaat,\tJ.\t(2013).\tDynamic\tadaptive\tpolicy\tpathways:\tA\tmethod\tfor\tcrafting\trobust\tdecisions\tfor\ta\tdeeply\tuncertain\tworld.\tGlobal\tEnvironmental\tChange,\t23(2),\t485–498.\thttps://doi.org/10.1016/j.gloenvcha.2012.12.006. 65 Dual-Use Foundation Models with Widely Available Model Weights227\t\tRand\tComment\tat\t4\t(“The\tmost\tcommon\tapproach\tto\tstructured\taccess\tis\tto\tcreate\tflexible\tapplication\tprogramming\tinterfaces\t(APIs)\tthat\tallow\tresearchers,\tsmall\tbusinesses,\tor\tthe\tpublic\tto\taccess\tthe\tmodel.”). 228\t\tAnthony\tBarrett\tComment\tat\t3\t(“Foundation\tmodel\tdevelopers\tthat\tplan\tto\tprovide\tdownloadable,\tfully\topen,\tor\topen\tsource\taccess\tto\ttheir\tmodels\tshould\tfirst\tuse\ta\tstaged-release\tapproach\t(e.g.,\tnot\treleasing\tparameter\tweights\tuntil\tafter\tan\tinitial\tsecured\tor\tstructured\taccess\trelease\twhere\tno\tsubstantial\trisks\tor\tharms\thave\temerged\tover\ta\tsufficient\ttime\tperiod),\tand\tshould\tnot\tproceed\tto\ta\tfinal\tstep\tof\treleasing\tmodel\tparameter\tweights\tuntil\ta\tsufficient\tlevel\tof\tconfidence\tin\trisk\tmanagement\thas\tbeen\testablished,\tincluding\tfor\tsafety\trisks\tand\trisks\tof\tmisuse\tand\tabuse.”)\t(internal\tcitation\tomitted). 229\t\tJohns\tHopkins\tCenter\tfor\tHealth\tSecurity\tat\t6\t(“…none\tof\tthe\tsmall\tstudies\tin\tthe\tfield\tso\tfar\thave\tevaluated\thow\tmuch\tdual-use\tfoundation\tmodels\tpurposefully\ttrained\ton\trelevant\tdata\t(eg,\tvirology\tliterature)\twill\tmarginally\timprove\tbioweapons\tdevelopment\tor\tassessed\tthe\tinteraction\tbetween\tLLMs\tand\tBDTs.\t24\tNor,\tto\tour\tknowledge,\thave\tthere\tbeen\tany\tpublished\tevaluations\tof\tthe\tmarginal\tbenefit\tBDTs\tlike\tEvo\tor\tRFdiffusion\tcould\tplay\tin\tbioweapons\tdesign.”). 230\t\tMozur,\tP.\tet\tal.\t(2024,\tFebruary\t21).\tChina’s\tRush\tto\tDominate\tA.I.\tComes\tWith\ta\tTwist:\tIt\tDepends\ton\tU.S.\tTechnology.\tNYTimes.\thttps://www.nytimes. com/2024/02/21/technology/china-united-states-artificial-intelligence.html.\t231\tNational\tAssociation\tof\tManufacturers\tComment\tat\t2\t(“The\tavailability\tof\tmodel\tweights\tallows\tindependent\texamination\tof\ta\tmodel\tto\tensure\tit\tis\tfit\tfor\tpurpose\tand\tto\tidentify\tand\tmitigate\tits\tvulnerabilities.”).\tAt\tthe\tsame\ttime,\tthe\tbenefit\tof\ttransparency\tmay\tbe\trelative\tto\tthe\tavailability\tof\tother\tcomponents\tand\tresources.\tSee,\te.g.,\tCSET\tComment\tat\t16\t(“Models\twith\tpublicly\tavailable\tweights\tfit\talong\ta\tspectrum\tof\topenness,\tand\twhere\tthey\tfit\tdepends\ton\tthe\taccessibility\tof\ttheir\tcomponents\t[.\t.\t.]\tMore\tresearch\tis\tneeded\tto\tgauge\thow\tdifferent\tdegrees\tof\taccess\tand\ttransparency\tcan\timpact\tthe\tability\tto\tscrutinize\tor\tevaluate\topen\tmodels.\tFor\texample,\tmany\topen\tmodel\tcome\twith\tdocumentation\tand\tmodel\tcards,\tbut\tthe\tlevel\tof\tdetail\tin\tthese\tdocuments\tcan\tvary\tdramatically,\tand\tthey\tcan\tenable\t(or\tnot\tenable)\tdifferent\tdegrees\tof\tevaluation.”);\tDatabricks\tComment\tat\t10\t(“Making\tthe\tmodel\tcode\twidely\tavailable\tin\taddition\tto\tthe\tmodel\tweights\tprovides\tthe\tbenefits\tof\tincremental\ttransparency\tin\tevaluation\tthe\tmodel[.\t.\t.].”). 232\t\tSee,\te.g.,\tEleutherAI\tInstitute\tComment\tat\t24\t(“Open-weights\tmodels\tallow\tmore\tresearchers\tthan\tjust\tthe\tsmall\tnumber\tof\tat\tindustry\tlabs\tto\tinvestigate\thow\tto\timprove\tmodel\tsafety,\timproving\tthe\tbreadth\tand\tdepth\tof\tmethods\tthat\tcan\tbe\texplored,\tand\talso\tallows\tfor\ta\twider\tdemographic\tof\tresearchers\tor\tauditors\tof\tsafety.”);\tDatabricks\tComment\tat\t5\t(“The\tbiggest\trisks\tDatabricks\tsees\tare\tthe\trisks\tthat\twould\tbe\tcreated\tby\tprohibiting\tthe\twide\tavailability\tof\tmodel\tweights:\ti.e.,\tthe\trisks\tto\teconomic\tproductivity\tbenefitting\ta\tlarger\tswath\tof\tsociety,\tinnovation,\tscience,\tcompetition,\tand\tAI\ttransparency\tif\tOpen\tDUFMs\twere\tnot\twidely\tavailable.”). 233\t\tSee,\te.g.,\tEleutherAI\tInstitute\tComment\tat\t24\t(listing\texamples\tof\tresearch\tfacilitated\tby\t“open-weights\tfoundation\tmodels.”);\tRishi\tBommasani\tet\tal.\tComment\tat\t3\t(“Model\tweights\tare\tessential\tfor\tseveral\tforms\tof\tscientific\tresearch\tacross\tAI\tinterpretability,\tsecurity,\tand\tsafety)\t;\tCDT\tComment\tat\t8\t(“Researchers\tused\tthe\tmodel\tweights\tof\tMistral\t7B\t[.\t.\t.]\tto\tdecrease\tthe\tcomputational\tpower\trequired\tfor\tfine-tuning\tthe\tmodel\tfor\tdownstream\ttasks\tby\ta\tfactor\tof\tten.”). 234\t\tFor\texample,\tStable\tDiffusion\t3.\t(2023,\tFebruary\t22).\thttps://stability.ai/news/stable-diffusion-3.\t“suite\tof\tmodels\tcurrently\tranges\tfrom\t800M\tto\t8B\tparameters.” 235\t\tSee,\te.g.,\tCDT\tComment\tat\t33-40;\tThe\tAbundance\tInstitute\tComment\tat\t7\t(“Like\tobject\tcode,\tmodel\tweights\tcommunicate\tinformation\tto\ta\tcomputer\t–\tin\tthis\tcase,\ta\tcomputer\trunning\tan\tinference\tengine.\t[.\t.\t.]\tPeople\tand\torganizations\twho\twish\tto\tpublish\tsuch\tmodel\tweights\thave\ta\tprotected\tspeech\tinterest\tin\tdoing\tso.”);\tMozilla\tComment\tat\t10\tn.2\t(“Further,\tas\tU.S.\tcourts\thave\theld\tmultiple\ttimes,\tcomputer\tsource\tcode\tmust\tbe\tviewed\tas\texpressive\tfor\tFirst\tAmendment\tpurposes.\t[.\t.\t.]\tA\tsimilar\targument\tcould\tbe\tmade\tabout\tthe\timportance\tof\tprotecting\tthe\tsharing\tof\tinformation\tabout\tmodel\tweights\tand\tother\tAI\tcomponents.”).\tCf.\tG.S.\tHans\tComment\tat\t2\t(“Regulated\tAI\tcompanies\tmay\trely\tupon\tthe\treasoning\tof\t[Bernstein\tv.\tU.S.]\tto\targue\tthat\texport\trestrictions\ton\tmodel\tweights\tviolate\tthe\tFirst\tAmendment.”).\tBut\tsee\tRozenshtein,\tA.\t(April\t4,\t2024).\tThere\tIs\tNo\tGeneral\tFirst\tAmendment\tRight\tto\tDistribute\tMachine-Learning\tModel\tWeights.\tLawfare.\tThere\tmay\talso\tbe\tother\tconstitutional\tchallenges\tregarding\topenness\tin\tAI\tmodels\tbeyond\trestriction\tof\tmodel\tweights.\tSee\tgenerally\tG.S.\tHans\tComment\t(outlining\ta\trange\tof\tpotential\tFirst\tAmendment\tchallenges\trelated\tto\tgovernment\trequirements\ton\ttransparency,\tcontent\tmoderation,\tand\tother\ttopics.). 236\t\tEngler,\tA.\t(Jan.\t22,\t2024).\tThe\tcase\tfor\tAI\ttransparency\trequirements.\thttps://www.brookings.edu/articles/the-case-for-ai-transparency-requirements/.\t237\tGreene,\tT.,\t&\tet\tal.\t(2022).\tBarriers\tto\tacademic\tdata\tscience\tresearch\tin\tthe\tnew\trealm\tof\talgorithmic\tbehaviour\tmodification\tby\tdigital\tplatforms.\tNature\tMachine\tIntelligence,\t4,\t323–330.\t238\tGorwa,\tR.,\t&\tVeale,\tM.\t(2023,\tNovember\t21).\tModerating\tModel\tMarketplaces:\tPlatform\tGovernance\tPuzzles\tfor\tAI\tIntermediaries.\tArXiv.org.\thttps://export. arxiv.org/abs/2311.12573v1. 239\t\tNational\tInstitute\tof\tStandards\tand\tTechnology.\tArtificial\tIntelligence\tRisk\tManagement\tFramework\t(AI\tRMF\t1.0).\t(2023).\thttps://doi.org/10.6028/NIST. AI.100-1.\t240\tLongpre,\tS.,\t&\tet\tal.\t(2024).\tA\tSafe\tHarbor\tfor\tAI\tEvaluation\tand\tRed\tTeaming.\tArXiv.\thttps://arxiv.org/pdf/2403.04893.\t241\tGoogle\tComment\tat\t3. 242\t\tNational\tTelecommunications\tand\tInformation\tAdministration\t(2024).\tArtificial\tIntelligence\tAccountability\tPolicy\tReport.\thttps://www.ntia.gov/sites/ 66 Dual-Use Foundation Models with Widely Available Model Weightsdefault/files/publications/ntia-ai-report-final.pdf. 243\t\tIBM\tComment\tat\t4. 244\t\tAI\tPolicy\tand\tGovernance\tWorking\tGroup\tComment\tat\t3. 245\t\tBalwit,\tA.,\t&\tKorinek,\tA.\t(2022,\tMay\t10).\tAligned\twith\twhom?\tDirect\tand\tsocial\tgoals\tfor\tAI\tsystems.\thttps://www.brookings.edu/articles/aligned-withwhom-direct-and-social-goals-for-ai-systems/.\t246\tSartori,\tL.,\t&\tTheodorou,\tA.\t(2022).\tA\tsociotechnical\tperspective\tfor\tthe\tfuture\tof\tAI:\tnarratives,\tinequalities,\tand\thuman\tcontrol.\tEthics\tand\tInformation\tTechnology,\t24(4).\thttps://link.springer.com/article/10.1007/s10676-022-09624-3. 247\t\tIBM\tComment\tat\t8. 248\t\tAI\tAccountability\tPolicy\tReport,\tNational\tTelecommunications\tand\tInformation\tAdministration.\t(2024,\tMarch).\thttps://www.ntia.gov/issues/artificialintelligence/ai-accountability-policy-report. 249\t\tGorwa\tand\tMichael\tVeale,\t‘Moderating\tModel\tMarketplaces:\tPlatform\tGovernance\tPuzzles\tfor\tAI\tIntermediaries’\t(2024)\t16(2)\tLaw\tInnovation\tand\tTechnology. 250\t\tNational\tTelecommunications\tand\tInformation\tAdministration\t(2024).\tArtificial\tIntelligence\tAccountability\tPolicy\tReport.\thttps://www.ntia.gov/sites/ default/files/publications/ntia-ai-report-final.pdf. 251\t\tSee,\tfor\texample,\tprevious\twritings\tfrom\tthe\tClinton\tadministration\tabout\tthe\tInternet,\twhich\tnoted\tthat\t“governments\tshould\tencourage\tindustry\tselfregulation\twherever\tappropriate\tand\tsupport\tthe\tefforts\tof\tprivate\tsector\torganizations\tto\tdevelop\tmechanisms\tto\tfacilitate\tthe\tsuccessful\toperation\tof\tthe\tInternet.”\t1997\tGlobal\tElectronic\tCommerce\tFramework.\tClintonwhitehouse4.Archives.gov. 252\t\tRodriguez,\tS.\tand\tSchechner,\tS.\tFacebook\tParent’s\tPlan\tto\tWin\tAI\tRace:\tGive\tIts\tTech\tAway\tFree.\tWSJ.\tMay\t19,\t2024.\thttps://www.wsj.com/tech/ai/metasplan-to-win-ai-race-give-its-tech-away-free-4bcc080a. 253\t\tSee,\te.g.,\tPreparedness.\t(n.d.).\tOpenAI.\thttps://openai.com/preparedness/;\tAnthropic’s\tResponsible\tScaling\tPolicy.\t(2023,\tSeptember\t19).\tAnthropic.\thttps://www.anthropic.com/news/anthropics-responsible-scaling-policy.\t254\tSee,\te.g.,\tAI\tPolicy\tand\tGovernance\tWorking\tGroup\tComment\tat\t3-5\t;\tPublic\tKnowledge\tComment\tat\t11-12\t;\tHugging\tFace\tComment\tat\t8-9. 255\t\tSee,\te.g.,\tStability\tAI\tComment\tat\t4\t(“By\treducing\tthese\tcosts,\topen\tmodels\thelp\tto\tensure\tthe\teconomic\tbenefits\tof\tAI\taccrue\tto\ta\tbroad\tcommunity\tof\tdevelopers\tand\tsmall\tbusinesses,\tnot\tjust\tBig\tTech\tfirms\twith\tdeep\tpockets.). 256\t\tSee,\te.g.,\tAI\tPolicy\tand\tGovernance\tWorking\tGroup\tat\t1\t(“Openly\tavailable\tdata,\tcode,\tand\tinfrastructure\thave\tbeen\tcritical\tto\tthe\tadvancement\tof\tscience,\ttechnological\tinnovation,\teconomic\tgrowth,\tand\tdemocratic\tgovernance.\tThese\topen\tresources\thave\tbeen\tbuilt\tand\tshared\tin\tthe\tcontext\tof\tcommitments\tto\topen\tscience,\tto\texpanding\tindustry\tand\tmarkets,\tand\tto\tthe\tprinciple\tthat\tsome\ttechnologies\tshould\tbe\twidely\tavailable\tfor\tmaximum\tpublic\tbenefit,\twhile\tallowing\tfor\tcontrol\tof\taccess\tto\tdata,\tcode,\tand\tinfrastructure\tas\tnecessary\tfor\tsafety\tand\tsecurity\tpurposes.”). 257\t\tNix,\tN.,\tZakrzewski,\tC.,\tDe\tVynck,\tG.,\t(2024,\tMarch\t10)\tSilicon\tValley\tis\tpricing\tacademics\tout\tof\tAI\tresearch.\tWashington\tPost.\thttps://www.washingtonpost. com/technology/2024/03/10/big-tech-companies-ai-research/. 258\t\tAffiliation\tof\tresearch\tteams\tbuilding\tnotable\tAI\tsystems,\tby\tyear\tof\tpublication.\t(n.d.).\tOur\tWorld\tin\tData.\thttps://ourworldindata.org/grapher/affiliationresearchers-building-artificial-intelligence-systems-all. 259\t\tRand\tComment\tat\t3\t(“Publishing\tfoundation\tmodel\tweights\tcan\taid\tin\tAI\tsafety\tresearch.”). 260\t\tGoogle\tComment\tat\t2\t(“While\tthe\tbenefits\tof\topen\tAI\tmodels\tare\tprofound,\tthere\tis\talso\ta\trisk\tthat\ttheir\tuse\taccelerates\tharms,\tlike\tdeepfake\timagery,\tdisinformation,\tand\tmalicious\tservices.”). 261\t\tConsistent\twith\tthis\trecommendation,\tthe\tfederal\tgovernment\thas\ttaken\tseveral\tsignificant\tsteps\ttoward\tcollecting\ta\tmore\thigh-quality\tevidence\tbase.\tFor\texample,\tSection\t4.2(a)\tof\tExecutive\tOrder\t14110\tprovides\tfor\tthe\tcollection\tof\tinformation\tby\tthe\tfederal\tgovernment\tfrom\tdevelopers\tof\tcertain\tdualuse\tfoundation\tmodels,\tincluding\tcertain\t“results\tof\tany\tdeveloped\tdual-use\tfoundation\tmodel’s\tperformance\tin\trelevant\tAI\tred-team\ttesting[.]” 262\t\tIn\tour\treport\ton\tAI\taccountability\tpolicy,\twe\tstressed\tthe\timportance\tof\tindependent\taudits\tand\tassessments\tin\tlieu\tof\tsole\treliance\ton\tinternal\tselfassessments.\tSee\thttps://www.ntia.gov/sites/default/files/2024-04/ntia-ai-report-print.pdf\tat\t20-21,\t46-49.\tWe\tnoted\tthat\t“[d]eveloping\tregulatory\trequirements\tfor\tindependent\tevaluations,\twhere\twarranted,\tprovides\ta\tcheck\ton\tfalse\tclaims\tand\trisky\tAI,\tand\tincentivizes\tstronger\tevaluation\tsystems.”\tId.\tat\t48.\tWe\tconcluded\tthat\t“[i]ndependent\tAI\taudits\tand\tevaluations\tare\tcentral\tto\tany\taccountability\tstructure[]”\tand\t“[t]here\tare\tstrong\targuments\tfor\tsectoral\tregulation\tof\tAI\tsystems\tin\tthe\tUnited\tStates\tand\tfor\tmandatory\taudits\tof\tAI\tsystems\tdeemed\tto\tpresent\ta\thigh\trisk\tof\tharming\trights\tor\tsafety\t–\taccording\tto\tholistic\tassessments\ttailored\tto\tdeployment\tand\tuse\tcontexts.”\tId.\tat\t70,\t73.\tWe\trecommended\tthat\tthe\tfederal\tgovernment\t“work\twith\tstakeholders\tas\tappropriate\tto\tcreate\tguidelines\tfor\tAI\taudits\tand\tauditors[]”\tand\t“require\tas\tneeded\tindependent\tevaluations\tand\tregulatory\tinspections\tof\thigh-risk\tAI\tmodel\tclasses\tand\tsystems.”\tId.\tat\t70,\t73.\t263 U.S.\tCopyright\tOffice.\tCopyright\tand\tArtificial\tIntelligence.\thttps://www.copyright.gov/ai/. 264\t\tDepartment\tof\tEnergy.\tFrontiers\tin\tArtificial\tIntelligence\tfor\tScience,\tSecurity\tand\tTechnology\thttps://www.energy.gov/fasst. 67 Dual-Use Foundation Models with Widely Available Model Weights265\t\tModel\tweight\trestrictions\tbased\ton\tnon-expressive\tactivity\t–\tfor\texample,\ton\ta\tmodel’s\tdemonstrated\tcapability\tto\tevade\thuman\tcontrol\t–\twould\tface\tfewer\tlegal\tchallenges\tthan\trestrictions\tbased\ton\texpressive\tactivity.\tHowever,\tcourts\twould\tneed\tto\tdetermine\twhich,\tif\tany,\tmodel\tweight\trestrictions\tcounted\tas\texpressive\tspeech. 266\t\tSee,\te.g.,\tNarayanan,\tA.,\t&\tKapoor,\tS.\t(March\t21,\t2024).\tAI\tsafety\tis\tnot\ta\tmodel\tproperty.\tAI\tSnake\tOil.\thttps://www.aisnakeoil.com/p/ai-safety-is-not-amodel-property. 267\t\tArtificial\tintelligence:\tPerformance\ton\tknowledge\ttests\tvs.\tNumber\tof\tparameters.\t(2023).\thttps://ourworldindata.org/grapher/ai-performanceknowledge-tests-vs-parameters.\tOwen,\tD.\t(2023,\tJune\t9).\tHow\tPredictable\tIs\tLanguage\tModel\tBenchmark\tPerformance?\tEpoch\tAI.\thttps://epochai.org/ blog/how-predictable-is-language-model-benchmark-performance. 268\t\tThe\tterms\t“8B”\tand\t“70B”\tmean\tthe\tmodels\thave\t8\tbillion\tand\t70\tbillion\tparameters,\trespectively. 269\t\tSaplin,\tM.\t(2024,\tApril\t18).\tLlama\t3\t8B\tis\tbetter\tthan\tLlama\t2\t70B.\tDev.To.\thttps://dev.to/maximsaplin/llama-3-in-8b-and-70b-sizes-is-out-58pk. 270\t\tRoser,\tM.,\tet\tal.\t(2023,\tMarch\t28).\tWhat\tis\tMoore’s\tLaw?\tExponential\tgrowth\tis\tat\tthe\theart\tof\tthe\trapid\tincrease\tof\tcomputing\tcapabilities.\tOurworld,\thttps://ourworldindata.org/moores-law. 271\t\tDepending\ton\tthe\tpolicy\toptions\tunder\tconsideration,\tthese\tdevelopments\tcould\tcounsel\tin\tfavor\tof\teither\t“broader”\tor\t“narrower”\tthresholds\tfor\tinclusion.\tFor\texample,\tcontinued\tdecreases\tin\tthe\tcost\tof\ttraining\tpowerful\tmodels\tcould\tweigh\tin\tfavor\tof\tmore\tnarrowly\tdefining\tthe\tset\tof\tmodels\tsubject\tto\tcertain\trequirements,\tbecause\tthose\trequirements\tcould\tbecome\timpossible\tto\teffectively\tenforce\tif\ta\tvery\tlarge\tgroup\tof\tpeople\tare\teach\tcapable\tof\ttraining\tthose\tmodels.\tSee\talso\tLambert,\tN.\tInterconnects\tDBRX:\tThe\tnew\tbest\topen\tmodel\tand\tDatabricks’\tML\tstrategy.\twww.interconnects. ai.\thttps://www.interconnects.ai/p/databricks-dbrx-open-llm\t(describing\t“Mosaic’s\tLaw”,\ta\tphenomenon\tcoined\tby\tthe\tformer\tCEO\tof\tMosaic\twhereby\ttraining\t“a\tmodel\tof\ta\tcertain\tcapability\twill\trequire\t1/4\tthe\t[money]\tevery\tyear”\tdue\tto\ttechnological\tadvances).\tOn\tthe\tother\thand,\tdecreases\tin\tthe\tamount\tof\tmodel\tparameters\tor\ttraining\tcompute\tnecessary\tto\tachieve\ta\tcertain\tlevel\tof\tcapability\tcould\tweigh\tin\tfavor\tof\tmore\tbroadly\tdefining\tthe\ttechnical\tcharacteristics\tthat\twould\tsubject\ta\tmodel\tto\tpolicy\trequirements,\tbecause\tmodels\twith\tthe\tsame\ttechnical\tcharacteristics\tmay\tincrease\tin\ttheir\tcapabilities\t–\tand\ttherefore\trisks\t–\tover\ttime. 272\t\tEvo:\tDNA\tfoundation\tmodeling\tfrom\tmolecular\tto\tgenome\tscale.\tArc\tInstitute.\t(2024,\tFebruary\t27).\thttps://arcinstitute.org/news/blog/evo.\tNote,\thowever,\tthat\tthe\tEvo\tmodel\tcontains\tapproximately\t7\tbillion\tparameters,\tfewer\tthan\tthe\ttens-of-billions\tthreshold\tset\tforth\tin\tthe\tEO. 273\t\tHong,\tW.,\tet\tal.\t(2022).\tCogVideo:\tLarge-scale\tPretraining\tfor\tText-to-Video\tGeneration\tvia\tTransformers.\tArXiv.\thttps://arxiv.org/pdf/2205.15868;\tStable\tDiffusion\t3.\t(February\t22,\t2024).\tStability\tAI.\thttps://stability.ai/news/stable-diffusion-3. 274\t\tCreate\tRealistic\tDeepfakes\twith\tDeepFaceLab\t2.0.\t(2023,\tNovember\t16).\thttps://www.toolify.ai/ai-news/create-realistic-deepfakes-withdeepfacelab-20-45020.\t275\tSee\tPan\tAlexander,\tet\tal.\t(2023)\tDo\tthe\tRewards\tJustify\tthe\tMeans?\tMeasuring\tTrade-Offs\tBetween\tRewards\tand\tEthical\tBehavior\tin\tthe\tMachiavelli\tBenchmark.\tProceedings\tof\tMachine\tLearning\tResearch.\thttps://proceedings.mlr.press/v202/pan23a.html. 276\t\tVadapalli,\tSreya\tet\tal.\t(May\t2022)\tArtificial\tIntelligence\tand\tmachine\tlearning\tapproaches\tusing\tgene\texpression\tand\tvariant\tdata\tfor\tpersonalized\tmedicine.\tNational\tInstitutes\tof\tHealth\tNational\tLibrary\tof\tMedicine.\thttps://www.ncbi.nlm.nih.gov/pmc/articles/PMC10233311/. 277\t\tSelinger,\tEvan.\t(November\t13,\t2021)\tFacebook’s\tnext\tprivacy\tnightmare\twill\tbe\ta\tsight\tto\tsee.\tBoston\tGlobe.\thttps://www.bostonglobe.com/2021/11/12/ opinion/facebooks-next-privacy-nightmare-will-be-sight-see/. 278\t\tJerome,\tJ.\t(2020,\tSeptember\t26).\tThe\tRace\tto\tMap\tReality\tSo\tSilicon\tValley\tCan\tAugment\tIt\tIs\tOn.\thttps://thewire.in/tech/silicon-valley-augmented-realityfacebook-google. 279\t\tSee\tMetz,\tCade.\t(May\t13,\t2024)\tOpenAI\tUnveils\tNew\tChatGPT\tThat\tListens,\tLooks,\tand\tTalks.\tThe\tNew\tYork\tTimes.\thttps://www.nytimes.com/2024/05/13/ technology/openai-chatgpt-app.html. 280\t\tThe\tU.S.\tGovernment\tis\tcurrently\tdefining\tAI\tand\tAI-enabling\ttalent\tin\tOMB\tM-24-10\tas:\tindividuals\twith\tpositions\tand\tmajor\tduties\twhose\tcontributions\tare\timportant\tfor\tsuccessful\tand\tresponsible\tAI\toutcomes.\tAI\tand\tAI-Enabling\tRoles\tinclude\tboth\ttechnical\tand\tnon-technical\troles,\tsuch\tas\tdata\tscientists,\tsoftware\tengineers,\tdata\tengineers,\tdata\tgovernance\tspecialists,\tstatisticians,\tmachine\tlearning\tengineers,\tapplied\tscientists,\tdesigners,\teconomists,\toperations\tresearchers,\tproduct\tmanagers,\tpolicy\tanalysts,\tprogram\tmanagers,\tbehavioral\tand\tsocial\tscientists,\tcustomer\texperience\tstrategists,\thuman\tresource\tspecialists,\tcontracting\tofficials,\tmanagers,\tand\tattorneys. 281\t\tRand\tComment\tat\t4\t(“The\tmost\tcommon\tapproach\tto\tstructured\taccess\tis\tto\tcreate\tflexible\tapplication\tprogramming\tinterfaces\t(APIs)\tthat\tallow\tresearchers,\tsmall\tbusinesses,\tor\tthe\tpublic\tto\taccess\tthe\tmodel.”). 282\t\tShevlane,\tT.\t(2022).\tStructured\tAccess:\tAn\tEmerging\tParadigm\tfor\tSafe\tAI\tDeployment.\tUniversity\tof\tOxford.\tat\t20.\thttps://arxiv.org/abs/2201.05159.\t283\tSee,\te.g.,\tCDT\tComment\tat\t39-40\t(discussing\tpotential\tFirst\tAmendment\tconsiderations\tthat\tcould\tbe\timplicated\tin\tthe\tregulation\tof\topen\tfoundation\tmodels\tand\ttheir\tweights). 284\t\tBucknall,\tB.,\t&\tTrager,\tR.\t(2023).\tStructured\tAccess\tfor\tThird-Party\tResearch\ton\tFrontier\tAI\tModels:\tInvestigating\tResearchers’\tModel\tAccess\tRequirements.\tOxford\tMartin\tSchool.\thttps://www.oxfordmartin.ox.ac.uk:8443/publications/structured-access-for-third-party-research-on-frontier-ai-modelsinvestigating-researchers-model-access-requirements.\t285\tSee,\te.g.,\tNarayanan,\tA.,\t&\tKapoor,\tS.\t(March\t21,\t2024).\tAI\tsafety\tis\tnot\ta\tmodel\tproperty.\tAI\tSnake\tOil.\thttps://www.aisnakeoil.com/p/ai-safety-is-not-amodel-property. 286\t\tChopra,\tR.,\tet\tal.\t(2023).\tJoint\tStatement\ton\tEnforcement\tEfforts\tagainst\tDiscrimination\tand\tBias\tin\tAutomated\tSystems.\thttps://www.ftc.gov/system/files/ f tc_gov/pdf/EEOC-CRT-FTC-CFPB-AI-Joint-Statement%28final%29.pdf.", "metadata": {"original_filename": "item014_US_Dual-Use Foundation Models with Widely Available Model Weights Report.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:35:11.077532", "updated_at": "2025-08-28T21:35:11.077532", "word_count": 266636}