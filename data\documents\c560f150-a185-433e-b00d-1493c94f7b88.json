{"doc_id": "c560f150-a185-433e-b00d-1493c94f7b88", "title": "item233_US_Written Testimony of  <PERSON> Head of Global Public Sector, Cohere Before the Senate Armed Services Committee, Subcommittee on Cybersecurity", "text": "Written Testimony of  <PERSON> Head of Global Public Sector, Cohere Before the Senate Armed Services Committee, Subcommittee on Cybersecurity \r\n\r\n4. Policy Recommendations for Congress Four years ago, Congress’ AI Commission took a comprehensive look at the opportunity of applying AI to national security challenges. Their conclusion then was that by 2025 the foundations for widespread integration of AI across DoD must be in place, and that the Department should allocate at least $8 billion toward AI annually. Public estimates from last year were that DoD is allocating about half the amount recommended by the Commission.  To support the Department of Defense in responsibly accelerating AI adoption, I respectfully offer the following policy recommendations for Congress: (A) Modernize Procurement Processes to Drive AI Innovation: Perhaps the most important step Congress can take is to modernize DoD's acquisition and procurement models for AI and software. The Secretary of Defense’s March 6 memorandum on software acquisition is a key step that clearly signals “software companies make software and the DoD will buy software from software companies.” Current federal 8 procurement rules and lengthy contracting cycles often favor large, established defense vendors and can inadvertently create high barriers to entry for innovative AI startups. If the U.S. military wants to have the best technology, then we must ensure the best technology companies can compete to serve them – this includes non-traditional defense suppliers like Cohere. Congress should encourage and empower DoD to use more flexible, agile procurement mechanisms tailored to fast-evolving tech. For example, expanding the use of challenge-based solicitations, pilot programs, Commercial Solutions Openings (CSOs) and Other Transaction Authorities (OTAs) can allow the Department to rapidly test and integrate cutting-edge AI solutions. These approaches lower the burden to get in the door, enabling smaller firms with innovative ideas to prove their value through prototypes or competitions rather than needing an extensive track record of past contracts. Alongside this, Congress can push for simplified compliance requirements and streamlined vendor qualification criteria for AI software, so that well-intentioned rules do not unintentionally exclude agile startups. By making federal procurement more accessible and agile, the government can leverage its \"power of the purse\" to catalyze AI innovation and avoid stagnation. Importantly, modernizing procurement isn't just about speed – it's also about outcomes. I recommend that Congress direct DoD to update its source selection criteria for AI-related contracts to place appropriate weight on innovation, security, and performance, rather than defaulting to corporate size or longest past performance. The goal should be to reward true technical merit and risk mitigation, not just familiarity. Taken together, these procurement reforms will help ensure that the U.S. military has access to the full marketplace of AI innovation – bringing the best capabilities to our cyber warfighters, from large defense primes to innovative startups. (B) Promote Interoperability and Avoid Vendor Lock-In: As the DoD acquires AI tools from various sources, Congress should ensure that interoperability and open standards are a priority. No single vendor should be able to supply all of DoD's AI needs. The AI ecosystem is dynamic and evolving, and so should be AI vendors selling to the government. We want an ecosystem where multiple AI solutions can plug into defense systems seamlessly and even augment each other. To that end, Congress could require that DoD include interoperability requirements in AI procurements and programs, mandating that solutions adhere to common data formats, APIs, or integration standards. This will allow systems from different providers – say an AI cybersecurity sensor from one company and an AI analytics dashboard from another – to work together without heroic integration efforts. It also prevents proprietary lock-in, where DoD might be stuck with one vendor's ecosystem.  Open, interoperable approaches spur competition and innovation, because vendors know their products must operate in a hybrid environment and can be replaced with 9 improved technology. Alongside interoperability, Congress should encourage DoD to avoid single-source dependency in critical AI capabilities. This might involve funding diverse pilots for a given use-case (so multiple solutions are evaluated), and then adopting the best – or even multiple – solutions. Legislative report language could underscore that avoiding vendor lock-in is a strategic imperative for both security and negotiating power. In summary, Congress should help DoD obtain AI systems that are as flexible and interchangeable as possible. This not only ensures our forces get the best tech, but it also safeguards against supply-chain risks and empowers DoD to rapidly upgrade components as the technology advances. (C) Support AI Adoption with Internal Benchmarking: While speeding up AI adoption, Congress must also ensure that appropriate benchmarking guidelines are in place for AI in national security. In the commercial space, we have quickly learned that general academic benchmarks are regularly gamed and often don’t represent real world use. For example, they don’t showcase how models will tackle tasks on an assembly line or in analyzing research. We recommend that Congress and the DoD consider funding programs that develop methods to test and validate AI systems for the kinds of reliability and use cases they will require - including human evaluations. ", "metadata": {"original_filename": "item233_US_Written Testimony of  <PERSON> Head of Global Public Sector, Cohere Before the Senate Armed Services Committee, Subcommittee on Cybersecurity.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:35:13.793139", "updated_at": "2025-08-28T21:35:13.793139", "word_count": 5687}