from pydantic_settings import BaseSettings
from typing import Optional
import os
from pathlib import Path


class Settings(BaseSettings):
    # 应用配置
    APP_NAME: str = "文档分析系统"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True
    
    # 智谱AI API配置
    ZHIPUAI_API_KEY: str = "default_test_key"
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./document_analysis.db"
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    # API配置
    MAX_WORKERS: int = 4
    REQUEST_TIMEOUT: int = 30
    MAX_RETRIES: int = 3
    
    # 文件上传配置
    MAX_FILE_SIZE: str = "10MB"
    UPLOAD_DIR: str = "uploads"
    
    # 缓存配置
    CACHE_TTL: int = 3600
    REDIS_URL: Optional[str] = None
    
    # 安全配置
    SECRET_KEY: str = "default_secret_key_change_in_production_1234567890"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    ALLOWED_ORIGINS: list[str] = ["http://localhost:3000", "http://localhost:8000", "http://127.0.0.1:8000"]
    
    # API密钥配置
    API_KEY_PREFIX: str = "sk-"
    API_KEY_LENGTH: int = 32
    API_KEY_EXPIRE_DAYS: int = 365
    
    # 速率限制配置
    RATE_LIMIT_REQUESTS_PER_MINUTE: int = 60
    RATE_LIMIT_REQUESTS_PER_HOUR: int = 1000
    RATE_LIMIT_REQUESTS_PER_DAY: int = 10000
    
    # 密码策略
    PASSWORD_MIN_LENGTH: int = 8
    PASSWORD_REQUIRE_UPPERCASE: bool = True
    PASSWORD_REQUIRE_LOWERCASE: bool = True
    PASSWORD_REQUIRE_NUMBERS: bool = True
    PASSWORD_REQUIRE_SPECIAL: bool = True
    
    # 监控配置
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    
    # 项目路径
    BASE_DIR: Path = Path(__file__).resolve().parent.parent.parent
    
    # 提示词模板路径
    PROMPTS_DIR: Path = BASE_DIR / "prompts"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建全局设置实例
settings = Settings()
