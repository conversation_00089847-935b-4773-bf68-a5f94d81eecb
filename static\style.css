/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --error-color: #f56565;
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --bg-primary: #ffffff;
    --bg-secondary: #f7fafc;
    --bg-tertiary: #edf2f7;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15), 0 10px 10px rgba(0, 0, 0, 0.04);
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--accent-color) 100%);
    background-attachment: fixed;
    min-height: 100vh;
    padding: 20px;
    line-height: 1.6;
    color: var(--text-primary);
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 头部样式 */
header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--accent-color) 100%);
    color: white;
    padding: 60px 40px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
}

header h1 {
    font-size: 3em;
    margin-bottom: 15px;
    font-weight: 700;
    position: relative;
    z-index: 1;
    background: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.8));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.2em;
    opacity: 0.95;
    position: relative;
    z-index: 1;
    font-weight: 300;
    letter-spacing: 0.5px;
}

/* 导航样式 */
.main-nav {
    margin-top: 30px;
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
    position: relative;
    z-index: 1;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    font-size: 14px;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(15px);
    border-color: rgba(255, 255, 255, 0.4);
    font-weight: 600;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 主内容区域 */
main {
    padding: 40px;
}

section {
    margin-bottom: 50px;
    position: relative;
}

h2 {
    color: var(--text-primary);
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 3px solid transparent;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color)) bottom/100% 3px no-repeat;
    font-size: 1.8em;
    font-weight: 600;
    position: relative;
}

h2::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
    border-radius: 2px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 25px;
    position: relative;
}

label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 0.95em;
    letter-spacing: 0.5px;
}

input[type="text"],
input[type="password"],
textarea {
    width: 100%;
    padding: 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
    font-family: inherit;
    background: var(--bg-primary);
    box-shadow: var(--shadow-sm);
}

input[type="text"]:focus,
input[type="password"]:focus,
textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1), var(--shadow-md);
    transform: translateY(-1px);
}

input[type="text"]:hover,
input[type="password"]:hover,
textarea:hover {
    border-color: var(--primary-dark);
}

/* API配置区域 */
.config-section {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    padding: 30px;
    border-radius: var(--border-radius-lg);
    margin-bottom: 40px;
    border: 1px solid rgba(102, 126, 234, 0.1);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.config-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.model-config {
    display: flex;
    flex-direction: column;
}

.model-config select,
.model-config input {
    width: 100%;
}

@media (max-width: 768px) {
    .config-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

/* 配置按钮样式 */
.form-group .btn {
    margin-right: 10px;
    margin-bottom: 10px;
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
    border: 1px solid var(--secondary-color);
}

.btn-secondary:hover {
    background: var(--secondary-dark);
    border-color: var(--secondary-dark);
}

/* 自定义模型输入框动画 */
#custom-model {
    transition: all 0.3s ease;
    opacity: 0;
    max-height: 0;
    overflow: hidden;
}

#custom-model[style*="block"] {
    opacity: 1;
    max-height: 50px;
}

/* 成功和错误消息样式 */
.success-message {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    box-shadow: var(--shadow-sm);
    animation: slideInDown 0.3s ease;
}

.error-message {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    box-shadow: var(--shadow-sm);
    animation: slideInDown 0.3s ease;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 分析按钮组样式 */
.analysis-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 20px;
}

.secondary-btn {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.secondary-btn:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.tertiary-btn {
    background: linear-gradient(135deg, #059669, #047857);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.tertiary-btn:hover {
    background: linear-gradient(135deg, #047857, #065f46);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 高级分析结果样式 */
.advanced-results-container {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    margin: 20px 0;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.analysis-info {
    background: rgba(102, 126, 234, 0.1);
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
}

.task-result {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 15px;
}

.task-result h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.task-content pre {
    background: rgba(0, 0, 0, 0.05);
    padding: 10px;
    border-radius: var(--border-radius);
    overflow-x: auto;
    font-size: 14px;
    line-height: 1.4;
}

.orchestrator-info {
    background: rgba(118, 75, 162, 0.1);
    padding: 15px;
    border-radius: var(--border-radius);
    margin-top: 20px;
}

/* 高级批量分析进度样式 */
.advanced-batch-progress-container {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    margin: 20px 0;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.batch-info {
    background: rgba(102, 126, 234, 0.1);
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
}

.advanced-batch-results-container {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    margin: 20px 0;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.batch-summary {
    background: rgba(102, 126, 234, 0.1);
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
}

.results-list {
    margin-top: 20px;
}

.result-item {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 15px;
}

.result-item.success {
    border-left: 4px solid #10b981;
}

.result-item.error {
    border-left: 4px solid #ef4444;
}

.task-result-item {
    background: rgba(0, 0, 0, 0.05);
    padding: 10px;
    border-radius: var(--border-radius);
    margin-bottom: 10px;
}

.task-result-item pre {
    margin: 5px 0 0 0;
    font-size: 13px;
    line-height: 1.3;
}

@media (max-width: 768px) {
    .analysis-buttons {
        flex-direction: column;
    }

    .secondary-btn,
    .tertiary-btn {
        width: 100%;
    }
}

.config-section::before {
    content: '⚙️';
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 3em;
    opacity: 0.1;
    transform: rotate(-15deg);
}

.hint {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 14px;
}

.hint a {
    color: #667eea;
    text-decoration: none;
}

.hint a:hover {
    text-decoration: underline;
}

/* 文件上传样式 */
.upload-section {
    margin-bottom: 30px;
}

.upload-buttons {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.upload-btn {
    display: inline-block;
    padding: 16px 32px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    font-size: 16px;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.upload-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.upload-btn:hover::before {
    left: 100%;
}

.upload-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.upload-btn:active {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.file-list {
    max-height: 250px;
    overflow-y: auto;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 15px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.file-item {
    padding: 12px 16px;
    background: var(--bg-primary);
    margin-bottom: 8px;
    border-radius: var(--border-radius);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.file-item:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.file-item .file-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--text-secondary);
    font-weight: 500;
}

.file-item .remove-btn {
    color: var(--error-color);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: var(--transition);
    font-weight: bold;
}

.file-item .remove-btn:hover {
    background: var(--error-color);
    color: white;
    transform: rotate(90deg);
}

.divider {
    text-align: center;
    margin: 20px 0;
    color: #999;
    position: relative;
}

.divider::before,
.divider::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 40%;
    height: 1px;
    background: #ddd;
}

.divider::before {
    left: 0;
}

.divider::after {
    right: 0;
}

textarea {
    resize: vertical;
    min-height: 150px;
}

/* 任务选项样式 */
.task-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.task-option {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #f5f5f5;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
}

.task-option:hover {
    background: #e8e8e8;
}

.task-option input[type="checkbox"] {
    margin-right: 8px;
}

/* 按钮样式 */
.primary-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    color: white;
    border: none;
    padding: 18px 48px;
    font-size: 18px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.primary-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s;
}

.primary-btn:hover::before {
    left: 100%;
}

.primary-btn:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.primary-btn:active {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.primary-btn:disabled {
    background: var(--text-muted);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 加载动画 */
.loading {
    text-align: center;
    padding: 60px 40px;
}

.spinner {
    width: 60px;
    height: 60px;
    margin: 0 auto 30px;
    border: 4px solid var(--bg-tertiary);
    border-top: 4px solid var(--primary-color);
    border-right: 4px solid var(--accent-color);
    border-bottom: 4px solid var(--secondary-color);
    border-radius: 50%;
    animation: spin 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
    100% { transform: rotate(360deg) scale(1); }
}

.loading p {
    color: var(--text-secondary);
    font-size: 1.1em;
    font-weight: 500;
    animation: pulse-text 2s ease-in-out infinite;
}

@keyframes pulse-text {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* 结果显示样式 */
.result-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    margin-bottom: 25px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.result-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

.result-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.result-card h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.4em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.result-card h3::before {
    content: '📊';
    font-size: 1.2em;
}

/* 批量结果样式 */
.batch-results-container {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--primary-color);
}

.batch-results-container h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.5em;
    font-weight: 600;
}

.batch-result-card {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: var(--shadow-sm);
    border-left: 3px solid var(--accent-color);
    transition: var(--transition);
}

.batch-result-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.batch-result-card h4 {
    color: var(--text-primary);
    margin-bottom: 10px;
    font-size: 1.2em;
    font-weight: 500;
}

.result-summary {
    margin-bottom: 15px;
}

.result-summary p {
    margin: 5px 0;
    color: var(--text-secondary);
}

.detailed-results {
    border-top: 1px solid var(--border-color);
    padding-top: 15px;
    margin-top: 15px;
}

.task-result {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 10px;
    border-left: 2px solid var(--primary-color);
}

.task-result h5 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.1em;
    font-weight: 500;
}

.task-content {
    color: var(--text-primary);
}

.task-content pre {
    background: var(--bg-secondary);
    padding: 10px;
    border-radius: var(--border-radius);
    font-size: 0.9em;
    line-height: 1.4;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.batch-progress-container {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow-md);
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: var(--bg-secondary);
    border-radius: 10px;
    overflow: hidden;
    margin: 15px 0;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 10px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.error {
    color: var(--error-color);
    font-weight: 500;
}

/* 批量处理提示样式 */
.batch-hint {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    border-radius: var(--border-radius);
    padding: 15px;
    margin: 15px 0;
    text-align: center;
    box-shadow: var(--shadow-md);
}

.batch-hint p {
    margin: 5px 0;
    font-size: 0.95em;
}

.batch-hint p:first-child {
    font-weight: 600;
    font-size: 1.1em;
}

/* 文件列表样式改进 */
.file-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 10px;
    margin-top: 10px;
    background: var(--bg-secondary);
}

.file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin: 5px 0;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    border-left: 3px solid var(--accent-color);
}

.file-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

.file-name {
    flex: 1;
    font-size: 0.9em;
    font-weight: 500;
}

.remove-btn {
    background: var(--error-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition);
}

.remove-btn:hover {
    background: #c0392b;
    transform: scale(1.1);
}

/* 批量处理配置样式 */
.batch-config {
    background: var(--bg-secondary);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin: 15px 0;
}

.concurrency-control {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 10px 0;
}

.slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: var(--bg-primary);
    outline: none;
    -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

#concurrency-value {
    font-weight: bold;
    font-size: 1.2em;
    color: var(--primary-color);
    min-width: 20px;
    text-align: center;
}

.concurrency-label {
    color: var(--text-secondary);
    font-size: 0.9em;
}

.concurrency-hint {
    margin-top: 10px;
    padding: 8px 12px;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--accent-color);
}

.concurrency-hint small {
    color: var(--text-secondary);
    font-size: 0.85em;
}

/* 进度显示增强 */
.progress-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.progress-item {
    background: var(--bg-secondary);
    padding: 10px 15px;
    border-radius: var(--border-radius);
    text-align: center;
    border-left: 3px solid var(--primary-color);
}

.progress-item .label {
    font-size: 0.8em;
    color: var(--text-secondary);
    margin-bottom: 5px;
}

.progress-item .value {
    font-size: 1.2em;
    font-weight: bold;
    color: var(--primary-color);
}

.current-files {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 10px;
    margin: 10px 0;
    max-height: 100px;
    overflow-y: auto;
}

.current-files .file-name {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    margin: 2px;
}

.visualization-btn-container {
    text-align: center;
    margin: 30px 0;
    padding: 30px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

.viz-btn {
    background: var(--bg-primary);
    color: var(--primary-color);
    border: none;
    padding: 16px 32px;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-md);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.viz-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.result-content {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: var(--border-radius);
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
    font-size: 14px;
    line-height: 1.6;
}

.result-content pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0;
    color: var(--text-secondary);
}

/* 历史记录样式 */
.history-list {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 10px;
}

.history-item {
    padding: 20px;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    margin-bottom: 15px;
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.history-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.5s;
}

.history-item:hover::before {
    left: 100%;
}

.history-item:hover {
    background: var(--bg-secondary);
    transform: translateX(8px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.history-item h4 {
    color: var(--text-primary);
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 1.1em;
}

.history-item .date {
    color: var(--text-muted);
    font-size: 14px;
    font-weight: 500;
}

.empty-state {
    text-align: center;
    color: var(--text-muted);
    padding: 60px 20px;
    font-size: 1.1em;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 2px dashed var(--border-color);
}

.empty-state::before {
    content: '📭';
    display: block;
    font-size: 3em;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* 错误提示样式 */
.error-message {
    background: #fee;
    color: #c33;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.success-message {
    background: #efe;
    color: #3c3;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

/* 页脚样式 */
footer {
    background: #f8f9fa;
    padding: 20px;
    text-align: center;
    color: #666;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .container {
        margin: 10px;
        border-radius: 12px;
    }
    
    header {
        padding: 40px 20px;
    }
    
    header h1 {
        font-size: 2.5em;
    }
}

@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .container {
        margin: 0;
        border-radius: 0;
        min-height: 100vh;
    }
    
    header {
        padding: 30px 20px;
    }
    
    header h1 {
        font-size: 2.2em;
        margin-bottom: 10px;
    }
    
    .subtitle {
        font-size: 1em;
    }
    
    .main-nav {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }
    
    .nav-link {
        width: 100%;
        max-width: 250px;
        text-align: center;
        padding: 12px 20px;
    }
    
    main {
        padding: 20px;
    }
    
    .upload-buttons {
        flex-direction: column;
        align-items: stretch;
    }
    
    .upload-btn {
        text-align: center;
    }
    
    .task-options {
        grid-template-columns: 1fr;
    }
    
    .primary-btn {
        width: 100%;
        padding: 16px 24px;
    }
    
    .result-card {
        padding: 20px;
    }
    
    .result-content {
        max-height: 300px;
    }
}

@media (max-width: 480px) {
    header {
        padding: 25px 15px;
    }
    
    header h1 {
        font-size: 1.8em;
    }
    
    .subtitle {
        font-size: 0.9em;
    }
    
    main {
        padding: 15px;
    }
    
    h2 {
        font-size: 1.5em;
    }
    
    .config-section {
        padding: 20px;
    }
    
    .result-card {
        padding: 15px;
    }
    
    .viz-btn {
        padding: 12px 24px;
        font-size: 14px;
    }
}

/* 主题切换按钮 */
.theme-toggle {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
    margin-left: 12px;
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

.theme-toggle:active {
    transform: scale(0.95);
}

/* 语言选择器样式 */
.language-selector {
    display: flex;
    gap: 5px;
    margin: 0 10px;
}

.language-selector button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.language-selector button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.language-selector button.active {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    font-weight: 600;
}

/* 暗色主题支持 */
[data-theme="dark"] {
    --text-primary: #f7fafc;
    --text-secondary: #e2e8f0;
    --text-muted: #a0aec0;
    --bg-primary: #1a202c;
    --bg-secondary: #2d3748;
    --bg-tertiary: #4a5568;
    --border-color: #4a5568;
    --bg-card: rgba(45, 55, 72, 0.8);
}

[data-theme="dark"] body {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 50%, #667eea 100%);
}

[data-theme="dark"] .container {
    background: rgba(26, 32, 44, 0.95);
}

[data-theme="dark"] .config-section {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
}

[data-theme="dark"] .prompt-item {
    background: rgba(45, 55, 72, 0.8);
    border-color: #4a5568;
}

[data-theme="dark"] .prompt-content {
    background: rgba(26, 32, 44, 0.8);
    border-color: #4a5568;
    color: #e2e8f0;
}

/* 系统偏好暗色主题 */
@media (prefers-color-scheme: dark) {
    :root:not([data-theme="light"]) {
        --text-primary: #f7fafc;
        --text-secondary: #e2e8f0;
        --text-muted: #a0aec0;
        --bg-primary: #1a202c;
        --bg-secondary: #2d3748;
        --bg-tertiary: #4a5568;
        --border-color: #4a5568;
        --bg-card: rgba(45, 55, 72, 0.8);
    }
    
    :root:not([data-theme="light"]) body {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 50%, #667eea 100%);
    }
    
    :root:not([data-theme="light"]) .container {
        background: rgba(26, 32, 44, 0.95);
    }
    
    :root:not([data-theme="light"]) .config-section {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
}

/* 焦点样式优化 */
*:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 打印样式 */
@media print {
    body {
        background: white;
    }
    
    .container {
        box-shadow: none;
        border: none;
    }
    
    .config-section,
    .upload-section {
        display: none;
    }
}
