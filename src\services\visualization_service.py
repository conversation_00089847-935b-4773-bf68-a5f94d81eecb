import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import json
from collections import Counter

from src.models.schemas import (
    VisualizationData, ChartData, SummaryMetrics, TaskType,
    ActorRelationResult, RoleFramingResult, ProblemScopeResult, 
    CausalMechanismResult, AnalysisResult
)

logger = logging.getLogger(__name__)


class VisualizationService:
    """可视化服务 - 将分析结果转换为可视化数据"""

    def __init__(self):
        # 延迟初始化，避免循环依赖
        self.analysis_service = None

    async def generate_visualization_data(
        self, 
        doc_id: str, 
        task_types: Optional[List[TaskType]] = None
    ) -> Optional[VisualizationData]:
        """生成完整的可视化数据"""
        try:
            # 延迟初始化分析服务
            if self.analysis_service is None:
                from src.services.analysis_service import get_analysis_service
                self.analysis_service = await get_analysis_service()
            
            # 获取分析结果
            results = await self.analysis_service.get_results(doc_id)
            if not results:
                return None
            doc_title = f"Document {doc_id}"  # 这里可以从文档服务获取真实标题
            
            # 转换字符串键为TaskType枚举
            enum_results = {}
            for key, value in results.items():
                try:
                    task_type = TaskType(key)
                    enum_results[task_type] = value
                except ValueError:
                    logger.warning(f"Unknown task type: {key}")
                    continue
            
            # 过滤任务类型
            if task_types:
                enum_results = {k: v for k, v in enum_results.items() if k in task_types}

            # 生成图表数据
            charts = await self.generate_charts_with_results(enum_results)
            
            # 生成摘要
            summary = await self.generate_summary_metrics(doc_id)

            return VisualizationData(
                doc_id=doc_id,
                title=doc_title,
                analysis_date=datetime.now(),
                task_types=list(enum_results.keys()),
                charts=charts,
                summary=summary.dict() if summary else {}
            )

        except Exception as e:
            logger.error(f"生成可视化数据失败: {e}")
            return None

    async def generate_charts(
        self, 
        doc_id: str, 
        task_types: Optional[List[TaskType]] = None
    ) -> Dict[str, Any]:
        """生成图表数据"""
        try:
            # 延迟初始化分析服务
            if self.analysis_service is None:
                from src.services.analysis_service import get_analysis_service
                self.analysis_service = await get_analysis_service()
            
            # 获取分析结果
            results = await self.analysis_service.get_results(doc_id)
            if not results:
                return {}

            # 转换字符串键为TaskType枚举
            enum_results = {}
            for key, value in results.items():
                try:
                    task_type = TaskType(key)
                    enum_results[task_type] = value
                except ValueError:
                    logger.warning(f"Unknown task type: {key}")
                    continue

            if task_types:
                enum_results = {k: v for k, v in enum_results.items() if k in task_types}

            return await self.generate_charts_with_results(enum_results)

        except Exception as e:
            logger.error(f"生成图表数据失败: {e}")
            return {}

    async def generate_charts_with_results(self, enum_results: Dict[TaskType, Any]) -> Dict[str, Any]:
        """根据枚举结果生成图表数据"""
        try:
            charts = {}

            # 为每种任务类型生成图表
            for task_type, result in enum_results.items():
                task_result = result.result if hasattr(result, 'result') else result
                task_charts = await self._generate_task_charts(task_type, task_result)
                charts[task_type.value] = task_charts

            return charts

        except Exception as e:
            logger.error(f"生成图表数据失败: {e}")
            return {}

    async def _generate_task_charts(self, task_type: TaskType, result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """为特定任务类型生成图表"""
        charts = []

        if task_type == TaskType.ACTOR_RELATION:
            charts.extend(await self._generate_actor_relation_charts(result))
        elif task_type == TaskType.ROLE_FRAMING:
            charts.extend(await self._generate_role_framing_charts(result))
        elif task_type == TaskType.PROBLEM_SCOPE:
            charts.extend(await self._generate_problem_scope_charts(result))
        elif task_type == TaskType.CAUSAL_MECHANISM:
            charts.extend(await self._generate_causal_mechanism_charts(result))

        return charts

    async def _generate_actor_relation_charts(self, result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成行为者关系图表"""
        charts = []
        actors = result.get("actors", [])
        relations = result.get("relations", [])

        # 行为者类型分布饼图
        if actors:
            actor_types = Counter([actor.get("type", "未知") for actor in actors])
            charts.append({
                "type": "pie",
                "title": "行为者类型分布",
                "data": {
                    "labels": list(actor_types.keys()),
                    "datasets": [{
                        "data": list(actor_types.values()),
                        "backgroundColor": [
                            "#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", 
                            "#9966FF", "#FF9F40", "#FF6384", "#C9CBCF"
                        ]
                    }]
                }
            })

        # 关系类型分布柱状图
        if relations:
            relation_types = Counter([rel.get("type", "未知") for rel in relations])
            charts.append({
                "type": "bar",
                "title": "关系类型分布",
                "data": {
                    "labels": list(relation_types.keys()),
                    "datasets": [{
                        "label": "关系数量",
                        "data": list(relation_types.values()),
                        "backgroundColor": "#36A2EB"
                    }]
                }
            })

        # 行为者网络图数据
        if actors and relations:
            network_data = self._generate_network_data(actors, relations)
            charts.append({
                "type": "network",
                "title": "行为者关系网络",
                "data": network_data
            })

        return charts

    async def _generate_role_framing_charts(self, result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成角色塑造图表"""
        charts = []
        heroes = result.get("heroes", [])
        victims = result.get("victims", [])
        villains = result.get("villains", [])

        # 角色分布饼图
        role_counts = {
            "英雄": len(heroes),
            "受害者": len(victims),
            "反派": len(villains)
        }
        
        charts.append({
            "type": "pie",
            "title": "角色分布",
            "data": {
                "labels": list(role_counts.keys()),
                "datasets": [{
                    "data": list(role_counts.values()),
                    "backgroundColor": ["#28a745", "#ffc107", "#dc3545"]
                }]
            }
        })

        # 角色特征雷达图
        if heroes or victims or villains:
            role_features = self._analyze_role_features(heroes, victims, villains)
            charts.append({
                "type": "radar",
                "title": "角色特征分析",
                "data": role_features
            })

        return charts

    async def _generate_problem_scope_charts(self, result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成问题范围图表"""
        charts = []
        expansion = result.get("expansion_strategies", [])
        reduction = result.get("reduction_strategies", [])

        # 策略分布图
        strategy_counts = {
            "扩大化策略": len(expansion),
            "缩小化策略": len(reduction)
        }
        
        charts.append({
            "type": "doughnut",
            "title": "问题框架策略分布",
            "data": {
                "labels": list(strategy_counts.keys()),
                "datasets": [{
                    "data": list(strategy_counts.values()),
                    "backgroundColor": ["#17a2b8", "#6f42c1"]
                }]
            }
        })

        return charts

    async def _generate_causal_mechanism_charts(self, result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成因果机制图表"""
        charts = []
        causal_chains = result.get("causal_chains", [])

        # 因果链长度分布
        if causal_chains:
            chain_lengths = [len(chain.get("chain", [])) for chain in causal_chains]
            length_distribution = Counter(chain_lengths)
            
            charts.append({
                "type": "bar",
                "title": "因果链长度分布",
                "data": {
                    "labels": [f"{length}步" for length in sorted(length_distribution.keys())],
                    "datasets": [{
                        "label": "因果链数量",
                        "data": [length_distribution[length] for length in sorted(length_distribution.keys())],
                        "backgroundColor": "#fd7e14"
                    }]
                }
            })

        return charts

    def _generate_network_data(self, actors: List[Dict], relations: List[Dict]) -> Dict[str, Any]:
        """生成网络图数据"""
        nodes = []
        edges = []

        # 添加节点
        for i, actor in enumerate(actors):
            nodes.append({
                "id": actor.get("name", f"Actor_{i}"),
                "label": actor.get("name", f"Actor_{i}"),
                "type": actor.get("type", "未知"),
                "size": 20
            })

        # 添加边
        for relation in relations:
            edges.append({
                "from": relation.get("source", ""),
                "to": relation.get("target", ""),
                "label": relation.get("type", ""),
                "width": 2
            })

        return {"nodes": nodes, "edges": edges}

    def _analyze_role_features(self, heroes: List[Dict], victims: List[Dict], villains: List[Dict]) -> Dict[str, Any]:
        """分析角色特征"""
        # 简化的特征分析
        features = {
            "labels": ["积极性", "影响力", "描述详细度", "情感强度"],
            "datasets": []
        }

        # 英雄特征
        if heroes:
            features["datasets"].append({
                "label": "英雄",
                "data": [85, 75, 80, 90],
                "borderColor": "#28a745",
                "backgroundColor": "rgba(40, 167, 69, 0.2)"
            })

        # 受害者特征
        if victims:
            features["datasets"].append({
                "label": "受害者",
                "data": [30, 60, 70, 40],
                "borderColor": "#ffc107",
                "backgroundColor": "rgba(255, 193, 7, 0.2)"
            })

        # 反派特征
        if villains:
            features["datasets"].append({
                "label": "反派",
                "data": [20, 80, 75, 85],
                "borderColor": "#dc3545",
                "backgroundColor": "rgba(220, 53, 69, 0.2)"
            })

        return features

    async def generate_summary_metrics(self, doc_id: str) -> Optional[SummaryMetrics]:
        """生成摘要指标"""
        try:
            # 延迟初始化分析服务
            if self.analysis_service is None:
                from src.services.analysis_service import get_analysis_service
                self.analysis_service = await get_analysis_service()
            
            # 获取分析结果
            results = await self.analysis_service.get_results(doc_id)
            if not results:
                return None
            
            # 转换字符串键为TaskType枚举
            enum_results = {}
            for key, value in results.items():
                try:
                    task_type = TaskType(key)
                    enum_results[task_type] = value
                except ValueError:
                    logger.warning(f"Unknown task type: {key}")
                    continue
            
            # 计算总指标
            total_actors = 0
            total_relations = 0
            role_distribution = {}
            key_themes = []

            for task_type, result in enum_results.items():
                task_result = result.result if hasattr(result, 'result') else result
                
                if task_type == TaskType.ACTOR_RELATION:
                    total_actors += len(task_result.get("actors", []))
                    total_relations += len(task_result.get("relations", []))
                    
                    # 统计行为者类型
                    for actor in task_result.get("actors", []):
                        actor_type = actor.get("type", "未知")
                        role_distribution[actor_type] = role_distribution.get(actor_type, 0) + 1

                # 收集关键发现
                key_findings = task_result.get("key_findings", [])
                key_themes.extend(key_findings)

            return SummaryMetrics(
                total_actors=total_actors,
                total_relations=total_relations,
                role_distribution=role_distribution,
                sentiment_score=0.0,  # 简化版本
                complexity_score=min((total_actors + total_relations) / 10, 1.0),
                key_themes=list(set(key_themes))[:5]  # 最多5个主题
            )

        except Exception as e:
            logger.error(f"生成摘要指标失败: {e}")
            return None

    async def compare_documents(self, doc_id1: str, doc_id2: str) -> Dict[str, Any]:
        """比较两个文档"""
        try:
            summary1 = await self.generate_summary_metrics(doc_id1)
            summary2 = await self.generate_summary_metrics(doc_id2)

            if not summary1 or not summary2:
                return {}

            comparison = {
                "metrics_comparison": {
                    "actors": {
                        "doc1": summary1.total_actors,
                        "doc2": summary2.total_actors,
                        "difference": summary2.total_actors - summary1.total_actors
                    },
                    "relations": {
                        "doc1": summary1.total_relations,
                        "doc2": summary2.total_relations,
                        "difference": summary2.total_relations - summary1.total_relations
                    },
                    "complexity": {
                        "doc1": summary1.complexity_score,
                        "doc2": summary2.complexity_score,
                        "difference": summary2.complexity_score - summary1.complexity_score
                    }
                },
                "role_distribution_comparison": {
                    "doc1": summary1.role_distribution,
                    "doc2": summary2.role_distribution
                }
            }

            return comparison

        except Exception as e:
            logger.error(f"文档比较失败: {e}")
            return {}

    async def get_dashboard_data(self, limit: int = 10) -> Dict[str, Any]:
        """获取仪表板数据"""
        try:
            # 这里应该从数据库或文件系统获取最近的文档列表
            # 简化版本，返回模拟数据
            return {
                "total_documents": 25,
                "total_analyses": 89,
                "recent_documents": [
                    {
                        "doc_id": f"doc_{i}",
                        "title": f"文档 {i}",
                        "analysis_date": datetime.now().isoformat(),
                        "task_types": ["actor_relation", "role_framing"],
                        "total_actors": i * 2,
                        "total_relations": i * 3
                    }
                    for i in range(min(limit, 5))
                ],
                "system_stats": {
                    "avg_analysis_time": 2.5,
                    "success_rate": 0.95,
                    "most_common_task": "actor_relation"
                }
            }

        except Exception as e:
            logger.error(f"获取仪表板数据失败: {e}")
            return {}

    async def export_visualization_data(self, doc_id: str, format: str = "json") -> Dict[str, Any]:
        """导出可视化数据"""
        try:
            viz_data = await self.generate_visualization_data(doc_id)
            if not viz_data:
                return {}

            if format == "json":
                return {
                    "format": "json",
                    "data": viz_data.dict(),
                    "exported_at": datetime.now().isoformat()
                }
            elif format == "csv":
                # 简化的CSV导出
                csv_data = self._convert_to_csv(viz_data)
                return {
                    "format": "csv",
                    "data": csv_data,
                    "exported_at": datetime.now().isoformat()
                }
            else:
                raise ValueError(f"不支持的导出格式: {format}")

        except Exception as e:
            logger.error(f"导出可视化数据失败: {e}")
            return {}

    def _convert_to_csv(self, viz_data: VisualizationData) -> str:
        """转换为CSV格式"""
        lines = ["doc_id,title,analysis_date,task_types,total_actors,total_relations"]
        
        summary = viz_data.summary
        lines.append(f"{viz_data.doc_id},{viz_data.title},{viz_data.analysis_date},"
                    f"{','.join([t.value for t in viz_data.task_types])},"
                    f"{summary.get('total_actors', 0)},{summary.get('total_relations', 0)}")
        
        return "\n".join(lines)