{"id": "fe693f91-1918-4389-9107-16f745abe6b5", "task_type": "causal_mechanism", "name": "默认因果机制分析", "description": "分析政策文档中的因果关系和归因模式", "template": "请分析以下政策文档中的因果关系和归因模式。\n\n文档内容：\n{document}\n\n请执行以下分析任务：\n1. 识别文档中描述的因果链（事件A导致事件B导致事件C...）\n2. 分析归因模式（问题被归因于什么因素）\n3. 分析责任框架（谁被认为应对问题负责）\n4. 总结关键发现\n\n请以严格的JSON格式返回分析结果，结构如下（不要包含注释）：\n{\n    \"causal_chains\": [\n        {\n            \"sequence\": [\"原因1\", \"结果1/原因2\", \"结果2\"],\n            \"description\": \"因果链描述\"\n        }\n    ],\n    \"attribution_patterns\": [\n        {\n            \"target\": \"归因对象\",\n            \"factors\": [\"归因因素1\", \"归因因素2\"],\n            \"evidence\": [\"支持证据1\", \"支持证据2\"]\n        }\n    ],\n    \"responsibility_framing\": {\n        \"responsible_actors\": [\"负责任的行为者1\", \"负责任的行为者2\"],\n        \"absolved_actors\": [\"被免责的行为者1\", \"被免责的行为者2\"],\n        \"framing_strategy\": \"责任框架策略描述\"\n    },\n    \"key_findings\": [\"关键发现\"]\n}\n\n重要：只返回纯JSON格式的分析结果，不要包含任何额外的解释、注释或说明文字。", "version": "1.0.0", "is_active": true, "is_default": true, "created_at": "2025-08-28 14:02:00.952040", "updated_at": "2025-08-28 14:02:00.952040", "created_by": "user", "tags": ["默认", "因果", "归因", "系统"], "performance_score": null}