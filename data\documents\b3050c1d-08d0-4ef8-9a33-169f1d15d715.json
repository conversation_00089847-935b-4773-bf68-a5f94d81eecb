{"doc_id": "b3050c1d-08d0-4ef8-9a33-169f1d15d715", "title": "item179_US_What Makes a Good AI Benchmark", "text": "What Makes a Good AI Benchmark?\r\nResearch Outcomes Our research highlights that AI model benchmarks— including ones that are commonly used—vary significantly in their quality. For example, the widely used MMLU benchmark scored the lowest on usability (5.0) among all 24 benchmarks we evaluated, while another commonly used benchmark, GPQA, scored much higher (10.9). Yet it is common for developers to report results on both MMLU and GPQA without articulating their limitations or quality differences—for example, when introducing major models such as GPT-4, Claude-3, and Gemini. Similarly, the UK’s AI Safety Institute has developed a framework for evaluating LLMs that includes both MMLU and GPQA, while the EU AI Act specifically mentions the use of such benchmarks. This means policymakers and other actors often rely on conflicting and even misleading evaluations. Most benchmarks we evaluated also fail to distinguish between signals and noise. Developers may test two models with one benchmark but struggle to understand if different results reflect genuine performance differences or merely noisy outputs. Implementation remains another major weakness of AI benchmarks. Both FM and non-FM benchmarks, on average, achieve their highest scores at the design stage (10.6 and 11.1 on average, respectively) and their lowest scores at the implementation stage (5.5 and 7.4 on average, respectively), in line with previously reported implementation challenges. Of note, both FM and non-FM benchmarks are particularly weak on the reproducibility and interpretation of results: 17 of 24 benchmarks do not provide easyto-run scripts to replicate the results from initial papers, and only 4 of 24 benchmarks provide scripts to replicate some of the results. This is a problem. Reproducibility is important for validating benchmarks, but there are clear gaps when it comes to empowering developers, companies, civil society groups, and policymakers to evaluate and replicate results. We also found statistically significant correlations between design and usability scores for FM and nonFM benchmarks, suggesting that poorly designed benchmarks tend to be less usable. Finally, the strong discrepancies we found in AI benchmark quality highlight the urgent need for the development of best practices that can help ensure Reproducibility is important for validating benchmarks, but there are clear gaps when it comes to empowering developers, companies, civil society groups, and policymakers to evaluate and replicate results. 4Policy Brief  a minimum quality standard for AI benchmarks, especially given their increasing popularity and use in governance contexts. We developed a checklist of 46 best practices and encourage developers to adopt these during their process of creating benchmarks. These include, for example, making sure that the benchmark clearly describes how scores should be interpreted, makes its evaluation code publicly available, documents its limitations, and includes a feedback channel for users. In addition to identifying best practices, we outlined a variety of design considerations that benchmark developers should take into account when developing high-quality benchmarks but that were either context-dependent or harder to operationalize as concrete criteria. These include considering whether to prioritize broad concept benchmarks or those focused on specific AI contexts and domains; how to assess multimodal models across their multiple modalities; whether to prioritize dynamic versus static benchmarks in different situations; and how to prevent cheating and to ensure evaluations accurately reflect model performance. Policy Discussion AI benchmarks have already become a widely accepted tool for developers to compare model performance and in some cases inform decisions regarding downstream tasks. Policymakers, too, are increasingly working to understand AI benchmarks, promoting their use across companies and relying on their outcomes for policy decisions. Making AI benchmarks more practicable, transparent, and comparable is therefore crucial. What Makes a Good  AI Benchmark? Small changes can lead to significant improvements in overall benchmark practices. Our research underscores that policymakers should go one step further to articulate what makes AI benchmarks high-quality and what their limitations are—stating clearly in guidance documents that benchmarks vary in quality and approach, and that developers should strive to articulate the quality of their benchmark evaluations. These statements alone should clarify misconceptions about different benchmarks’ applicability and encourage industry to strengthen benchmarks’ interpretability and usability. Articulating quality metrics across benchmark life cycle stages (e.g., when they are designed vs. implemented) can strengthen evaluations. Our research also shows that small changes can lead to significant improvements in overall benchmark practices. Many criteria we laid out over the benchmarking life cycle’s five phases are relatively easy to implement, even for existing AI benchmarks. For example, adding code documentation and a point of contact to a benchmark are not time-consuming, but they can significantly enhance a benchmark’s usability, transparency, and accountability. Developers and civil society groups should make these kinds of measures an explicit best practice, and policymakers should integrate such recommendations into their AI evaluation guidance. 5Policy Brief  Policymakers should additionally encourage research on open challenges in AI benchmarking. These include quick saturation (benchmarks becoming quickly outdated because model capabilities advance so quickly that models achieve near-perfect scores), contamination (model developers training on benchmark data, such as when scraping the web), poor construct validity (not designing a test such that it accurately measures the concept it is intended to measure), and standardization of benchmark reporting. Future research on these topics could build on our concept of measuring the quality of benchmarks and focus further on empowering developers and evaluators to produce systematic, repeatable, and interpretable results for different AI applications. The greater adoption of AI benchmarks helps with systematic model evaluation, transparency, and, ideally, accountability. By adopting this framework and checklist to generate higher-quality benchmarks, we hope that developers, policymakers, and other stakeholders can make better-informed model selections and decisions for downstream tasks—while potentially reducing risks and improving outcomes in high-stakes applications.", "metadata": {"original_filename": "item179_US_What Makes a Good AI Benchmark.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:51:55.384965", "updated_at": "2025-08-28T21:51:55.384965", "word_count": 6676}