#!/usr/bin/env python3
"""
🕐 Task 5: 时间框架分析模块
政策叙事中的时间维度分析 - PANTOS扩展分析模块
"""

import re
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class TemporalDirection(Enum):
    """时间方向枚举"""
    PAST = "past"
    PRESENT = "present" 
    FUTURE = "future"
    TIMELESS = "timeless"

class UrgencyLevel(Enum):
    """紧迫性级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class TemporalPattern:
    """时间模式"""
    pattern_type: str
    direction: TemporalDirection
    indicators: List[str]
    evidence: List[str]
    strength: float
    explanation: str

@dataclass
class UrgencyIndicator:
    """紧迫性指示器"""
    level: UrgencyLevel
    triggers: List[str]
    evidence: List[str]
    strategy: str
    impact_prediction: str

@dataclass
class HistoricalAnalogy:
    """历史类比"""
    analogy_type: str
    historical_reference: str
    current_situation: str
    comparison_points: List[str]
    persuasive_function: str
    evidence: str

class TemporalFramingAnalyzer:
    """时间框架分析器"""
    
    def __init__(self):
        # 时态指示词词典
        self.temporal_indicators = {
            TemporalDirection.PAST: [
                "过去", "以前", "曾经", "历史", "传统", "既往", "前期", 
                "早期", "原来", "此前", "之前", "历来", "过往", "往昔",
                "昔日", "旧时", "从前", "先前", "历史上", "回顾"
            ],
            TemporalDirection.PRESENT: [
                "现在", "目前", "当前", "今天", "如今", "此时", "现今",
                "眼下", "当下", "此刻", "现阶段", "目前阶段", "现状",
                "当前形势", "现实", "实际", "当前情况"
            ],
            TemporalDirection.FUTURE: [
                "未来", "将来", "今后", "以后", "下一步", "接下来", 
                "明天", "远期", "长远", "前景", "发展", "即将",
                "将要", "未来发展", "前瞻", "预期", "展望", "规划"
            ]
        }
        
        # 紧迫性指示词
        self.urgency_indicators = {
            UrgencyLevel.CRITICAL: [
                "紧急", "急迫", "刻不容缓", "迫在眉睫", "十万火急",
                "火烧眉毛", "争分夺秒", "时不我待", "当务之急",
                "燃眉之急", "危急", "告急", "岌岌可危"
            ],
            UrgencyLevel.HIGH: [
                "尽快", "抓紧", "加快", "马上", "立即", "赶紧",
                "及时", "迅速", "快速", "加紧", "尽早", "从速",
                "不拖延", "不等不靠", "第一时间"
            ],
            UrgencyLevel.MEDIUM: [
                "适时", "逐步", "稳步", "有序", "按步骤", "分阶段",
                "计划性", "渐进", "循序渐进", "稳妥推进"
            ],
            UrgencyLevel.LOW: [
                "长期", "逐渐", "慢慢", "缓慢", "从容", "不急",
                "徐徐", "渐次", "慢慢来", "细水长流"
            ]
        }
        
        # 历史类比模式
        self.historical_patterns = [
            "吸取.*教训", "前车之鉴", "历史经验", "以史为鉴",
            "重蹈覆辙", "前人.*经验", "历史告诉我们", "古人云",
            "历史证明", "历史表明", "回顾历史", "纵观历史"
        ]
    
    async def analyze_temporal_framing(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """分析文档的时间框架策略"""
        
        content = document.get("text", "")
        doc_id = document.get("doc_id", "")
        title = document.get("title", "")
        
        logger.info(f"开始时间框架分析: {doc_id}")
        
        try:
            # 1. 时态分布分析
            tense_analysis = await self._analyze_tense_distribution(content)
            
            # 2. 紧迫性构建分析
            urgency_analysis = await self._analyze_urgency_construction(content)
            
            # 3. 历史类比检测
            historical_analogies = await self._detect_historical_analogies(content)
            
            # 4. 未来投射分析
            future_projections = await self._analyze_future_projections(content)
            
            # 5. 时间连贯性评估
            temporal_coherence = await self._assess_temporal_coherence(
                tense_analysis, urgency_analysis, historical_analogies
            )
            
            # 6. 叙事时间策略识别
            narrative_strategies = await self._identify_narrative_time_strategies(
                content, tense_analysis, urgency_analysis
            )
            
            result = {
                "doc_id": doc_id,
                "title": title,
                "temporal_distribution": tense_analysis,
                "urgency_construction": urgency_analysis,
                "historical_analogies": historical_analogies,
                "future_projections": future_projections,
                "temporal_coherence": temporal_coherence,
                "narrative_strategies": narrative_strategies,
                "analysis_metadata": {
                    "analyzer_version": "temporal_framing_v1.0",
                    "analysis_time": datetime.now().isoformat(),
                    "confidence_score": self._calculate_confidence_score(
                        tense_analysis, urgency_analysis, historical_analogies
                    )
                }
            }
            
            logger.info(f"时间框架分析完成: {doc_id}")
            return result
            
        except Exception as e:
            logger.error(f"时间框架分析失败: {e}")
            return {
                "doc_id": doc_id,
                "error": str(e),
                "analysis_time": datetime.now().isoformat()
            }
    
    async def _analyze_tense_distribution(self, content: str) -> Dict[str, Any]:
        """分析时态分布"""
        
        # 统计各时间方向的词汇出现频率
        temporal_counts = {direction.value: 0 for direction in TemporalDirection}
        temporal_examples = {direction.value: [] for direction in TemporalDirection}
        
        sentences = re.split(r'[。！？]', content)
        
        for sentence in sentences:
            if not sentence.strip():
                continue
                
            for direction in TemporalDirection:
                if direction == TemporalDirection.TIMELESS:
                    continue
                    
                indicators = self.temporal_indicators[direction]
                for indicator in indicators:
                    if indicator in sentence:
                        temporal_counts[direction.value] += 1
                        if len(temporal_examples[direction.value]) < 3:
                            temporal_examples[direction.value].append(sentence.strip())
        
        # 计算比例和主导时间方向
        total_temporal = sum(temporal_counts.values())
        
        if total_temporal > 0:
            proportions = {
                direction: count / total_temporal 
                for direction, count in temporal_counts.items()
            }
            dominant_direction = max(proportions.items(), key=lambda x: x[1])[0]
        else:
            proportions = {direction: 0 for direction in temporal_counts.keys()}
            dominant_direction = "present"  # 默认为现在时
        
        return {
            "temporal_counts": temporal_counts,
            "temporal_proportions": proportions,
            "dominant_direction": dominant_direction,
            "temporal_examples": temporal_examples,
            "temporal_diversity": len([p for p in proportions.values() if p > 0.1])
        }
    
    async def _analyze_urgency_construction(self, content: str) -> Dict[str, Any]:
        """分析紧迫性构建策略"""
        
        urgency_patterns = []
        sentences = re.split(r'[。！？]', content)
        
        for sentence in sentences:
            if not sentence.strip():
                continue
            
            for level in UrgencyLevel:
                indicators = self.urgency_indicators[level]
                found_indicators = [ind for ind in indicators if ind in sentence]
                
                if found_indicators:
                    urgency_patterns.append({
                        "urgency_level": level.value,
                        "indicators": found_indicators,
                        "evidence": sentence.strip(),
                        "strategy": self._identify_urgency_strategy(sentence, level),
                        "impact_assessment": self._assess_urgency_impact(sentence, level)
                    })
        
        # 分析整体紧迫性倾向
        if urgency_patterns:
            level_counts = {}
            for pattern in urgency_patterns:
                level = pattern["urgency_level"]
                level_counts[level] = level_counts.get(level, 0) + 1
            
            dominant_urgency = max(level_counts.items(), key=lambda x: x[1])[0]
        else:
            dominant_urgency = "medium"
        
        return {
            "urgency_patterns": urgency_patterns,
            "dominant_urgency_level": dominant_urgency,
            "urgency_frequency": len(urgency_patterns),
            "urgency_strategies": self._categorize_urgency_strategies(urgency_patterns)
        }
    
    async def _detect_historical_analogies(self, content: str) -> List[Dict[str, Any]]:
        """检测历史类比"""
        
        analogies = []
        sentences = re.split(r'[。！？]', content)
        
        for sentence in sentences:
            if not sentence.strip():
                continue
            
            # 检测历史类比模式
            for pattern in self.historical_patterns:
                if re.search(pattern, sentence):
                    analogy = {
                        "analogy_type": self._classify_analogy_type(sentence),
                        "historical_reference": self._extract_historical_reference(sentence),
                        "current_situation": self._extract_current_context(sentence),
                        "comparison_function": self._analyze_comparison_function(sentence),
                        "persuasive_strategy": self._identify_persuasive_strategy(sentence),
                        "evidence": sentence.strip()
                    }
                    analogies.append(analogy)
        
        return analogies
    
    async def _analyze_future_projections(self, content: str) -> Dict[str, Any]:
        """分析未来投射"""
        
        future_patterns = []
        prediction_keywords = [
            "预计", "预期", "预测", "预见", "展望", "前瞻",
            "预判", "估计", "可能", "或将", "有望", "势必"
        ]
        
        sentences = re.split(r'[。！？]', content)
        
        for sentence in sentences:
            if not sentence.strip():
                continue
            
            # 检测未来投射模式
            found_predictions = [kw for kw in prediction_keywords if kw in sentence]
            
            if found_predictions or any(ind in sentence for ind in self.temporal_indicators[TemporalDirection.FUTURE]):
                future_patterns.append({
                    "projection_type": self._classify_projection_type(sentence),
                    "prediction_scope": self._analyze_prediction_scope(sentence),
                    "certainty_level": self._assess_certainty_level(sentence),
                    "time_horizon": self._extract_time_horizon(sentence),
                    "evidence": sentence.strip()
                })
        
        return {
            "future_projections": future_patterns,
            "projection_count": len(future_patterns),
            "projection_themes": self._extract_projection_themes(future_patterns)
        }
    
    async def _assess_temporal_coherence(self, tense_analysis: Dict, 
                                       urgency_analysis: Dict,
                                       historical_analogies: List) -> Dict[str, Any]:
        """评估时间连贯性"""
        
        # 时间一致性分析
        dominant_direction = tense_analysis["dominant_direction"]
        dominant_urgency = urgency_analysis["dominant_urgency_level"]
        
        # 连贯性评分算法
        coherence_score = 0.5  # 基础分
        
        # 时间方向一致性加分
        if dominant_direction == "future" and dominant_urgency in ["high", "critical"]:
            coherence_score += 0.2
        elif dominant_direction == "past" and len(historical_analogies) > 0:
            coherence_score += 0.2
        elif dominant_direction == "present" and dominant_urgency in ["medium", "high"]:
            coherence_score += 0.1
        
        # 时间多样性检查
        temporal_diversity = tense_analysis["temporal_diversity"]
        if temporal_diversity >= 2:
            coherence_score += 0.1
        
        # 归一化到0-1范围
        coherence_score = min(1.0, max(0.0, coherence_score))
        
        return {
            "coherence_score": coherence_score,
            "dominant_temporal_strategy": self._identify_dominant_strategy(
                dominant_direction, dominant_urgency, historical_analogies
            ),
            "temporal_tensions": self._identify_temporal_tensions(
                tense_analysis, urgency_analysis
            ),
            "narrative_flow_quality": self._assess_narrative_flow(coherence_score)
        }
    
    async def _identify_narrative_time_strategies(self, content: str,
                                                tense_analysis: Dict,
                                                urgency_analysis: Dict) -> List[Dict[str, Any]]:
        """识别叙事时间策略"""
        
        strategies = []
        
        # 策略1: 历史正当性构建
        if tense_analysis["temporal_proportions"]["past"] > 0.3:
            strategies.append({
                "strategy_type": "历史正当性构建",
                "description": "通过引用历史经验和传统来建立政策的正当性",
                "strength": tense_analysis["temporal_proportions"]["past"],
                "examples": tense_analysis["temporal_examples"]["past"][:2]
            })
        
        # 策略2: 未来愿景描绘
        if tense_analysis["temporal_proportions"]["future"] > 0.4:
            strategies.append({
                "strategy_type": "未来愿景描绘", 
                "description": "通过描绘美好未来来动员支持",
                "strength": tense_analysis["temporal_proportions"]["future"],
                "examples": tense_analysis["temporal_examples"]["future"][:2]
            })
        
        # 策略3: 紧迫性动员
        if urgency_analysis["dominant_urgency_level"] in ["high", "critical"]:
            strategies.append({
                "strategy_type": "紧迫性动员",
                "description": "通过强调时间紧迫性来推动行动",
                "strength": urgency_analysis["urgency_frequency"] / len(content.split('。')),
                "examples": [p["evidence"] for p in urgency_analysis["urgency_patterns"][:2]]
            })
        
        # 策略4: 现实问题聚焦
        if tense_analysis["temporal_proportions"]["present"] > 0.5:
            strategies.append({
                "strategy_type": "现实问题聚焦",
                "description": "专注于当前现实问题的分析和解决",
                "strength": tense_analysis["temporal_proportions"]["present"],
                "examples": tense_analysis["temporal_examples"]["present"][:2]
            })
        
        return strategies
    
    def _identify_urgency_strategy(self, sentence: str, level: UrgencyLevel) -> str:
        """识别紧迫性策略"""
        if level == UrgencyLevel.CRITICAL:
            return "危机动员"
        elif level == UrgencyLevel.HIGH:
            return "行动催促"
        elif level == UrgencyLevel.MEDIUM:
            return "稳步推进"
        else:
            return "长期规划"
    
    def _assess_urgency_impact(self, sentence: str, level: UrgencyLevel) -> str:
        """评估紧迫性影响"""
        if "决策" in sentence or "政策" in sentence:
            return "政策制定影响"
        elif "行动" in sentence or "实施" in sentence:
            return "执行推动影响"
        elif "支持" in sentence or "参与" in sentence:
            return "公众动员影响"
        else:
            return "一般性影响"
    
    def _classify_analogy_type(self, sentence: str) -> str:
        """分类类比类型"""
        if "教训" in sentence or "失败" in sentence:
            return "负面警示类比"
        elif "成功" in sentence or "经验" in sentence:
            return "正面经验类比"
        elif "变化" in sentence or "发展" in sentence:
            return "发展趋势类比"
        else:
            return "一般历史类比"
    
    def _extract_historical_reference(self, sentence: str) -> str:
        """提取历史引用"""
        # 简化实现，实际可以使用NER等技术
        historical_markers = ["改革开放", "文革", "新中国", "古代", "明清", "近代"]
        for marker in historical_markers:
            if marker in sentence:
                return marker
        return "历史经验"
    
    def _extract_current_context(self, sentence: str) -> str:
        """提取当前语境"""
        return sentence[:50] + "..." if len(sentence) > 50 else sentence
    
    def _analyze_comparison_function(self, sentence: str) -> str:
        """分析比较功能"""
        if "不能" in sentence or "避免" in sentence:
            return "警示功能"
        elif "应该" in sentence or "要" in sentence:
            return "指导功能"
        else:
            return "说明功能"
    
    def _identify_persuasive_strategy(self, sentence: str) -> str:
        """识别说服策略"""
        if "证明" in sentence:
            return "理性论证"
        elif "告诉" in sentence:
            return "权威引用"
        else:
            return "经验借鉴"
    
    def _classify_projection_type(self, sentence: str) -> str:
        """分类投射类型"""
        if "发展" in sentence or "增长" in sentence:
            return "发展预测"
        elif "风险" in sentence or "挑战" in sentence:
            return "风险预警"
        elif "机遇" in sentence or "前景" in sentence:
            return "机遇展望"
        else:
            return "一般预测"
    
    def _analyze_prediction_scope(self, sentence: str) -> str:
        """分析预测范围"""
        if "全国" in sentence or "全球" in sentence:
            return "宏观预测"
        elif "行业" in sentence or "领域" in sentence:
            return "行业预测"
        else:
            return "局部预测"
    
    def _assess_certainty_level(self, sentence: str) -> str:
        """评估确定性水平"""
        high_certainty = ["必将", "势必", "一定会", "确定"]
        medium_certainty = ["可能", "或许", "大概", "预计"]
        
        if any(word in sentence for word in high_certainty):
            return "高确定性"
        elif any(word in sentence for word in medium_certainty):
            return "中等确定性"
        else:
            return "低确定性"
    
    def _extract_time_horizon(self, sentence: str) -> str:
        """提取时间跨度"""
        if "短期" in sentence or "近期" in sentence:
            return "短期"
        elif "中期" in sentence:
            return "中期"
        elif "长期" in sentence or "远期" in sentence:
            return "长期"
        else:
            return "未明确"
    
    def _extract_projection_themes(self, future_patterns: List) -> List[str]:
        """提取投射主题"""
        themes = set()
        for pattern in future_patterns:
            if "发展" in pattern["evidence"]:
                themes.add("发展主题")
            if "改革" in pattern["evidence"]:
                themes.add("改革主题")
            if "创新" in pattern["evidence"]:
                themes.add("创新主题")
        return list(themes)
    
    def _categorize_urgency_strategies(self, urgency_patterns: List) -> Dict[str, int]:
        """分类紧迫性策略"""
        strategies = {}
        for pattern in urgency_patterns:
            strategy = pattern["strategy"]
            strategies[strategy] = strategies.get(strategy, 0) + 1
        return strategies
    
    def _identify_dominant_strategy(self, dominant_direction: str, 
                                  dominant_urgency: str,
                                  historical_analogies: List) -> str:
        """识别主导策略"""
        if dominant_direction == "future" and dominant_urgency in ["high", "critical"]:
            return "未来紧迫性动员"
        elif dominant_direction == "past" and len(historical_analogies) > 0:
            return "历史经验借鉴"
        elif dominant_direction == "present" and dominant_urgency == "high":
            return "现实问题紧急应对"
        else:
            return "均衡时间策略"
    
    def _identify_temporal_tensions(self, tense_analysis: Dict, urgency_analysis: Dict) -> List[str]:
        """识别时间张力"""
        tensions = []
        
        # 检测时间方向冲突
        proportions = tense_analysis["temporal_proportions"]
        if proportions["past"] > 0.3 and proportions["future"] > 0.3:
            tensions.append("历史与未来的张力")
        
        # 检测紧迫性与时间跨度冲突
        if urgency_analysis["dominant_urgency_level"] == "critical" and proportions["future"] > 0.4:
            tensions.append("即时紧迫与长期规划的矛盾")
        
        return tensions
    
    def _assess_narrative_flow(self, coherence_score: float) -> str:
        """评估叙事流畅度"""
        if coherence_score >= 0.8:
            return "流畅连贯"
        elif coherence_score >= 0.6:
            return "基本连贯"
        elif coherence_score >= 0.4:
            return "部分冲突"
        else:
            return "明显矛盾"
    
    def _calculate_confidence_score(self, tense_analysis: Dict, 
                                  urgency_analysis: Dict,
                                  historical_analogies: List) -> float:
        """计算置信度分数"""
        
        # 基于数据丰富程度计算置信度
        base_score = 0.3
        
        # 时态数据丰富度
        temporal_richness = sum(1 for count in tense_analysis["temporal_counts"].values() if count > 0)
        base_score += temporal_richness * 0.1
        
        # 紧迫性数据丰富度
        if urgency_analysis["urgency_frequency"] > 0:
            base_score += min(0.3, urgency_analysis["urgency_frequency"] * 0.1)
        
        # 历史类比数据
        if len(historical_analogies) > 0:
            base_score += min(0.2, len(historical_analogies) * 0.1)
        
        return min(1.0, base_score)

# 创建全局分析器实例
temporal_analyzer = TemporalFramingAnalyzer()

async def analyze_temporal_framing(document: Dict[str, Any]) -> Dict[str, Any]:
    """对外接口：时间框架分析"""
    return await temporal_analyzer.analyze_temporal_framing(document)

if __name__ == "__main__":
    # 测试时间框架分析器
    import asyncio
    
    async def test_temporal_analyzer():
        test_document = {
            "doc_id": "temporal_test_001",
            "title": "政策时间框架测试文档",
            "text": """
            政府将在未来五年内制定新的人工智能监管政策，确保AI技术的安全发展。
            历史经验告诉我们，技术创新必须与监管政策相协调。
            当前形势下，我们必须抓紧时间推进相关工作，不能再拖延了。
            预计到2030年，人工智能产业将迎来重大发展机遇。
            吸取过去的教训，我们要避免重蹈覆辙。
            现在正是关键时刻，时不我待，必须立即行动。
            """
        }
        
        result = await analyze_temporal_framing(test_document)
        print(json.dumps(result, indent=2, ensure_ascii=False))
    
    asyncio.run(test_temporal_analyzer())
