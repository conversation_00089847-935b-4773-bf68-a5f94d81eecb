{"doc_id": "00ea5fb4-da9d-4a9f-870b-91ddaf747e3c", "title": "item094_US_Controlling Large Language Model Outputs A Primer", "text": "Introduction\r\nLarge language models (LLMs) are powerful AI models that can generate all kinds of\r\ntext outputs, from poetry and professional emails to recipes and computer code. They\r\nhave diffused broadly in recent months and are projected to have important societal\r\nramifications. For example, venture capitalists and large tech companies alike have\r\npoured funding into LLM development and application-layer products built on top of\r\nthese tools, and researchers expect LLMs to be broadly integrated into society and the\r\neconomy in the years ahead.1\r\nDespite their popularity and promise, LLMs are also capable of generating outputs that\r\nare hurtful, untrue, and, at times, even dangerous. In response to their increasing\r\npopularity, many people have asked: How do AI developers control the text that a\r\nlanguage model generates?\r\nIn this primer, we tackle this question directly and present a high-level overview of\r\nhow AI developers attempt to prevent LLMs from outputting harmful or otherwise\r\nundesirable text.* In the first section, we begin with a brief motivation of why\r\ndevelopers seek to control or influence model outputs. In the second section, we\r\ndescribe some relevant features of the language model development pipeline before\r\ndiving deeper into four classes of techniques that developers often use in practice in\r\nthe third section. The fourth section provides context on why the open vs. private\r\nmodel paradigm further complicates developers’ efforts to control and safeguard the\r\noutputs of their models.\r\nA common theme across the various methods to limit harmful content is that none are\r\nperfect. Whether a particular intervention is successful frequently depends on how\r\ndedicated the user is to generating malicious or subversive text, and may be a matter\r\nof degree (how frequently the model produces undesirable text) rather than absolutes\r\n(whether it will produce the undesirable text at all).\r\n* These practices can also be thought of as “aligning” LLMs with their developers’ policies or guardrails.\r\nAI alignment is a broader research area that generally focuses on ensuring AI systems behave in ways\r\nthat correspond to human values or human intentions.\r\nCenter for Security and Emerging Technology | 3\r\nWhy Control Large Language Model Outputs?\r\nAlthough interacting with a language model may occasionally feel like interacting with\r\na real person, these systems are not human. Instead, language models are essentially\r\ncomplex probability-calculating machines. They establish relations between language\r\ntokens—words, phrases, parts of words, or even punctuation marks and grammatical\r\nsymbols—and calculate the probability for each of them to come next in response to a\r\ngiven prompt. The models repeatedly choose one of the most likely tokens until their\r\noutputs are complete. Importantly, this means that language models have no\r\nunderlying understanding of factualness or truthfulness, nor are they retrieving\r\ninformation from any single source. They are more akin to “improv machines”2: they\r\nexcel at replicating patterns but have no built-in way to verify whether or not their\r\noutputs are useful, correct, or harmful.3\r\nWhile a full taxonomy of risks falls outside the scope of this piece, we highlight some\r\nof the risks that motivate interest in controlling LLM outputs.4 Firstly, some risks result\r\nfrom ordinary users simply receiving incorrect information. Users have already shown\r\na propensity to misunderstand the limitations of these systems and inappropriately cite\r\nLLMs, thinking they provide factual information5 (an example of what AI researchers\r\ncall “overreliance”6). The range of potential failures is vast. Users depending on the\r\nsystem for health information who are fed false advice could put themselves at risk.\r\nUsers turning to models for information about politics who receive false information\r\nmay lose faith in candidates without justification, undermining the democratic process.\r\nAs people use language models more frequently, the risks associated with\r\noverreliance will likely grow.\r\nSecondly, content does not need to be demonstrably false to cause harm. This leads to\r\nanother set of concerns that can occur when language models produce text that is\r\nbiased (e.g., regarding race, gender, religion, or other categories) or toxic. Research has\r\ntested and found evidence of biases related to political ideology, religion, gender, and\r\nmore in specific models.7 Another line of research has traced biases in language\r\nmodels to the training data and noted that content excluded from training data based\r\non certain keywords can disproportionately remove text from and about members of\r\nvarious minority groups.8 Toxic content from LLMs may be particularly problematic if\r\nshown to children or other vulnerable groups.\r\nFinally, there are also worries about bad actors using language models intentionally\r\nfor “malicious use.”9 One worst-case scenario that has received public attention is the\r\nrisk of a bad actor using a language model to learn how to create a homegrown bomb\r\nCenter for Security and Emerging Technology | 4\r\nor a bioweapon, although future research is needed to understand the relative risk\r\n(e.g., assessing the risk compared to information already available on Google).10 Other\r\ntroubling scenarios center on different types of malicious behavior such as the use of\r\nlanguage models to facilitate hacking, scamming, or generating disinformation\r\narticles.11 In all these cases, a strategy to prevent a model from outputting harmful\r\nmaterials would likely center on having it refuse to return such content outright.\r\nHow Large Language Models are Developed\r\nAs described above, language models are essentially complex probability-calculating\r\nmachines. In order to understand how AI developers attempt to control their outputs, it\r\nis useful to first understand the process by which they are created, and how every\r\nstage of this process influences the system that ultimately ends up interacting with\r\nhuman end-users.\r\n“Language model” is a general category describing a class of AI models that generate\r\nnatural language text outputs. Large language models, or LLMs, are particularly\r\npowerful language models that are trained on especially large quantities of text, much\r\nof it scraped from the open internet.12 While there is no strictly defined boundary\r\nbetween a regular language model and an LLM, a model’s “largeness” is generally a\r\nquestion of scale, both in terms of data and computational power. Training an LLM is a\r\nlong and computationally expensive process, with some of today’s most cutting-edge\r\nmodels requiring thousands of powerful computer chips and hundreds of millions of\r\ndollars to create.13 The most capable models are currently privately developed and\r\nprotected as corporate intellectual property, but increasingly capable alternatives have\r\nbeen released openly for public use or adaptation.\r\nFirst, models are pre-trained on large general-purpose text datasets to learn\r\ncorrelations among tokens found in this natural language text. While some training\r\ndatasets—consisting mostly of data scraped from publicly accessible web archives—\r\nare available for open inspection and use,14 the exact composition of data sources used\r\nto train today’s LLMs is largely unknown. Even AI developers generally do not have\r\ntotal visibility into the contents of their training datasets because the quantity of data\r\nrequired to pre-train an LLM is often in the scale of hundreds of terabytes.15\r\nAfter this initial training, they are commonly fine-tuned, at least once, on smaller, more\r\nspecialized datasets to improve their performance in a specific area. There are different\r\ntypes of fine-tuning for different purposes: reinforcement learning with human\r\nfeedback attempts (described further in the following section) to guide models’\r\nbehavior using input from humans, while other types of fine-tuning might train a model\r\nCenter for Security and Emerging Technology | 5\r\nmore on data for a certain application or style in order to improve the model’s\r\ncapability to generate that kind of text specifically. These training steps are often\r\nrepeated, with multiple rounds of iterative testing and evaluation to monitor model\r\nperformance.\r\nFinally, some fully-trained models are deployed for use, whether through a userfacing\r\ninterface like a chatbot or via an API (application programming interface). The\r\nsame model can be deployed in different forms; for example, OpenAI’s GPT-4 has\r\nbeen deployed as both the LLM that powers ChatGPT and can also be accessed\r\ndirectly via its API, which allows third-party developers to integrate it into their\r\nsoftware products without having direct access to the model. Another popular option\r\nfor developers is to open-source their model, which allows anyone to access its\r\nunderlying code, fine-tune it to their own specifications, and use it to build their own\r\napplications.\r\nFigure 1: Stages in the AI development pipeline and some associated language model\r\ncontrol techniques.\r\nSource: CSET.\r\nEach of these steps in the development process offers opportunities to shape the\r\nmodel’s behavior. In the following section, we discuss four classes of techniques that\r\nLLM developers use to steer outputs.\r\nCenter for Security and Emerging Technology | 6\r\nFour Techniques to Control LLM Outputs\r\n1. Editing Pre-training Data\r\nSince language models’ predictive power derives from correlations in the text they are\r\ntrained on, a common misconception about LLMs is that their outputs can be easily\r\nsteered by manipulating or editing their training data. A model whose training data\r\ncontains no reference to a particular word, for instance, is extremely unlikely to output\r\nanything containing that word. However, real-world pre-training is much more\r\ncomplicated. Considering the sheer volume of data that these models are pre-trained\r\non, it is extremely difficult to predict how changing their training data will affect their\r\nperformance or their propensity to output certain types of content.\r\nFor example, one study found that filtering a dataset reduced one language model’s\r\nlikelihood of generating harmful text, but the filtered versions also consistently\r\nperformed worse on standard performance benchmarks than their unfiltered\r\ncounterparts.16 Many LLM developers are wary of techniques that decrease model\r\nperformance, especially if other techniques can also be used to control outputs. Data\r\nfiltering methods can also backfire and lead to other unwanted outcomes, like erasing\r\ndialect patterns or marginalizing certain groups within the model’s training data.17 And\r\nwhile data augmentation—supplementing training data with examples of desired\r\noutcomes—has shown some promise, it is very difficult to effectively scale in order to\r\nreduce bias in large models.18\r\nUltimately, while training data manipulation is theoretically a powerful mechanism to\r\ncontrol model behavior, it is not a panacea for preventing many types of harmful\r\noutput, especially when meaning and harm are context dependent.19 Even though\r\nfactors like content filters and data sources can ultimately have significant effects on\r\nthe fully trained model’s behavior,20 researchers have yet to fully understand exactly\r\nhow to manipulate data in a way that will have meaningful impacts on the resulting\r\nmodel while minimizing performance loss. Smaller, specialized language models that\r\nare pre-trained on curated datasets are likely to have more success with data filtration\r\nor augmentation, but LLM developers are likely to rely on other methods in order to\r\nsteer their models.\r\nCenter for Security and Emerging Technology | 7\r\n2. Supervised Fine-Tuning\r\nOnce a model has been pre-trained, developers can continue to adjust its behavior by\r\ntraining it further on a specialized dataset. This process, known as supervised finetuning,\r\nis one of the most common ways to modify a language model, usually in an\r\neffort to improve its performance in a particular area. To make a general-purpose\r\nmodel like OpenAI’s GPT-4 better at math, for instance, an intuitive solution is to train\r\nthe model on math problems to improve its ability to recognize patterns in that\r\nparticular domain. The more high-quality data a model has been exposed to that is\r\nrelevant to a specific topic, the better the model will be at predicting the next token in\r\nits output in a way that is likely to be useful to human users.\r\nSupervised fine-tuning can be quite powerful in the right context when the right kind\r\nof data is available, and is one of the best ways to specialize a model to a specific\r\ndomain or use case. (“Supervised,” in this context, refers to the fact that the model is\r\nprovided with labeled data and thus does not have to perform the prerequisite step of\r\nlearning patterns and associations within the data.) For example, fine-tuning an LLM\r\non a collection of datasets described via instructions—consisting of specific tasks or\r\nrequests, such as “can we infer the following?” or “translate this text into Spanish”—\r\nsignificantly improves the model’s ability to respond to prompts in natural language.21\r\nThis technique, now known as instruction tuning, has been instrumental in creating\r\nLLM chatbots that can interpret all different kinds of inputs, from simple questions and\r\ndeclarative statements to lists of multi-step instructions.\r\nFine-tuning is a broadly applicable specialization technique that can be applied in\r\ncertain contexts to steer model behavior. Some research has shown that this technique\r\ncan not only improve a model’s performance in a particular area22 but can also\r\ncompensate for bias inherited from the pretrained model.23 These biases, which can\r\ninclude those related to protected categories like race or gender, stem from statistical\r\npatterns that the model has learned from its training data. A training dataset that only\r\nconsists of images of male doctors, for example, will result in a model that will\r\nconsistently produce images of men when prompted with “doctor.” Fine-tuning the\r\nmodel on a more balanced dataset can be one way to correct this issue.\r\nHowever, effective supervised fine-tuning depends on access to specialized and highquality\r\ndatasets, which may not be available in all domains or accurately capture the\r\nbehavior that researchers are attempting to control. Researchers have therefore looked\r\nto develop alternative techniques that are either not as reliant on specialized data or\r\nthat allow for a more flexible way to steer an LLM’s behavior.\r\nCenter for Security and Emerging Technology | 8\r\n3. Reinforcement Learning with Human Feedback (RLHF) and Constitutional AI\r\nTwo techniques often used to complement supervised fine-tuning employ\r\nreinforcement learning, which is the process of training a machine learning model to\r\nmake decisions via many iterations of trial and error. Over the course of the training\r\nprocess, the model receives either negative or positive feedback which gradually\r\n“teaches” it to take the series of actions that will maximize the amount of positive\r\nfeedback. Given the right conditions, reinforcement learning can be extremely effective\r\nand powerful—Google DeepMind’s AlphaGo Zero system, an earlier version of which\r\nfamously beat the human Go world champion in 2016,24 was primarily trained using\r\nreinforcement learning. While playing millions of games against itself over the course\r\nof several days, AlphaGo Zero repeatedly updated its own parameters to select better\r\nand better moves.25\r\nSince reinforcement learning already incorporates a built-in feedback process, AI\r\nresearchers leveraged it to create new techniques for fine-tuning LLMs. Reinforcement\r\nlearning with human feedback (RLHF) is a technique in which an LLM is fine-tuned\r\nwith the help of a different machine-learning model, known as a “reward model.” This\r\nreward model is trained on some of the original LLM’s text outputs, which human\r\nannotators have ranked based on some set of guidelines or preferences. The goal of\r\nthe reward model is to encode human preferences: once given some text input, it\r\noutputs a numerical score which reflects how likely humans are to prefer that text. The\r\nreward model then serves as the basis for the feedback mechanism used to fine-tune\r\nthe original LLM: as the original LLM outputs text, the reward model “scores” that\r\noutput and with each iteration, the original LLM adjusts its output to improve its\r\n“score.”26 Put more simply, RLHF attempts to train an LLM to generate outputs that\r\nhumans are more likely to deem acceptable. It’s perhaps most well-known for its role\r\nin turning OpenAI’s GPT-3.5 into ChatGPT,27 and has been remarkably successful at\r\nproducing LLMs that interact with users in human-like ways.\r\nUnlike supervised fine-tuning, which is typically used for creating a specialized model\r\nand does not necessarily involve steering a model based on any sense of “right” or\r\n“wrong,” RLHF centers on the principle that human preferences should play a role in\r\nhow an LLM behaves. The “human feedback” aspect of RLHF is its central component\r\nand also its greatest limitation. For example, in 2022 a team of OpenAI researchers\r\nhired 40 contractors to create and label a dataset of human preferences.28 Today, data\r\nannotators around the world spend hours rating interactions with pre-deployed\r\nversions of AI systems like ChatGPT and Google’s Sparrow.29 As long as human labor\r\nis necessary for RLHF, LLM creators will naturally face limitations on how much human\r\nCenter for Security and Emerging Technology | 9\r\nfeedback their models will receive because of the sheer time and cost of such\r\nmeasures.30 Furthermore, RLHF is tricky to implement even with enough feedback. A\r\npoorly designed feedback process may result in the model learning how to act in ways\r\nthat maximize the amount of positive feedback it receives but that may not actually\r\ntranslate into the kinds of outputs that human users prefer.31\r\nConstitutional AI is a related fine-tuning process, developed by the AI company\r\nAnthropic, that attempts to steer an LLM’s behavior with minimal human guidance.32\r\nUnlike RLHF, Constitutional AI does not rely on human labels or annotations as a way\r\nto encode human preferences. Instead, researchers provide the system with a list of\r\nguiding rules or principles—hence the term “constitutional”—and essentially ask\r\nanother model to evaluate and revise its outputs. 33 While Constitutional AI is\r\npromising as an RLHF alternative that relies on far fewer human-generated labels,\r\nRLHF still seems to be the industry standard for guiding and steering LLMs at the finetuning\r\nstage.34\r\n4. Prompt and Output Controls\r\nEven after pre-training and multiple rounds of fine-tuning, an LLM may still output\r\nundesirable text. Before developers incorporate models into consumer-facing\r\nproducts, they can choose to control models using additional techniques at either the\r\npre-output or the post-output stage. These techniques are also commonly referred to\r\nas “input filters” (applied at the pre-output stage) and “output filters” (applied at the\r\npost-output stage) and generally fall into three camps: detection, flagging, and\r\nredaction. Many existing tools and solutions originate from the need to moderate\r\ncontent on social media and are not necessarily specific to AI, but developers have\r\nincreasingly adapted them for use on large language models.35\r\nBefore the LLM even receives a user’s input, developers can screen prompts to assess\r\nwhether they are likely to evoke harmful text and show users a warning or refusal\r\nmessage in lieu of completion from the AI system. This can create a similar effect to\r\nthe model itself refusing to answer certain types of prompts. While both of these\r\nmethods can be bypassed by jailbreaking, in which users deliberately circumvent\r\nmodels’ content restrictions,36 these methods can serve as a basic defense for nonmalicious\r\nusers. AI developers are also increasingly evaluating their LLMs using “redteaming,”\r\na systematic testing process that includes jailbreaking models in a controlled\r\nenvironment in order to see how their guardrails might fail.37 While red-teaming is\r\nused for a number of purposes, including acquiring information that can be used to\r\nimprove controls, one major incentive for developers is to detect and fix potential\r\nCenter for Security and Emerging Technology | 10\r\njailbreaks before the model is released to the public. Another variation on prompt\r\nscreening is prompt rewriting, in which a different language model rewrites usersubmitted\r\nprompts before they are passed to the target model.38 This may provide\r\nsome protection against jailbreaking, as it effectively creates another set of guardrails\r\nbetween the user and the target model.\r\nAt the post-output stage, once the LLM has composed a response to a prompt but\r\nbefore that output has been shown to the user, developers can employ additional\r\nchecks and filters. One option is to train a separate machine learning model—often\r\nreferred to as a “toxicity filter”39—to detect harmful content, then use that model to\r\ncatch outputs before they can be shown to users. Like supervised fine-tuning, these\r\ntechniques rely on human-labeled data. While they have demonstrably positive effects\r\non how toxic LLM outputs are, labeling the datasets of harmful content that are used\r\nto train the detection models is often actively harmful to workers’ mental health.40\r\nPost-fine-tuning model controls are also often combined with monitoring or user\r\nreporting. Usually, this involves a combination of automated content detection or\r\nfiltering, human content moderation, and user reporting. For example, OpenAI provides\r\na moderation endpoint that developers can use to automatically flag or filter\r\npotentially harmful outputs,41 and was also initially relying on human content\r\nmoderators to help evaluate outputs from ChatGPT.42 Finally, if a harmful or\r\nundesirable output makes it through all of the existing controls, many LLM interfaces\r\ncontain a user feedback mechanism so that users can flag individual outputs directly.\r\nDevelopers are extremely unlikely to catch every prompt or use case that might lead to\r\na harmful output, and thus rely on users to give feedback on model performance.\r\nOpen vs. Private Models\r\nThe safeguards listed in the previous section are mostly voluntary techniques that are\r\napplied by the companies that develop and host top-tier LLMs. These large companies\r\ngenerally have both the incentives and the resources to attempt to secure their models\r\nand improve the safety of their outputs. For example, minimizing the chances that a\r\nlanguage model will return toxic text may improve the user experience and boost\r\npublic confidence in the model, making it easier to incorporate into a software product.\r\nHowever, private companies are not the only ones building and supplying these\r\nmodels. Smaller, but still powerful, open models are increasingly available for anyone\r\nto download and adapt. Some publicly accessible models were originally trained by AI\r\nlabs on carefully selected pre-training data and may have been red-teamed prior to\r\nrelease, but these released models can be fine-tuned by third-party users who may be\r\nCenter for Security and Emerging Technology | 11\r\nless diligent or even outright malicious. Other open models may be trained by\r\ndevelopers who may have fewer resources dedicated to safety, less interest in curating\r\ntheir fine-tuning data, or fewer incentives to monitor the prompts that are provided to\r\ntheir models or the outputs that are ultimately created. Recently, some powerful open\r\nmodels have also been produced in foreign countries, such as the United Arab\r\nEmirates and China, where developers may have different views about what guardrails\r\nshould exist.43 While many of these open models are created with safety and\r\nresponsible use in mind, there is no way to guarantee that all of their downstream\r\nusers adhere to the same standards and that the resulting applications will be errorfree\r\nor sufficiently safeguarded. In fact, some research suggests that fine-tuning a\r\nmodel can undermine its developers’ safeguards even when users may not intend to\r\ndo so.44\r\nThe AI development community is currently debating whether private or open models\r\nare better for safety. For one, private models are not guaranteed to be easier to control\r\nin all circumstances. Even if they are secured and safeguarded, cutting-edge models\r\nare more likely to possess capabilities that require novel or more rigorous control\r\ntechniques. Other variables, such as whether or not the user is interfacing directly with\r\nthe model, may also affect how easy it is to control. Finally, while open models are\r\ndifficult to control and monitor once they are adopted by downstream users, they also\r\nbroaden access to researchers outside private companies who may have fewer\r\nresources or need the flexibility to experiment freely with an LLM.\r\nConclusion\r\nControlling LLM outputs remains challenging. In practice, the methods above are\r\nalmost always used in combination with each other, and undesirable outputs will\r\ncontinue to slip through despite developers’ best efforts. Today’s methods are more\r\nlike sledgehammers than scalpels; any attempt to control a model in a specific way,\r\nsuch as preventing it from outputting violent content, is likely to have unintended\r\nconsequences, like making it unable to describe the plot of an R-rated movie.45 There\r\nare also legitimate disagreements about whether or not a given output is harmful, and\r\nthe definition of what constitutes harmful or toxic content might vary depending on the\r\ncontext in which any given model is deployed.\r\nSeveral other factors complicate the situation further. Firstly, this is a pacing problem.\r\nAI researchers are racing to develop and test these techniques while simultaneously\r\nkeeping up with the breakneck pace of AI capabilities progress. The popularity of\r\njailbreaks and other methods for bypassing content controls also means that\r\nCenter for Security and Emerging Technology | 12\r\ndevelopers are constantly discovering new ways their models can be manipulated.\r\nFinally, it is very difficult for those outside the leading AI labs to evaluate how effective\r\nthese individual methods are because there is little information about their\r\neffectiveness for some of the most popular and powerful LLMs. While open models\r\ncan provide useful data in this vein, they may be smaller and less capable than stateof-\r\nthe-art models. Public data on user behavior, such as API calls or what kinds of\r\nfeedback users are giving models, is also scarce.\r\nLanguage models can carry inherent risks, including their propensity to output\r\nundesirable text, including falsehoods, potentially dangerous information such as\r\ninstructions for biological or nuclear weapons, or malware code. Nevertheless, the idea\r\nthat developers can gain perfect control over an LLM by simply tweaking its inputs is\r\nmisleading. LLMs can be complex, messy, and behave in unpredictable ways. As AI\r\ngovernance and regulation become increasingly important, however, understanding\r\nhow they work and how they might be controlled will be more critical than ever.", "metadata": {"original_filename": "item094_US_Controlling Large Language Model Outputs A Primer.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:51:54.477105", "updated_at": "2025-08-28T21:51:54.477105", "word_count": 27286}