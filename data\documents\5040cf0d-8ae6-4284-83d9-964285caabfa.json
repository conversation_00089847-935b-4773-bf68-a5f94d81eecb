{"doc_id": "5040cf0d-8ae6-4284-83d9-964285caabfa", "title": "item017_US_Generative Artificial Intelligence  and  Open Data Guidelines and Best Practices", "text": "Part II: Guidelines and Best Practices The following sections highlight relevant Guidelines and Best Practices for achieving generative AIreadiness. These are intended to be starting points for Commerce to adopt and implement, and therefore are not exhaustive.  1.0 Documentation  Documentation refers to the process of recording, describing, and contextualizing data to make it understandable and usable. Documentation not only enhances the transparency and reproducibility of data but also provides the necessary contextual understanding needed to effectively interpret and derive meaningful patterns from data. Thus, it is recommended that data publishers within Commerce both communicate contextual information through public documentation affiliated with their open data assets to support users and developers of generative AI systems and maximize the availability and accessibility of documentation.  Guideline 1.1 Provide comprehensive context about data assets in documentation. Comprehensive documentation of datasets is essential for gaining deeper insights into Commerce data and particularly important for training models and data retrieval. Providing robust documentation helps ensure that AI models can be trained with a clear understanding of the data’s structure, sources, limitations, and intended use cases. This leads to more accurate, reliable, and 22 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices unbiased AI outcomes, which are crucial for maintaining trust and credibility in AI-powered applications. Best Practices  1.1.1 Provide helpful characteristics of open data within documentation. Well-structured and relevant documentation is essential for supporting both data users and AI systems in their efforts to generate insights and derive meaningful patterns from Commerce data. To maximize the utility of open data, comprehensive documentation should include key characteristics that offer a complete understanding of the dataset. Essential elements to highlight include the dataset’s intended use, known limitations, known biases, a detailed data dictionary, and its lineage and provenance—documenting the dataset's origin and any changes in custody up to its current state24. Additionally, thorough recordkeeping about data sources, rights (such as copyright ownership and status), and licensing terms and conditions should be provided. It is equally important to include information about any unknowns or missing data within the documentation, as this transparency allows users to accurately assess the dataset’s completeness and reliability. By meticulously covering these aspects, open data publishers can significantly enhance the overall utility, trustworthiness, and usability of their data. For generative AI development, this level of detail in documentation enriches the model's knowledge base during training and fine-tuning, supporting robust performance and accurate outputs. Additionally, it facilitates efficient data retrieval as it ensures that AI systems can access and leverage the right data effectively. 1.1.2 Implement persistent identifiers. 24 National Library of Medicine definition of data provenance. 23 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices A persistent identifier (PID) is a long-lasting reference to a document, file, or other digital object. Implementing PIDs can provide stable and permanent references to specific datasets, versions, and related documentation. As such, PIDs allow users to reliably track and access the same dataset or document over time, even as updates are made. PIDs support consistency across systems, enhance metadata, and facilitate precise feedback from users. PIDs are crucial for generative AI-ready data as they ensure stable and reliable access to specific datasets or versions over time, even as updates occur. PIDs support consistencies across the development process, allowing models to access the exact version of data required and enhance data provenance and reproducibility. They ensure that the origins, processing steps, and modifications of data are well-documented and traceable. PIDs are vital for verifying AI models' outcomes and maintaining accountability across systems. 1.1.3 Update documentation with each data release and use version control. In order to maintain its relevance and accuracy, documentation should be updated with each data release and version controlled to match the data release so that data users understand how a dataset has changed over time. Although specific implementations vary, version control is a system to track changes to data over time25, ideally with a changelog indicating what changes were made. Published documentation should capture any alterations to variable names, statistical methodologies, and dataset structures that could materially affect the interpretation of the data. 1.1.4 Provide version controlled open-source code for data processing. Providing open-source code for data processing is an important element of reproducible and responsible open data, particularly code written by the federal government to 25 Git is a free and open-source distributed version control system. This Git article provides descriptions and examples of version control. 24 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices transform data before it is published. While natural language descriptions written in documentation can be useful in communicating how and when such decisions were made when the data was generated, code often provides a more exact representation of these processes. Further, open-source software for data processing can demonstrate to users and automated systems how to correctly parse and interact with specific data resources. Published code should be version controlled and maintained in line with data processing pipeline updates. This practice enables transparency for both users and models relying on Commerce data and also enables revisiting prior versions of documentation as needed.  Guideline 1.2 Maximize the availability and accessibility of documentation Documentation about Commerce data should be easily accessible to all who seek to use it for each stage of the generative AI process. Both humans and AI systems should experience few or no technical barriers when attempting to access documentation. Best Practices  1.2.1 Provide documentation in human and machine-readable formats. Providing data documentation in both human and machine-readable formats26 is a necessary step in ensuring Commerce open data assets are transparent, interpretable, reliable, and accessible, especially in the context of training and building generative AI (and other AI/ML) applications. Human-readable27 documentation ensures that data users (such as researchers, analysts, or developers) can easily understand important elements of the data, supporting informed decision-making and appropriate data usage. Machinereadable documentation is necessary for automating data processing workflows, allowing 26 The OPEN Government Data Act requires open government data assets made available by federal agencies to be published as machine-readable data. Figure A1.1. 27 The Open Data Handbook describes human-readable data as being presented in a format that can be conveniently read by a human. Figure A1.1.  25 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices AI systems the ability to effectively parse, interpret, and utilize the documentation with limited human intervention. Descriptive summaries in both formats will best suit data users and generative models looking to contextualize a dataset as a whole, while metadata (discussed in Section 2.0 Data and Metadata Formats) assists in communicating further detail such as the data’s structure, organization, and individual elements. This dual-format approach allows data to be leveraged to its fullest potential when developing generative AI applications. Regardless of format, documentation should be available in a consistent location to aid in easy retrieval. 1.2.2 Use open-source software and formats, where appropriate. Particularly when publishing code for data publishing, consider transitioning to opensource software, such as R or Python, versus proprietary software that is less accessible for the average user. Embracing open-source solutions throughout Commerce’s data publishing process can enhance public trust and encourage the use of Commerce’s open data. Open-source software promotes accessibility, transparency, and interoperability, which are essential for generative AI systems to effectively ingest and use Commerce open data. Generative AI tools benefit from open-source software like Python or R because these platforms offer flexibility in handling a variety of data formats and provide robust libraries for preprocessing, cleaning, and transforming data into AI-readable formats. Additionally, using open-source formats avoids the barriers posed by proprietary software, making public data more easily accessible for a wide range of users and AI applications. This enhances collaboration, trust, and innovation in AI model development. 26 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices 2.0 Data and Metadata Formats Commerce must continuously evolve its data and metadata standards to align with emerging technologies such as generative AI. Metadata refers to structured information about an information resource that aids in retrieving, using, or managing that resource.28 In other words, metadata is information that helps make data usable; it enables humans and machines to understand one or more aspects of the data, such as its source, type, owner, and relationship to other datasets. Though Commerce disseminates various types and categories of data, from raw data, such as NOAA’s sensor network readings, to derived data, such as statistical products from the U.S. Census Bureau, there are common metadata properties that are essential for the generative AI development process. As Commerce data grows in size and breadth, metadata is therefore becoming increasingly important, especially as it enables automated systems to use data reliably and accurately. One important distinction applicable to all Commerce data products is the distinction between document-level metadata and content-level metadata. Document-level metadata provides high-level information about a dataset or document, such as its title, author, publication date, and overall subject matter. It is essential for enabling discovery and identification of datasets but does not provide details necessary for their specific use or application. Content-level metadata, on the other hand, describes the content itself and is necessary for understanding or using the dataset. Content-level metadata enables automated tools or systems to interpret and process the structure, meaning, and relationships within the data or document, rather than merely parsing or extracting surface-level information. Content-level metadata can be categorized as follows: 1. Formatting metadata specifies how the data are divided into records and components, allowing them to be processed mechanically.  2. Dataset-level metadata describe properties of the dataset (or table), including source, provenance, licensing, etc. 28 Understanding Metadata is a revision and expansion of Metadata Made Simpler: a guide for libraries published by NISO Press in 2001. 27 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices 3. Variable-level metadata provides labels for the fields or columns of the dataset, together with information about type, precision, accuracy, or language (for textual variables). Variable-level metadata can also include: a. Identity and constraint metadata, information about the uniqueness of variable values and how variable identifiers link across tables in the dataset or into external controlled vocabularies. b. Functional metadata, a broad category of descriptions which support or enable complex processing by AI systems or other applications which transform and combine data for users. Providing comprehensive metadata at all levels can support data’s machine understandability versus just machine readability. Although data across Commerce can vary greatly,29 Commerce's data producers should attach generative AI-relevant, comprehensive, structured metadata to its open data and maximize the availability and accessibility of its data and metadata. 29 It is recognized that relevant metadata may vary considerably depending on the type of data source. For example, metadata that may be helpful to include for NOAA’s raw data related to sensor network readings could include information like sensor specifications, device calibration details, geographic and temporal information for each measurement, and other survey details. On the other hand, helpful metadata for derived datasets such as Census data could include information on the levels of aggregation applied, definitions and details on statistical methodologies used in creating the dataset, confidentiality techniques applied, and uncertainty measures. Geospatial metadata could include beneficial elements such as the spatial/geographic bounding, or the area the dataset represents, and coordinate systems or projections utilized for the dataset. Imagery metadata should include essential elements such as image format, resolution, color space, and capture details (e.g., timestamp, geolocation). Video metadata should include crucial elements such as format, codec, frame rate, resolution, and temporal information. 28 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices Guideline 2.1 Publish comprehensive and structured data and metadata Comprehensive metadata provides critical context, such as data provenance, variable definitions, and descriptions of any transformations applied, which are key for accurate data processing and model training. As models are fine-tuned, detailed metadata supports a model’s ability to generalize across different datasets or tasks, improving the robustness and adaptability of the model. In deployed generative AI applications, metadata plays a crucial role, guiding how models process new data and generate outputs, ensuring consistency, and minimizing the risk of misinterpretation. Metadata should provide as much context as possible and be co-located so that information is not missed during any stage in the development and analysis process. These metadata may be integrated into the data source itself or included as sidecar files; the approach depends on an individual data source’s format and size.  Structuring data and metadata is essential for ensuring generative AI systems can effectively access, process, and interpret information. Structure allows these systems to interact with the information seamlessly, removing obstacles that could limit their performance and efficiency. Consistent structure also facilitates interoperability between AI systems, allowing models to work 29 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices with data from diverse sources without compatibility issues. This broadens the range of datasets that generative AI can access, enabling better training and more reliable results from models.30,31 Best Practices 2.1.1 Include generative AI-relevant information in metadata at the dataset level.   Currently, the DCAT-US schema32 is the metadata standard for structured data within the U.S. government data ecosystem and is used throughout Commerce.33,34 DCAT-US was initially established to address requirements within the OPEN Government Data Act35 and the Office of Management and Budget (OMB) Memorandum M-13-1336 as part of the federal government’s Project Open Data (POD) initiative. It builds on the international Data Catalog Vocabulary (DCAT) specification, which was established and is maintained by the World Wide Web Consortium (W3C). DCAT makes public sector data more searchable across borders and sectors. It allows users to find, access, and use data more effectively. Other domain-specific metadata standards suggested by guiding agencies, such as the ISO geographic data and data quality standards, are also utilized for generating metadata to support datasets.  30Another important distinction is the difference between document-level metadata and content-level metadata. For structured data, content-level metadata describes individual variables, fields, or paths (for hierarchical formats such as JSON or XML). 31Practitioners should use domain-specific standards for dataset values, such as American National Standards Institute (ANSI) codes for ensuring uniform identification of geographic entities like states and places, and standards-based metadata schemas, like DCAT for data cataloging or International Organization for Standardization (ISO) standards, to ensure structured data and metadata. It is important for Commerce to utilize standards that have been suggested or endorsed by guiding federal agencies, such as the Office of Management and Budget and/or Federal Geographic Data Committee, for use in public products. Data producers and publishers within Commerce should also determine an internal working metadata schema to allow for interoperability and consistent, streamlined use of data creation and quality control tools, helping to minimize technical barriers. 32 The DCAT-US schema is the standardized metadata specification for describing all datasets and APIs within a government agency’s comprehensive data inventory. It was formerly known as the Project Open Data Metadata Schema. 33 Commerce Data Hub. 34 DCAT does not support content-level metadata itself but only references external data dictionaries. 35 Title II of the Evidence Act, also known as the Open, Public, Electronic, and Necessary (OPEN) Government Data Act. 36 The Office of Management and Budget (OMB) Memorandum M-13-13, issued on May 9, 2013, is titled Open Data Policy – Managing Information as an Asset. The memorandum establishes a framework for managing government information as a strategic asset to promote openness, interoperability, and safeguarding of data 30 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices As of 2024, the federal government is updating the DCAT specification and will be introducing the DCAT-US v3.0 schema to improve data cataloging, discovery, and interoperability for US government agencies.37 In addition, DCAT-US v3.0 aligns with the global W3C DCAT v3.0 standard,38 upholds FAIR (Findable, Accessible, Interoperable, and Reusability) data principles,39 and expands support for geospatial data. As of the publishing of this document, the official guidance for use of this schema is not yet available.  In the meantime, it is important to include key recommended fields or properties in metadata that provide additional context for AI/ML related data use and are of particular relevance to generative AI-related data retrieval. These recommended properties and their relevance are listed by category in Table 2 below. Additional metadata that is not captured in Table 2 may also be relevant. If the value for a given property does not exist, indicate that the value is unknown (e.g. report null for that value). It is more useful for AI developers and other data users if a metadata property is included as unknown rather than omitted entirely as doing so indicates a gap while preserving a uniform structure. Table 2 below indicates metadata properties to be included to facilitate generative AI development. Properties highlighted in green are both recommended by DCAT-US v3.040 and are recommended by this guidance. Properties in white were not formally recommended by DCAT-US v3.0 but are recommended by this guidance to further improve metadata for generative AI.  37 Project Overview page on the DCAT-US GitHub Wiki. DCAT-US (Data Catalog Vocabulary - United States) is a profile of the W3C DCAT standard, tailored to meet the specific needs of the U.S. federal government. The project aims to improve the Findability, Accessibility, Interoperability, and Reusability (FAIRness) of federal data by providing a standardized metadata schema for documenting datasets. 38 The “rights” related property in DCAT-US 3.0 doesn’t perfectly align with W3C, as W3C DCAT v3 is broader. 39 The FAIR Guiding Principles for scientific data management and stewardship describes the FAIR principles— Findability, Accessibility, Interoperability, and Reusability—which are designed to guide data producers and publishers in making data more usable and valuable 40 The DCAT-US 3.0 recommended properties. 31   32 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices  Property Description  from DCAT-US v3.0 documentation41  Context provided relevant to generative AIrelated data retrieval Access Restriction  An indication of whether there are access restrictions on the data Ensures responsible access to sensitive historical records. Enhances transparency, aiding researchers and authorized users in understanding and navigating access parameters for archived materials  Data Dictionary  Specifies a data dictionary or schema that defines fields (variables, dimensions, measures, attributes) in the dataset Important both for parsing dataset correctly and provides context to improve quality of patterns learned and model output Identifier Unique identifier for dataset Helps disambiguate different datasets; promotes transparency and data consistency Keyword Keywords describing the dataset Helpful to summarize larger focus of dataset; can help with finding/searching/crawling for new relevant data by developers and other users Licensing This property refers to the license under which the dataset is made available Helps ensure users and automated systems parse data licenses accurately Publisher  The entity responsible for making the dataset available Important for understanding and citing of data sources, linking other context to dataset   Rights  This property refers to a statement that specifies rights associated with the dataset Helps ensure users and automated systems parse data rights accurately Temporal Coverage The time period the dataset covers Important for training models with relevant temporal data and increases likelihood of models returning timely/relevant results to users Update/ Modification Date  The most recent date on which the dataset was changed or modified Helps model developers access/update relevant information   41 The DCAT-US 3.0 Profile (DCAT-US 3.0) is an updated specification designed to facilitate data cataloging, discovery, and interoperability among US government agencies.   33 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices  Documentation  A page or document containing more information about the dataset Helps model developers easily identify additional relevant context/metadata about Dataset Has Version Indicates a related dataset that is a version, edition, or adaptation of the current dataset  Helps identify raw versus derived datasets and disambiguate different data versions Language Natural language used for textual metadata of the dataset Helps ensure metadata are parsed correctly; helps users find language-specific resources to improve models for English and non-English models  Provenance A statement about the lineage of the dataset, including any changes in ownership and custody Helps model developers access/update relevant information. Written language especially helpful context for LLM model training  Version  The version name or identifier of the dataset Helps model developers access/update relevant information  Version Notes A description of the differences between this and previous versions of the dataset Helps model developers access/update relevant information  Table 2. Metadata properties to be included to facilitate generative AI development. If possible, it is also recommended to create a custom property, nextUpdateDate, which would allow data users to know when to expect updates to datasets used in the development of generative AI systems.  2.1.2. Add comprehensive variable-level metadata for machine understandability. Variable-level metadata, distinct from dataset-level metadata discussed in 2.1.1, provide granular descriptions and attributes for individual data variables within a dataset. This level of detail is essential for enabling machine understanding, ensuring accurate data processing, and supporting advanced AI functionalities such as imputation, aggregation, and data exploration. To enhance machine interpretability and usability, variable-level metadata Generative Artificial Intelligence and Open Data: Guidelines and Best Practices (particularly functional, which is especially relevant for processed data products such as those produced by statistical organizations) should include: 1. Application and Presentation Logic, such as microformats (e.g., names divided into \"First,\" \"Middle,\" and \"Last\") and rules for data visualization or exploration (e.g., recommending using \"Region\" as a pivot variable). This enhances the utility of datasets for both human users and AI systems. 2. Dependency Information, including the dependence of variables on one another or the hierarchy of aggregations (i.e. total count, single characteristic, combined characteristics, etc.). Dependency information is crucial for the automatic combination of variables and for realistic and consistent imputation of data values. 3. Distributional information, including margins of error, expected distributions, etc. This supports high-quality data analysis and modeling by enabling accurate statistical validation and interpretation of variable-level data. 4. Non-scalar and compound data, including: a. Ontological variables, with multiple levels of generality (e.g., \"Country > State > City\");  b. Flexible value schemas, reflecting adaptive design or complex variables. This could include geographical (e.g., \"City nested within County, nested within State\") or organizational hierarchies (e.g., \"Department within Division within Agency\"); and  c. Varieties of missingness or incompleteness (e.g., data not collected for a specific reason versus missing due to errors or external factors). Detailed variable-level metadata is critical for creating generative AI-ready data by enabling seamless integration, efficient processing, and accurate analysis within AI workflows. For generative AI systems, comprehensive variable-level metadata can enhance training and fine-tuning, providing structured, machine-readable information to guide AI models in understanding variable relationships, dependencies, and distributions. It can also support 34 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices real-time data retrieval, enabling AI systems to dynamically query and combine variables, ensuring responses are based on up-to-date and contextually accurate information. By embedding comprehensive variable-level metadata, Commerce can improve the interpretability and transparency of Commerce’s data assets and empower generative AI systems to produce increasingly reliable, context-rich outputs. 2.1.3 Publish metadata aligned with common, or accepted, metadata schemas and standards. In addition to leveraging DCAT, Commerce should publish their metadata using common web standards. Schema.org42 is a metadata standard used to describe structured data and is used by 50 million sites including Commerce sites like data.census.gov and nist.gov. In early 2024, MLCommons, an open AI engineering consortium that regularly collaborates with both academia and industry,43 officially released Croissant.44 Croissant is an extension to the widely used schema.org45 metadata standard, which is used by over 50 million sites to describe structured data, including Commerce sites like data.census.gov and nist.gov. Croissant extends the schema.org dataset vocabulary and describes dataset attributes, resources they contain, and their structure and semantics using JSON-LD to better streamline their usage for AI/ML model training.  42 Schema.org is a collaborative, community activity with a mission to create, maintain, and promote schemas for structured data on the Internet, on web pages, in email messages, and beyond. 43 MLCommons is an Artificial Intelligence engineering consortium, built on a philosophy of open collaboration to improve AI systems. 44 Croissant is a metadata format for datasets that simplifies how data is used by ML tools and frameworks. 45 Schema.org is a collaborative, community activity with a mission to create, maintain, and promote schemas for structured data on the Internet, on web pages, in email messages, and beyond. 35 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices Croissant is already in use for 400,000 datasets across three major dataset publishers for AI: Hugging Face,46 Kaggle,47 and Open ML.48 Croissant does not require data publishers to change the representation of the underlying data – rather, its operationalized documentation enables datasets to be loaded into ML platforms in a few lines of code without reformatting. Croissant is compatible with widely used frameworks for AI development like PyTorch,49 TensorFlow,50 and JAX.51 It can also be used with Croissant Editor to create, validate, and modify Croissant datasets in a user-friendly interface.52,53 Croissant also allows the specification of both document-level and content-level metadata. Croissant does not directly align with DCAT-US v3.0. However, AI/ML development and use continues to proliferate, increasing the importance of preparing open data for generative AI model training. Some entities within Commerce already publish metadata in multiple forms54 and have begun work to improve the AI-readiness of their metadata, such as NOAA.55 Publishing open datasets in formats ready for model training allows for their utility 46 Hugging Face is an AI community and platform that focuses on making machine learning more accessible and collaborative. It provides a wide range of resources, including pre-trained models, datasets, and tools for natural language processing (NLP) and other AI tasks. The platform is known for its Transformers library, which offers state-ofthe-art models for various machine learning tasks. 47 Kaggle is a platform for data scientists and machine learning practitioners. It hosts data science competitions, provides access to a vast array of datasets, and offers tools for collaboration and analysis. 48 OpenML is an open platform for sharing datasets, algorithms, and experiments. 49 PyTorch is an open-source machine learning library developed by Facebook's AI Research Lab (FAIR). It's widely used for building deep learning models and conducting research in fields like computer vision, natural language processing, and reinforcement learning. 50 TensorFlow is an open-source software library for machine learning and artificial intelligence, developed by Google Brain. It's designed to facilitate the development and deployment of machine learning models across a variety of tasks, with a particular focus on deep learning. 51 JAX is a Python library developed by Google for high-performance numerical computing and machine learning research. It extends the capabilities of NumPy with features like automatic differentiation, just-in-time (JIT) compilation, and support for GPU and TPU acceleration. 52 Access Croissant Editor through Hugging Face. 53 Access Croissant Editor through MLCommons. 54 U.S. Census Bureau's American Community Survey (ACS) 1-Year Estimates Subject Table S0101.This table provides detailed data on age and sex for various geographic areas, including the nation, states, counties, and more. 55 ESIP Data Readiness Cluster is the repository for the Data Readiness Cluster to maintain the AI-ready data checklist. The cluster is a community-driven group focusing on developing recommendations and community standards on AIready open environmental data.  36 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices within generative AI systems. Therefore, Commerce data that are used for AI/ML training should consider using Croissant or a Croissant-like vocabulary. 2.1.4 Use standard missing data values within data and metadata.  Commerce should be mindful to avoid using non-standard missing data values in both their data and metadata. Although specific standards vary somewhat by technology, consistency in indicating missing values is an important way to avoid misinterpretation of datasets for all users and applications. For AI-related data retrieval in particular, datasets are often accessed at scale by automated systems. These tools may inaccurately parse data entries and/or data types if missing data are not codified as expected. Therefore, for each given data and/or metadata format, Commerce should ensure that missing values are indicated using the standard approach for that format and that accompanying documentation explain the nomenclature used. 2.1.5 Ensure consistent and unambiguous file naming conventions. Standardized file naming helps users and automated systems easily locate, identify, and understand data files, thereby improving accessibility and usability. For generative AI, clear file naming conventions enhance the ability of models to organize and parse data efficiently, which is essential for training and generating accurate results. Adhering to consistent file naming conventions supports data discoverability, facilitates machine learning workflows, and ensures that files are properly grouped and contextualized. Guideline 2.2 Maximize the availability and accessibility of data and metadata Maximizing data and metadata availability involves publishing datasets in accessible repositories and using widely accepted, machine-readable formats to support a broad range of applications. Accessibility extends beyond just availability, requiring metadata that describes the content, context, and structure of data clearly. Effective metadata enables users, including generative AI 37 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices systems, to interpret data accurately and maximize the data's potential impact and usability across diverse sectors. Best Practices  2.2.1 Produce data and metadata in machine-readable formats. Minimally, data should be both human and machine-readable, as encouraged by the OPEN Government Data Act.56 Providing structured metadata that uses meaningful terminology supports this mandate by supplying the necessary context needed for accurate data interpretation.  2.2.2 Data should be available in common open data formats. At a minimum, data should be made available in non-proprietary and widely used data formats that are defined by openly available standards. For example, tabular data should be available in formats such as CSV57 or JSON.58 These data formats allow for the dissemination of data without requiring or privileging the use of specific software. Additionally, the JSONLD59 extension enables data publishers to provide additional context by including links to data catalogs and files. A dataset that presents data at several different geographical levels could include a link to another metadata file containing definitions of each geographic level. Graph-based metadata can also make use of either the RDF/JSON standard or the Croissant standard mentioned previously. These approaches ensure that data users and AI systems 56 Title II of the Evidence Act, also known as the Open, Public, Electronic, and Necessary (OPEN) Government Data Act, requires federal agencies to publish their data in machine-readable formats. 57 Format description page on the Library of Congress website which details the CSV (Comma-Separated Values) format as described in RFC 4180, a Request for Comments document. 58 RFC 8259, the official Request for Comment document which defines the JavaScript Object Notation (JSON) Data Interchange Format. JSON is a lightweight, text-based, language-independent data interchange format that is easy for both humans and machines to read and write. 59 JSON is a useful data serialization and messaging format. This specification defines JSON-LD 1.1, a JSON-based format to serialize Linked Data. The syntax is designed to easily integrate into deployed systems that already use JSON, and provides a smooth upgrade path from JSON to JSON-LD. It is primarily intended to be a way to use Linked Data in Web-based programming environments, to build interoperable Web services, and to store Linked Data in JSON-based storage engines 38 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices traversing this data can find the definitions needed to interpret the data contextually rather than being forced to consult external sources. Additionally, Commerce should consider:  ● Geospatial Data: Geospatial data should be shared in open formats such as shapefiles or GeoPackages to allow for data interoperability across Geographic Information System software products. For large geospatial data stored in the cloud, standard formats like Cloud-Optimized GeoTIFF (COG) should be considered.60 Geospatial information should never be provided as a simple text block as these can be difficult for machines to disambiguate. At the least, they should contain some common form of coding such as FIPS coding and the dictionary should be linked to or provided as part of the dataset. ● Image and Video Formats: It is important to use standardized, widely supported, open-source image and video formats for data publication. Avoid using proprietary or obsolete formats that may limit accessibility and interoperability. This ensures that data consumers and systems can easily access and use visual data without needing specific, proprietary software. ● Avoiding PDF Files for Data and Metadata: PDF files61 are problematic for data and metadata understanding by both users and automated systems. These files, which frequently contain text, images, and other elements, were primarily designed for presentation as opposed to data extraction. Consequently, they have a number of features, including inconsistent formatting, complex layouts, and embedded content, that are challenging to automatically parse and interpret. This unstructured content limits the ability of machines to effectively index such files, which affects data 60 The Open Geospatial Consortium (OGC) announced that the Cloud Optimized GeoTIFF (COG) Standard v1.0 has been approved by the OGC Membership for adoption as an official OGC Standard. COG, as an OGC Standard, formalizes existing practices already implemented by the community, such as the GDAL library or the COG explorer and other implementations. 61 Format description page on the Library of Congress website regarding PDF (Portable Document Format) Family. 39 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices discoverability. Further, extracting data from PDFs using optical character recognition (OCR) tools can also introduce errors. Therefore, these files should not be used for Commerce open data, metadata, or documentation.  ● Avoiding exclusively storing data in formats that privilege proprietary software: Avoid exclusively storing data in formats that privilege specific applications. Although opensource editors and software frequently exist for reading these formats, these editors may not work as consistently as the format’s corresponding proprietary software. Therefore, it is recommended to not publish data exclusively in these formats. Specifically, publishing data in XLSX files can be problematic for the purposes of developing generative AI (and other AI/ML) systems for two reasons. First, autocorrect errors throughout the data publishing process can change data content in unexpected ways, which has been found to be a significant issue in fields such as scientific research. Second, XLSX files have size limitations, leading to inconsistencies and/or challenges for the consistent representation of large datasets. Therefore, Commerce should avoid exclusive use of these formats for open data.   2.2.3 Use file structures that reduce structural ambiguity. For tabular data, flat tables, in which the data are presented in a non-nested format, reduces structural ambiguity and are simple for AI systems to parse. Flat files have the additional benefit of making it easier to update automated processes when there are changes to the underlying data structure in subsequent data releases. In contrast, hierarchical tables, in which data are presented in a nested format, often for presentation purposes, introduces complexity in the interpretation and use of tabular data (see Figure 2).  40 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices Figure 2: Examples of flat vs hierarchical data structures 2.2.4 When possible, both raw and derived data versions should be made available. Raw data are typically collected directly from instruments such as sensors, surveys, censuses, and other devices. It has usually undergone minimal processing. In contrast, derived datasets are the result of analyses, aggregations, and other calculations on raw data. Even when these types of data have the same origin, raw and derived data can vary in their level of granularity, frequency of updates, the degree to which they contain individuallevel information, and data confidentiality protections. Due to the sensitive nature of some raw data (such as Personally Identifiable Information), it cannot always be made publicly available. However, when possible, including both raw and derived data forms (and indicating if there is a linkage between the raw data and products derived from the raw data) can enhance generative AI-readiness by providing comprehensive training material, improving transparency, and supporting diverse applications.  3.0 Data Storage and Dissemination  Data storage and dissemination refers to methods used to store and distribute Commerce open data for generative AI development. Commerce should improve the navigation and retrieval of its 41 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices open data in order to shape the accessibility and usability of its data across diverse application domains. To do so, Commerce will disseminate its data in consistent formats across Commerce and ensure that its data is easily retrievable. Keeping data in consistent formats minimizes the need for complex pipelines, making it easier for developers to retrieve data assets and integrate them into AI models.  Commerce seeks to have consistent and easily accessible storage and dissemination for the following use cases:  ● Stored downloadable Commerce open datasets that AI developers and data users can easily navigate to;  ● Easily sourceable data that users can reference via hyperlink within a generated response, allowing users to navigate to Commerce data discovery tools such as the 2020 U.S. Census data found on data.census.gov; and ● Retrieval of specific data values for a generated response Whether users are navigating to Commerce data resources via generative AI applications or through other tools, it is important that the data they seek serves Commerce’s diverse users and use cases, from “no-code” users, to data science experts, to web crawlers, to automated data retrieval systems. Offering consistent formats and consistent access, whether through APIs or simple downloads, ensures that all users, including AI developers, can effectively engage with the data. 42 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices Guideline 3.1 Disseminate open data in consistent formats Disseminating open data in consistent formats ensures that datasets are easy to retrieve and integrate, supporting seamless access and usability for generative AI systems. Consistency in data formats reduces complexity, enabling users and generative AI systems to efficiently process and leverage the data across different use cases and platforms. Best Practices  3.1.1 Large datasets should be compressed or easily downloadable.   Generative AI systems rely on access to large datasets to improve model performance through extensive training and fine-tuning. Download times can be a significant barrier to accessing large quantities of data, particularly for users with limited computational or network resources. Generative AI, which benefits from diverse and voluminous data, requires swift and seamless access to such resources to optimize model accuracy and efficiency. By compressing or partitioning datasets, Commerce can lower these access barriers, enabling a broader community of researchers, developers, and organizations to leverage the data for AI innovation. 3.1.2 Compress large data files using open-source and language agnostic file formats.  File compression improves data access by significantly reducing file sizes, which can speed up download and API request times, and minimize storage requirements. This improves data accessibility, making it easier for users to quickly retrieve files without needing significant compute or storage resources. There are many approaches to file compression, but ZIP and Apache Parquet (Parquet) are emphasized because ZIP is highly accessible to implement and Parquet is particularly helpful for AI-related work.  ZIP is a widely used cross-platform format supporting lossless compression that allows for compression of multiple files at once into an archive.62 Archives include a directory that can 62 Format description page on the Library of Congress website regarding ZIP File Format (PKWARE). 43 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices load contained files without opening them, which enables users to identify and only extract relevant data without accessing the entire ZIP archive. Many software tools support compressing files into ZIP format, making it one of the most accessible compression options available. Disseminating ZIP files can be particularly helpful when users are downloading them from a Commerce data source and uploading them within a generative AI model for data analysis. Parquet, like ZIP, is an open source and language agnostic file format that enables efficient data compression and encoding.63 Notably, Parquet stores any form of data (including tabular data, image, documents, or other complex data) in a columnar format. Columnar format sometimes separates columns into distinct files, allowing for more efficient network access, and also enables more efficient querying and aggregation than row-based files like CSV, which can be especially useful for the large data files that are often used in AI model training. A nuance to consider is that there can be a preference toward standards that a community of users could prefer that differ from ZIP and Parquet, which could impact usage patterns if not prioritized. This judgement should be on the part of each bureau, office and operating unit to determine the best solution, while prioritizing the practice of distributing opensource formats.  3.1.3 Include long-form written documentation in data publications.   While standardized vocabularies like DCAT-US and Croissant enhance the value of metadata and facilitate easier parsing by automated systems, long-form written text is equally important and should not be overlooked in Commerce publications. Unlike PDFs, unstructured text doesn't require optical character recognition (OCR), which some PDFs may require in order to be machine-read effectively. Long-form written text is a particularly 63 Format description page on the Library of Congress website regarding Apache Parquet. 44 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices helpful context for training and fine-tuning generative AI models; so, publishing this form of metadata can help these models provide more accurate context and output for users interacting with Commerce data using automated tools. Additionally, long-form written documentation, such as unstructured text, is often more accessible for human users. Guideline 3.2 Store open data in easily retrievable locations To enable efficient data retrieval and use, open data must be stored in accessible and easily retrievable locations. By implementing the following best practices, data can be effectively integrated into AI workflows. Best Practices  3.2.1 Offer a range of modalities for retrieving data, minimally by RESTful API and direct download. RESTful APIs enable data scientists and developers to programmatically retrieve data. RESTful APIs provide a standardized, efficient, and scalable way for users to access and interact with data programmatically. They allow users to retrieve specific subsets of large datasets without downloading entire files, reducing bandwidth consumption and speeding up access. This is particularly useful for applications in generative AI, where models often require only portions of data or need to process it incrementally in real-time. RESTful APIs offer flexibility in integrating Commerce data into various AI workflows, making it easier to automate data retrieval, improving accessibility, and enabling more sophisticated data analysis across different platforms. Minimally ensure that API descriptions are machinereadable and that APIs have high data retrieval limits for accessing large datasets. For machine-readable API documentation, consider alignment with the Data Service class in DCAT-US v3.0 and/or OpenAPI, the latter of which is the current open standard for RESTful 45 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices API documentation.64 Further, setting permissive data access rules will prevent both humans and machines from being blocked when trying to fetch data. Note that for APIs, using graph representations for parameter and result types can make it easier for AI systems to construct valid custom requests. API access is particularly important when the data are updated frequently. Generative AI models are trained on static snapshots of data and text. To ensure the AI system can provide dynamic answers, the models can be trained to query APIs to find the most recent information. For example, the way an LLM would be able to answer a question about today’s weather forecast in Seattle would be to retrieve the latest information from an API. As described above, these live data access points should be RESTful APIs that are described using open standards like OpenAPI. Direct download files, existing in locations that are easily parsable and predictable, allow users to retrieve entire datasets in one operation without needing additional programming or API knowledge. This method is especially useful for users who want to store or process large datasets locally, as it provides a straightforward way to obtain the data in bulk. In generative AI, where models sometimes require vast amounts of data for offline training or analysis, direct download ensures users can access complete datasets without the need for complex configurations. Additionally, for users with intermittent or limited internet access, being able to download a dataset in one go, store it, and work offline is a significant advantage. However, provisioning data via direct download alone is insufficient to provide the near real-time data retrieval necessary to meet the needs of users requiring up to date information.  64 OpenAPI Documentation. OpenAPI is the most broadly adopted industry standard for describing new APIs. This site provides comprehensive guides and tutorials on how to use the OpenAPI Specification (OAS). 46 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices Both methods are valuable when preparing open data for AI systems, as developers working on generative AI and other AI applications often employ a variety of strategies for collecting training data.65,66 Regardless of the method used to retrieve Commerce data, the structure and content of the data should be identical (including metadata and data documentation).  3.2.2 Data websites should be regularly updated and easily crawlable. Data websites should be routinely updated and optimized for crawlability to support search engines, web crawlers, and other automated tools in discovering and indexing content. Ensuring that these sites are easily crawlable enhances the accessibility of Commerce's data resources for web searches and facilitates the aggregation of AI training data. To achieve effective crawlability, data portals should include: ● Well-structured sitemaps: Provide a clear map for web crawlers to follow, ensuring comprehensive coverage of the website's content. ● Consistent URL naming: Use uniform and descriptive resource locators for better organization and searchability. ● Proper security certifications: Ensure that security certificates are up-to-date to maintain trust and web functionality. ● A permissive robots.txt file: Maintain a robots.txt file that allows access to essential URLs while protecting sensitive data. More information on managing robots.txt files are detailed in 4.0 Data Licensing and Usage (4.1.2). ● HTML format for publications: Format working and research papers in HTML, as opposed to PDFs, enhancing machine readability. 65 An Overview of the Gemini App is a living document which is a comprehensive introduction to the Gemini app, an advanced AI assistant built on Google's research in LLMs. The document addresses the app’s capabilities as well as it’s limitations. 66 How ChatGPT and our language models are developed provides an overview of the publicly available information ChatGPT uses to help develop their models. The article explains how they collect and use the information. 47 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices ● Implement APIs that adhere to REST principles, enabling efficient, standardized, and scalable access to data resources for both developers and web crawlers. These practices not only improve the visibility of Commerce’s data but also contribute to broader efforts to make data resources readily available and usable.  4.0 Data Licensing and Usage Data Licensing and Usage explores different ways to clearly and consistently communicate the open data rights and permissions that Commerce grants to users for generative AI development. This guidance supports broad, equitable, and open access to its datasets and metadata, while also providing clear data ownership and usage rights and any restrictions on the reuse or redistribution of data. With today's LLMs, which are trained on extremely large corpuses of data, it can be difficult to ascertain usage conditions at scale. Some data are not clearly attributed to an initial author or rights holder; some data have been collected and synthesized into derived data sets; and some data are simply mis-labeled. Clarifying appropriate data usage is important for making Commerce data generative AIready, including signaling whether website crawling, AI model training, or use for data retrieval for AI systems are allowed. Commerce entities should consult with the General Law Division and the Office of the General Counsel and other relevant legal teams for questions regarding whether and how data may be licensed or used, as this can sometimes involve complex questions related to privacy, intellectual property, and national security. This current guidance focuses on how to incorporate good practices, standards, and usage considerations into datasets to signal that open data may be used for AI purposes.  This guidance presumes that the appropriate permissions and rights-related issues relevant to assessing and publishing Commerce data as open government data have already been made. However, certain practices, standards, and usage considerations can help further clarify data 48 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices accessibility, licensing, and use for data users and automated systems. To that end, information is provided here to assist Commerce in developing licensing and usage policies. This guidance considers usage policies at the dataset level and at the bureau and department level. Guideline 4.1 Publish comprehensible open data rights and permissions in accessible and accepted formats Publishing comprehensible open data rights and permissions in accessible, standardized formats is important for enabling responsible and efficient data use in AI. By adopting the following best practices, Commerce can enhance transparency and ensure shared data supports responsible AI development. Best Practices  4.1.1 Explicitly define and publish generative AI-related open data usage policies in a machine-readable format.  Although open data are intended to be freely accessible, this term can have various meanings and does not always imply freedom from all restrictions; as such, open data may not always be in the public domain. Commerce bureaus, offices, and operating units should clearly state their usage policies to clarify any applicable restrictions or licensing terms beyond copyright, such as those related to patents, trade secrets, or 49 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices privacy. To ensure consistency and avoid conflicting policies, Commerce should coordinate across the department to develop standardized templates for intellectual property statements and licensing, as current Commerce intellectual property rights (IPR) templates are not yet designed for this purpose. These templates should include specific policies related to AI, such as guidelines for AI model training, software development, and source identification in model outputs (i.e. LLM responses to prompts). Commerce policies should address whether models or other derivatives created from Commerce data must be openly licensed or if closed licensing is permissible. Any data available for AI model training should be explicitly labeled as such, and all policies should be published in a machine-readable format to facilitate accurate parsing and adherence by automated systems accessing Commerce data. 4.1.2 Include a robots.txt file at the root of Commerce websites.   Robots.txt files specify which URLs web crawlers can and cannot access, serving an important role in managing web crawler behavior (see Figure 3). Robots.txt files are typically found at the root of a website. For example, the robots.txt file for the website www.example.gov would be found at www.example.gov/robots.txt. If a robots.txt file is not available, the entire website is open for crawling.67 Therefore, Commerce websites should include them to clarify which URLs the department does and does not want used for AI model training.  While a robots.txt file may specify that a URL should not be accessed by crawlers, this specification does not prevent that URL from being indexed or accessed through search engines.68 Additionally, when attempting to do data retrieval, robots.txt files do not directly aid automated systems in finding the correct APIs or data sources. To control API access and data retrieval, other mechanisms such as API keys, access controls, and documentation 67 Search.gov page which provides information on how to create and use robots.txt files to manage how search engine bots crawl and index your website. 68 Google Developers Introduction to robots.txt provides a comprehensive guide on how to use robots.txt files to control how search engine crawlers access and index content on your website. 50 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices should be used, as described in 3.0 Storage and Dissemination and 5.0 Data Quality and Integrity. Figure 3: A portion of the robots.txt file for commerce.gov found at https://www.commerce.gov/robots.txt 4.1.3 Include comprehensive rights related metadata for responsible and trustworthy AI. Link any existing data licenses in the “License” property and rights-related information in the “Rights” or “Access”-related-property of a dataset’s metadata. Populating the “License,” “Rights,” and dcat:accessRights properties in each dataset’s metadata (as described in Table 2) helps all users easily access and follow existing policies. If there is no license, populating this metadata field as a null value is more helpful for users than omitting it from metadata entirely, as it reduces ambiguity about whether policies exist.  4.1.4 Distinguish between open data licenses (e.g. ODL) and copyright licenses (e.g. CCBY). Commerce entities should distinguish between the copyright license and the data license. A copyright license (e.g. a Creative Commons license) will not cover all the rights to the data itself. Therefore, as a best practice that aligns with the guiding principle to clarify usage policies, bureaus and offices should work with their General Law Division to provide the 51 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices appropriate data license (e.g. ODL) and, where appropriate, include a separate copyright license. Where possible, these data licenses should be in standardized, machine readable formats.  Avoid only releasing data using a copyright license as that may not include a clear statement of rights beyond copyright and can give a false impression about usage rights. The copyright license refers only to a specific bundle of intellectual property rights associated with the data and does not address other aspects of the data that may be needed to clearly convey rights to use or re-use data. Guideline 4.2 Develop and update data licenses and usage policies collaboratively To enhance the usability of open data for generative AI applications, while protecting Commerce’s data from unwanted use, it is suggested that Commerce develop and update its data licenses and usage policies through a collaborative approach across the department. This strategy encourages standardized licensing frameworks that promote interoperability and clarity across Commerce and for public data users, facilitating more effective data sharing and usage for AI systems. Best Practices  4.2.1 Develop and update data licenses and usage policies collaboratively throughout Commerce. Individual entities within Commerce should collaborate with the General Law Division and other relevant legal and policy teams to address specific licensing and usage considerations for their data resources. However, to ensure consistency and clarity for users accessing data from across Commerce, it is vital to foster inter-departmental collaboration. Siloed policy development can lead to inconsistencies and confusion, undermining the utility of shared data. Commerce entities are encouraged to communicate and collaborate 52 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices extensively to harmonize their licensing and usage policies. When updates are made to data licenses or usage policies, it is encouraged to proactively share these changes with each other to enhance consistency and transparency across the Department. Below are areas where Commerce entities could collaborate to improve licensing and usage policies for its open data: ● Updating license templates to include guidance for use and applicability of intellectual property licenses as well as standardized data licenses. Currently, Commerce provides some guidance using intellectual property and licensing in certain contexts, but these do not fully address data rights. Expanding the scope to cover the legal and technical aspects of intellectual property and data licenses would help improve general understanding of these concepts as well as fostering clarity and consistency across Commerce. This could be achieved by updating current guidelines to include rights in data, aligning with the increasing relevance of open data in Commerce operations.69 ● Developing detailed guidance on the application and use of metadata and machinereadable licenses that address both copyright, Intellectual Property Rights (IPRs), alongside overall data licenses. ● Creating a dedicated “IP and Data-Licensing” section within Commerce’s existing IP resources could provide robust templates and best practices for both intellectual property and data usage, ensuring that data shared by Commerce adheres to both legal standards and practical open data principles. This section would serve as a central resource across Commerce, enhancing the overall coherence of Commerces data governance practices. 4.2.2 Adopt consistent language and metadata structure around licensing and usage for Commerce’s open data.  69 Guidelines for Use of DOC’s Intellectual Property (IP) Templates 53 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices Although there may be specific considerations for applying data licensing and usage across Commerce’s entities (such as at the bureau level), using consistent language where possible (e.g. in the distribution license) helps align policies and makes them more interpretable by both humans and automated systems accessing these resources. For example, in the context of developing generative AI models with Commerce data, if one bureau uses a term like \"open license\" while another uses \"freely accessible,\" this inconsistency can lead to confusion about the data's usage rights.  In addition, the metadata structure used to disseminate licenses and usage policies in a machine-readable manner should be consistent across Commerce. For instance, if one bureau specifies data licensing terms for AI model training using the DCAT-US standard, while another uses a different format, it can create challenges for automated systems that need to aggregate and interpret this information. Consistent metadata structures ensure that AI systems, such as those accessing Commerce’s open data for model training, can accurately parse and apply licensing terms, ensuring compliance and reducing ambiguity during data use.  5.0 Data Quality and Integrity Data quality and integrity refers to the accuracy, reliability, and consistency of data throughout its lifecycle, ensuring that information is precise, complete, and trustworthy as it is created, processed, and shared. As an authoritative data provider, ensuring the quality and integrity of data as it reaches users is of utmost priority for Commerce. Achieving the data quality and integrity objectives stated here requires careful adherence to all the previous guidelines and best practices. There are two key concepts that Commerce hopes to address as its data flows in and out of generative AI applications: 1. Increase accuracy in AI Responses: When a user queries a generative AI model with a question requiring Commerce data (e.g., “What is the population of Suitland, 54 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices MD?”), Commerce can test that the data retrieved and used is accurate and properly represented. 2. Prioritization of Authoritative Data: Commerce needs to ensure that its data are prioritized over non-authoritative and potentially inaccurate sources in AI-generated responses. Generative AI tools create content based on the data and information they have been previously trained on, but Commerce data are constantly changing over time. Today, when Commerce open data are needed within a generated response, many AI developers find it easier to consume Commerce open data through download, and spend large swaths of time cleaning up and locally storing their now AI-ready Commerce data for their models to retrieve from. Ideally, AI developers could build automated data retrieval systems that directly pull high-quality data from Commerce’s data resources. For AI systems that are not capable of accessing external data, desired functionality could look something like telling the user to go to the Commerce website for the most up-to-date data, or even generating an API query for the user to execute. Commerce does not endorse AI systems retrieving figures directly from its training data, but strongly encourages AI systems to deterministically reference figures from Commerce’s data resources directly (with proper citation). Commerce will do this through continuous collaboration with, and evaluation of, widely used AI systems. Commerce will work to ensure these models do not provide users with outdated or fabricated information and instead disseminate authoritative, up-to-date open data assets.  55 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices Guideline 5.1 Prepare open data for high quality data retrieval After open data have gone through their regular quality checks and after their metadata and documentation have been enriched with properties highlighted within this document, it is important to ensure the data are AI-ready and easily accessible for training, fine-tuning, validation, and retrieval.  Best Practices  5.1.1 Indicate data quality in dataset metadata.   Consistently communicating whether and/or what assessments of data quality have been performed is valuable for all users. As it is the metadata standard currently in place, the most immediately accessible way to do so is by populating the “data quality” field in their DCAT-US dataset metadata (see Table 2 for further details). Even if the value is missing or unknown, adding this field helps users filter on data quality values and more clearly understand if quality checks need additional attention. In the long term, Commerce entities should provide more detailed structured metadata on validation procedures performed. 5.1.2 Automate AI-ready data quality control.  Before publishing data, bureaus should establish automated pipelines that check for missing values, inconsistent data types, and formatting issues, and should ensure that all AI-relevant metadata properties (as highlighted in Table 2) are filled. These automated checks should 56 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices serve as a layer of quality control, ensuring that the dataset is complete and well-structured before it is shared for retrieval. Once automated evaluations are complete, manual review processes should be employed to catch edge cases to ensure accuracy and quality of disseminated data. 5.1.3 Prime APIs for high-quality data retrieval. It's crucial to carefully prime Commerce’s underlying APIs and datasets to ensure highquality data retrieval, especially when working with Retrieval Augmented Generation (RAG) architectures (as seen in Figure 4). RAG models rely on live querying of external knowledge sources to supplement their training, so the quality and structure of the data returned by these APIs can have a significant impact on the model's performance. Though many of these recommendations are recognized throughout this guidance, Commerce agencies should ensure that:  ● The API endpoints exposed by the Commerce datasets are designed to efficiently return the most relevant and granular information.  ● The data returned by the APIs should be well-structured and formatted in a way that is easily consumable by the RAG model. This could include providing the data in a standardized format like JSON. ● The APIs should also provide relevant metadata and contextual information, at both variable and document-level, that can help the RAG model better understand and interpret the data. This could include details about data provenance, quality, and limitations, as well as any relationships or interdependencies between data entities. ● The APIs should be designed to handle the potentially high volume of requests from the RAG model, with low latency and high throughput. This may require implementing caching, pagination, and other optimization techniques on the serverside. ● The API documentation and tooling should be well-crafted to provide a smooth experience for developers integrating the Commerce data into their RAG-powered 57 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices applications. Clear examples, sample code, and integration guides can go a long way in facilitating adoption. By carefully designing and optimizing the underlying data APIs, Commerce data providers can ensure that RAG models are able to effectively leverage their datasets to generate highquality, contextually relevant output. This can lead to more accurate, informative, and useful applications of generative AI in the commerce space. Figure 4: Retrieval-augmented generation (RAG) assists generative AI models in producing quality responses to user queries by pulling in external, real-time data.70 Guideline 5.2 Continuously evaluate open data for accuracy Continuously test how generative AI models interact with Commerce data. This includes validating that the AI system correctly retrieves and processes the data, especially through mechanisms like retrieval-augmented generation (RAG), which allows AI models to pull in external, real-time data. 70 Amazon Web Services provides a high-level overview of retrieval-augmented generation (RAG), then explores how it is implemented in AI models as well as the rationale behind its introduction into models and the benefits it offers. 58 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices By ensuring the accuracy of these retrieval methods, Commerce can prevent misinformation and ensure that its datasets are used correctly by AI systems, especially in high-stakes scenarios such as economic forecasting or policy analysis. Best Practices  5.2.1 Develop benchmarking datasets for AI/ML application domains.   Benchmarking is a common model evaluation approach in AI/ML and evaluates the degree to which a model (or set of models) effectively learns patterns in a reference test dataset.71 These reference datasets, or “benchmarking datasets”, are often one of the first ways that new models are evaluated. Benchmarking datasets can improve both the retrieval and interpretation of Commerce data. However, there are many limitations of existing benchmarks, including that they can contain non-representative and biased data, do not capture needs that AI/ML models purport to address, and are gamed by model developers.7273 Although Commerce cannot address all these issues directly, as a large-scale, authoritative open data provider it is imperative that the agency pursue the development of easily discoverable Commerce-specific benchmark datasets. 5.2.2 Guide generative AI’s responses to Commerce related prompts. Providing prompt libraries, collections of pre-written, templated prompts and ideal responses from a generative AI system, tailored across commonly used Commerce open datasets, can help train models how to interact with live data (data that is changing regularly and is typically different from the training data). Commerce can provide developers with 71 NIST AI Glossary offers a comprehensive overview of AI-related terminology. 72 AI and the Everything in the Whole Wide World Benchmark explores the limits of using a small collection of influential benchmarks across different subfields in AI. Especially when these benchmarks operate as stand-ins for a range of anointed common problems that are frequently framed as foundational milestones on the path towards flexible and generalizable AI systems.  73 Benchmark datasets driving artificial intelligence development fail to capture the needs of medical professionals discusses how current AI benchmark datasets often do not align with the practical needs of medical practitioners. The study highlights that while AI benchmarks are crucial for progress, they frequently overlook tasks that clinicians find most relevant and desirable for automation. 59 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices lists of common prompts and ways their model should best respond to them. This could look like Figure 5, which consists of a table with the type of question or prompt, the example question or prompt, the ideal response, what to avoid, the relevant API call(s), and relevant data in response. This method can also capture nuances about the data within the model responses. Capturing accuracy and nuance are necessary when answering a prompt that necessitates a live response, meaning a response stemming from data that the model is not trained on and is retrieved from an outside source. Figure 5: An example question and ideal response with an example API call using the American Community Survey data. 74 5.2.3 Collaborate with developers of generative AI applications to ensure authoritative open data are prioritized. Due to the newness of these generative models, there is no guaranteed way to ensure authoritative data are prioritized over other data sources without collaboration with AI developers. For example, if asked, \"Tell me about the demographics of Washington, DC\", many of today’s models will present data from a multitude of sources, often ones that are not derived from government entities like the U.S. Census Bureau. A generative AI model 74 Further reading: The Democracy Works Elections Data article Integrating Election Data into Generative AI Tools explains how data and generative AI tools can provide generative AI users with reliable voting information while safeguarding them from potential harms. 60 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices might favor non-government websites over authoritative ones due to biases in training data, an underlying algorithm, data availability, query specificity, or limitations in contextual understanding or source prioritization. Many of the suggestions in these guidelines can support the accuracy of generative AI applications, such as improving metadata and data accessibility, but collaboration with the developers of a model can best ensure that models are trained and tuned to recognize and prioritize authoritative data sources. Future Work Several other opportunities merit further exploration and some need consideration by Commerce but are outside the immediate scope of these guidelines. These include:  ● Exploration of digital signatures. In the context of open Commerce data, the implementation of digital signatures is highly recommended to ensure data integrity and enhance security, particularly for datasets used in training AI models. Digital signatures provide a cryptographic mechanism for verifying that the data originates from a trusted, authoritative source and has not been altered during transmission or storage. This is crucial for maintaining trust and accuracy in datasets, as tampered or falsified data can introduce significant biases or vulnerabilities into machine learning models. By embedding digital signatures, Commerce could secure the authenticity and reliability of their datasets, fostering a safer data ecosystem and reinforcing confidence in the use of open data for AI/ML systems, including generative AI applications. ● Create evaluation metrics for AI-readiness. Though Commerce hopes to implement these guidelines across the department, this first iteration of guidelines is not accompanied with metrics or checklists to evaluate how generative AI-ready its data assets are. For example, it would be helpful to have a technical standard that describes the levels of AI-readiness that Commerce could strive for. Another example could be a checklist for website crawlability. 61 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices By establishing these metrics, Commerce would have clear, actionable targets to measure progress and identify areas for improvement, ensuring data assets are generative AI-ready. ● Educational materials for open data use.  Commerce has considerable educational resources, including a variety of educational websites75 and detailed training programs.76 Even so, resources to educate students, researchers, and the public on Commerce open data should continue to be improved, particularly as Commerce open data increasingly intersects with AI model development and use. This could include both the development of new training programs, tutorials, or materials as well as workshops and training sessions.  ● Partnering with other agencies in open data and AI-readiness efforts.  Commerce acknowledges that other federal agencies are exploring achieving AI-readiness, and looks forward to sharing its learnings with similar federal initiatives. One example of an ongoing partnership for AI-ready efforts is the National Science Foundation’s NAIRR Pilot program,77 incorporating Commerce’s NOAA and USPTO AI-ready data assets. ● Collaborating with AI and open data experts for further iteration. These guidelines represent a first step in an iterative process to improve Commerce open data for generative AI. Regular informal and formal feedback is critical to Commerce’s success in bringing their open data to the American people. To this end, Commerce welcomes feedback from the public, government, academia, industry, and other stakeholders on how each of these topics (data and metadata formats, data storage and dissemination, data licensing and usage, and data integrity and quality) can continue to be improved.  ● Create standard channels for communicating with data users. 75 National Oceanic and Atmospheric Administration (NOAA) Education tools and Resources. 76 Census Bureau’s Census Academy, a learning hub for Data skills, through which users can learn how to access and use Census Bureau data. 77 The National Artificial Intelligence Research Resource (NAIRR) is a vision for a shared national research infrastructure for responsible discovery and innovation in AI. The NAIRR pilot, as directed in the Executive Order on the Safe, Secure and Trustworthy Development and Use of Artificial Intelligence, is a proof-of-concept for the eventual full-scale NAIRR. The pilot will focus on supporting research and education across the nationwide research community, while gaining insights that will refine the design of a full NAIRR. Led by the U.S. National Science Foundation (NSF) in partnership with 12 other federal agencies and 26 non-governmental partners, the pilot makes available governmentfunded, industry and other contributed resources in support of the nation's research and education community.  62 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices Ideally, Commerce should develop a standard way to inform data users of changes to datasets; this could look like a standard page that could be tracked by users, or an email list. These tools can help data users and automated systems know when reconfiguration may be needed.  Additionally, feedback mechanisms are critical for ensuring that Commerce’s open data are continuously optimized for its data users and AI systems. Commerce entities should consider providing a common feedback mechanism (such as an online form) so that data users can contact Commerce with questions about changes, report issues, or provide suggestions for upcoming data releases. Generally, efforts to cultivate a community of open data users that can be informed when changes have been made are encouraged. Initiatives like the Census Bureau’s The Opportunity Project (TOP)78 or NOAA’s Open Data Dissemination Office Hours79 create forums for data users to ask questions and experiment with Commerce open data. Conclusion Commerce is committed to enhancing the integrity, interpretability, accessibility, and representativeness of its open data assets in the era of generative artificial intelligence. By adopting the guidelines and best practices outlined in this document—encompassing documentation, data and metadata formatting, data storage and dissemination, data licensing and usage, and data integrity and quality—the Department aims to ensure that its data remains a robust foundation for innovation, scientific discovery, and evidence-based policymaking. As generative AI technologies advance, the Department acknowledges both the immense opportunities and the accompanying challenges. Ensuring that Commerce data are generative AI78 The Opportunity Project (TOP) offers a framework for agencies to facilitate collaboration between technologists and community advocates in order to rapidly design digital solutions for the public good. The Opportunity Project is a program of the Census Open Innovation Labs at the U.S. Census Bureau. 79 As part of the NOAA Open Data Dissemination (NODD) user engagement series, NODD Office Hours are virtual discussions that allow users to connect with subject matter experts, share experiences, and provide feedback. 63 Generative Artificial Intelligence and Open Data: Guidelines and Best Practices ready and accessible to all stakeholders, regardless of resources, is crucial for fostering equitable innovation.  This document represents the critical initial step in an ongoing, iterative process. Commerce is dedicated to continuous collaboration with stakeholders—including other federal agencies, industry leaders, academia, and the public—to refine these guidelines in response to technological advancements and evolving user needs. Feedback mechanisms will facilitate this collaboration, ensuring that the guidelines remain relevant and effective. By implementing these best practices, the Department not only enhances the utility of its data for generative AI applications but also reinforces its strategic goal to \"expand opportunity and discovery through data.\" In doing so, Commerce reaffirms its mission to serve the American public, ensuring that the benefits of artificial intelligence and machine learning are realized widely and equitably, and that its data assets continue to be a catalyst for innovation and economic growth. ", "metadata": {"original_filename": "item017_US_Generative Artificial Intelligence  and  Open Data Guidelines and Best Practices.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:51:53.404818", "updated_at": "2025-08-28T21:51:53.404818", "word_count": 86250}