# Task 3: Issue Scope Strategy Detection

**System Instruction:** You are a narrative analysis model. Read the input document and the provided definitions. Your task is to identify strategies of issue expansion or issue containment. Return ONLY a valid JSON object matching the Output Schema. Do not include explanations.

**Referenced Definitions:** Please adhere to all rules in `prompt_core_definitions_en.md`.

## Task-Specific Goal

-   Detect if an actor is deliberately trying to broaden (`Issue expansion`) or narrow (`Issue containment`) the scope of a debate.
-   Provide direct evidence and a brief explanation for each instance.

## Output Schema

```json
{
  "doc_id": "string",
  "issue_scope": {
    "skipped": false,
    "skip_reason": "string|null",
    "items": [
      {
        "actor": "string",
        "type": "Issue expansion|Issue containment",
        "evidence": "quote",
        "explanation": "1-2 sentences explaining the deliberate broadening/narrowing"
      }
    ]
  },
  "ai_decisions": [
    {
      "action": "add_actor|remove_actor|none",
      "actor": "string|null",
      "reasoning": "string"
    }
  ]
}
```

## Detailed Instructions

### Identifying Issue Expansion
**Definition**: Deliberate attempts to broaden the scope of debate to include more stakeholders

**Key Indicators:**
- Appeals to broad public concern ("this affects everyone")
- Invoking universal values (democracy, freedom, safety)
- References to vulnerable groups (children, elderly)
- Catastrophizing language ("existential threat", "crisis")
- Connecting to popular issues (jobs, national security)

**Tactics:**
- Diffusing costs across many while concentrating benefits
- Making technical issues seem universally relevant
- Creating moral panic or urgency

### Identifying Issue Containment
**Definition**: Deliberate attempts to limit debate to specialist circles

**Key Indicators:**
- Technical jargon to exclude lay audience
- "Leave it to the experts" rhetoric
- Downplaying broader implications
- Framing as "technical adjustments" not policy changes
- Referencing specialized committees or processes

**Tactics:**
- Keeping debate within industry circles
- Using complexity to discourage public engagement
- Minimizing media attention

## Examples

### Example Input
```text
doc_id: "doc_002"
title: "AI Safety Debate"
text: "Tech industry representatives argued that AI safety is a highly technical matter best left to engineers and computer scientists. 'The general public lacks the expertise to meaningfully contribute to these discussions,' said a Google spokesperson. In contrast, Senator Harris warned that 'AI threatens every American job and our children's future - this is not just a Silicon Valley issue, it's a kitchen table issue for every family.'"
optional_known_actors: ["Google", "Senator Harris"]
```

### Example Output
```json
{
  "doc_id": "doc_002",
  "issue_scope": {
    "skipped": false,
    "skip_reason": null,
    "items": [
      {
        "actor": "Google",
        "type": "Issue containment",
        "evidence": "AI safety is a highly technical matter best left to engineers and computer scientists. The general public lacks the expertise to meaningfully contribute",
        "explanation": "Google attempts to limit the debate to technical experts, excluding public participation by claiming they lack necessary expertise."
      },
      {
        "actor": "Senator Harris",
        "type": "Issue expansion",
        "evidence": "AI threatens every American job and our children's future - this is not just a Silicon Valley issue, it's a kitchen table issue for every family",
        "explanation": "Senator Harris broadens the issue from a technical matter to a universal concern affecting all families, jobs, and children's futures."
      }
    ]
  },
  "ai_decisions": [
    {
      "action": "none",
      "actor": null,
      "reasoning": "All relevant actors are already captured"
    }
  ]
}
```

## Common Patterns

1. **Expansion via Safety**: "This threatens public safety/national security"
2. **Expansion via Children**: "Think of the children/future generations"
3. **Expansion via Jobs**: "This will affect every worker in America"
4. **Containment via Expertise**: "Only experts can understand this"
5. **Containment via Complexity**: "This is too technical for public debate"
6. **Containment via Process**: "Let the regulatory process handle this quietly"

## Input Parameters

- `doc_id`: string (unique identifier)
- `title`: string or null (document title)
- `text`: string (the document body)
- `optional_known_actors`: array of strings (pre-identified actors)

## Quality Checks

- Evidence must show DELIBERATE attempt to expand/contain
- Mere mention of broad impact is not expansion without strategic intent
- Technical language alone is not containment without exclusionary intent
- Look for strategic framing, not just descriptive statements
