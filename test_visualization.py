#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试可视化功能
"""

import asyncio
import aiohttp
import json

API_BASE = "http://127.0.0.1:8001/api/v1/visualization/"

async def test_visualization():
    """测试可视化功能"""
    
    print("=== 测试可视化功能 ===")
    
    async with aiohttp.ClientSession() as session:
        
        # 1. 测试仪表板数据
        print("\n1. 测试仪表板数据...")
        try:
            async with session.get(f"{API_BASE}dashboard") as response:
                if response.status == 200:
                    data = await response.json()
                    print("[OK] 仪表板数据获取成功")
                    print(f"   总文档数: {data.get('total_documents', 0)}")
                    print(f"   总分析数: {data.get('total_analyses', 0)}")
                else:
                    print(f"[ERROR] 仪表板数据获取失败: {response.status}")
        except Exception as e:
            print(f"[ERROR] 仪表板数据获取异常: {e}")
        
        # 2. 测试文档可视化数据
        print("\n2. 测试文档可视化数据...")
        try:
            async with session.get(f"{API_BASE}data/sample_doc_001") as response:
                if response.status == 200:
                    data = await response.json()
                    print("[OK] 文档可视化数据获取成功")
                    print(f"   文档ID: {data.get('doc_id')}")
                    print(f"   任务类型: {data.get('task_types', [])}")
                    print(f"   图表数量: {len(data.get('charts', {}))}")
                else:
                    print(f"[ERROR] 文档可视化数据获取失败: {response.status}")
        except Exception as e:
            print(f"[ERROR] 文档可视化数据获取异常: {e}")
        
        # 3. 测试摘要数据
        print("\n3. 测试摘要数据...")
        try:
            async with session.get(f"{API_BASE}summary/sample_doc_001") as response:
                if response.status == 200:
                    data = await response.json()
                    print("[OK] 摘要数据获取成功")
                    print(f"   行为者总数: {data.get('total_actors', 0)}")
                    print(f"   关系总数: {data.get('total_relations', 0)}")
                    print(f"   复杂度评分: {data.get('complexity_score', 0)}")
                else:
                    print(f"[ERROR] 摘要数据获取失败: {response.status}")
        except Exception as e:
            print(f"[ERROR] 摘要数据获取异常: {e}")
        
        # 4. 测试图表数据
        print("\n4. 测试图表数据...")
        try:
            async with session.get(f"{API_BASE}charts/sample_doc_001") as response:
                if response.status == 200:
                    data = await response.json()
                    print("[OK] 图表数据获取成功")
                    print(f"   文档ID: {data.get('doc_id')}")
                    charts = data.get('charts', {})
                    print(f"   图表数量: {len(charts)}")
                    for task_type, task_charts in charts.items():
                        print(f"   - {task_type}: {len(task_charts)} 个图表")
                else:
                    print(f"[ERROR] 图表数据获取失败: {response.status}")
        except Exception as e:
            print(f"[ERROR] 图表数据获取异常: {e}")
        
        # 5. 测试文档比较
        print("\n5. 测试文档比较...")
        try:
            async with session.get(f"{API_BASE}compare/doc1/doc2") as response:
                if response.status == 200:
                    data = await response.json()
                    print("[OK] 文档比较数据获取成功")
                    comparison = data.get('comparison', {})
                    metrics = comparison.get('metrics_comparison', {})
                    print(f"   文档1行为者: {metrics.get('actors', {}).get('doc1', 0)}")
                    print(f"   文档2行为者: {metrics.get('actors', {}).get('doc2', 0)}")
                else:
                    print(f"[ERROR] 文档比较数据获取失败: {response.status}")
        except Exception as e:
            print(f"[ERROR] 文档比较数据获取异常: {e}")
        
        # 6. 测试数据导出
        print("\n6. 测试数据导出...")
        try:
            async with session.post(f"{API_BASE}export/sample_doc_001?format=json") as response:
                if response.status == 200:
                    data = await response.json()
                    print("[OK] JSON导出成功")
                    print(f"   格式: {data.get('format')}")
                    print(f"   导出时间: {data.get('exported_at')}")
                else:
                    print(f"[ERROR] JSON导出失败: {response.status}")
        except Exception as e:
            print(f"[ERROR] JSON导出异常: {e}")
        
        try:
            async with session.post(f"{API_BASE}export/sample_doc_001?format=csv") as response:
                if response.status == 200:
                    data = await response.json()
                    print("[OK] CSV导出成功")
                    print(f"   格式: {data.get('format')}")
                else:
                    print(f"[ERROR] CSV导出失败: {response.status}")
        except Exception as e:
            print(f"[ERROR] CSV导出异常: {e}")
        
        print("\n=== 可视化功能测试完成 ===")

if __name__ == "__main__":
    asyncio.run(test_visualization())