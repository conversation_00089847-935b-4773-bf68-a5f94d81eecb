{"doc_id": "e1dd6f37-e922-457c-9f23-5a10fed6fac6", "title": "item274_US_TAKA ARIGA Chief Data Scientist U.S. Government Accountability Office", "text": "ARTIFICIAL INTELLIGENCE Key Practices to Help Ensure Accountability in Federal Use of AI Ta<PERSON>, Chief Data Scientist, Science, Technology Assessment and Analytics \r\n\r\nKey Practices to Help Ensure Accountability in Federal Use of AI What GAO Found Artificial intelligence (AI) is evolving at a rapid pace and the federal government cannot afford to be reactive to its complexities, risks, and societal consequences. Federal guidance has focused on ensuring AI is responsible, equitable, traceable, reliable, and governable. Third-party assessments and audits are important to achieving these goals. However, a critical mass of workforce expertise is needed to enable federal agencies to accelerate the delivery and adoption of AI.  Participants in an October 2021 roundtable convened by GAO discussed agencies’ needs for digital services staff, the types of work that a more technical workforce could execute, in areas such as artificial intelligence, and challenges associated with current hiring methods. They noted such staff would require a variety of digital and government-related skills. Participants also discussed challenges associated with existing policies, infrastructure, laws, and regulations that may hinder agency recruitment and retention of digital services staff.  During a September 2020 Comptroller General Forum on AI, experts discussed approaches to ensure federal workers have the skills and expertise needed for AI implementation. Experts also discussed how principles and frameworks on the use of AI can be operationalized into practices for managers and supervisors of these systems, as well as third-party assessors. Following the forum, GAO developed an AI Accountability Framework of key practices to help ensure responsible AI use by federal agencies and other entities involved in AI systems. The Framework is organized around four complementary principles: governance, data, performance, and monitoring.  Artificial Intelligence (AI) Accountability Framework \r\n\r\nChairman <PERSON>, Ranking Member Paul, and Members of the Committee: Thank you for the opportunity to discuss our work on artificial intelligence (AI). My testimony today summarizes two relevant GAO reports: our June 2021 Framework entitled Artificial Intelligence: An Accountability Framework for Federal Agencies and Other Entities1 and our November 2021 report on developing a pipeline of federal digital staff, entitled Digital Services: Considerations for a Federal Academy to Develop a Pipeline of Digital Staff. 2  In our AI Accountability Framework, we highlighted that, given the rapid pace at which AI is evolving, the federal government cannot afford to be reactive to AI’s complexities, risks, and societal consequences. GAO’s objective was to identify key practices to help ensure accountability and responsible AI use by federal agencies and other entities.3 Foundational to solving the AI accountability challenge is having a critical mass of digital expertise to help accelerate responsible delivery and adoption of AI capabilities. A talented and diverse cadre of digital-ready federal employees is essential to a government that can effectively design, develop, deploy, use, and monitor AI systems. In our Digital Services report, we noted that, as the federal government continues its modernization efforts, it faces a severe shortage of digital expertise, including in the field of AI. Each federal agency is individually coping with challenges in hiring, managing, and retaining staff with digital services skills because of a limited pipeline of candidates and bureaucratic processes. Various federal guidance have attempted to guide responsible, equitable, traceable, reliable, and governable AI capabilities. At the same time, robust and independent audits are important to ensuring that these goals are achieved. However, as AI technology advances, responsible management of AI systems will be challenging if the skills necessary to successfully develop, 1GAO, Artificial Intelligence: An Accountability Framework for Federal Agencies and Other Entities, GAO-21-519SP (Washington, D.C.: June 30, 2021).  2GAO, Digital Services: Considerations for a Federal Academy to Develop a Pipeline of Digital Staff, GAO-22-105388 (Washington, D.C.: Nov. 19, 2021).   3The Framework is organized around four complementary principles that address governance, data, performance, and monitoring. For each principle, the Framework describes key practices for federal agencies and other entities that are considering, selecting, and implementing AI systems. Each practice includes a set of questions for entities, auditors, and third-party assessors to consider as well as procedures for auditors and third-party assessors.  1 buy, or use AI capabilities are lacking. In our AI Accountability Framework, we highlight the need to recruit, develop, and retain competent personnel to ensure accountability and responsible use of AI in government programs and processes. Our AI Accountability Framework distills insights from cross-sectoral 23 experts convened during the Forum on Artificial Intelligence by the Comptroller General of the United States held on September 9 and 10, 2020. The work for the report also included an extensive literature review and independent validation of key practices from program officials and subject matter experts.4 For our Digital Services report, GAO convened a roundtable discussion on October 13, 2021 comprised of chief technology officers, chief data officers, chief information officers, and those in similar roles across the federal government, as well as knowledgeable representatives from academia and nonprofits. Additional information about our scope and methodology can be found in that report.  We performed the work on which this testimony is based in accordance with all applicable sections of GAO’s Quality Assurance Framework.  Background AI Life Cycle The life cycle of an AI system involves four phases: design, development, deployment, and continuous monitoring.5 As shown in figure 1, each phase includes considerations articulating the system’s concepts, collecting and processing data, building one or more machine learning models, validating the system, continuously assessing its impact and, if necessary, retiring an AI system from production.6 \r\n\r\nTechnical and Societal Implications of AI Implementing AI systems involves assessing technical performance, as well as identifying and mitigating any societal concerns. For example, to manage technical performance, AI technical stakeholders—data scientists, data engineers, developers, cybersecurity specialists, program managers, and others—will have to ensure that the AI system solves the problem initially identified; uses data sets appropriate for the problem; selects the most suitable learning algorithms; and evaluates and validates the system and its components to ensure it is functioning as intended. Without such assurances, AI systems may perform in unintended ways or otherwise not achieve the goals set out to achieve. As shown in figure 2, in addition to the AI technical stakeholders noted above, a broader community of participants—policy and legal experts, subject matter experts, and individuals using the AI system or impacted by its use, among others—should be engaged in AI development. \r\n\r\nFederal Government Digital Services Federal agencies rely on digital services to interact with the public and improve organizational performance. Such digital services, as defined by the Office of Management and Budget, include the delivery of digital information (e.g., data or content) and transactional services (e.g., online forms) across a variety of platforms, devices, and delivery mechanisms, such as websites, mobile applications, and social media. The digital services take a variety of forms (see fig. 3). \r\n\r\nIndividuals can obtain the necessary digital skills through a variety of pathways. For example, they can attend undergraduate and graduate degree programs, certification programs, and digital skills “boot camps,” or they can access free online courses and learn on their own. Additionally, some employers provide on-the-job training in areas such as AI, data science, and cloud services. For example, one company we interviewed has established an academy to provide its new digital services employees with a multi-week, in-person training to enhance their skills.  Developing a Federal Digital Workforce Pipeline Effective use of AI to improve government operations requires a digitally-ready workforce. Since 2001, however, GAO has identified mission-critical gaps in federal workforce skills and expertise in fields such as science, technology, engineering, and mathematics as high-risk areas.7 Agencies’ needs for digital services staff span varying degrees of urgency and roles. During our October 2021 roundtable discussion, technology leaders and knowledgeable experts shared their perspectives on developing a pipeline of federal digital staff. The discussion included observations about agencies’ immediate and long-term needs, key characteristics of a digital services academy, and agency and government-wide considerations around recruitment and retention of digital services staff. Immediate and Long-Term Needs Roundtable participants discussed agencies’ immediate and long-term needs for digital services staff, the types of inherently governmental work that a digital-ready workforce could execute, and challenges associated with current hiring methods. For example, one roundtable participant noted that their agency had more than 2,000 open positions requiring digital skill sets, and another described numerous project backlogs. Such gaps may lead to cascading implementation challenges.  Additionally, participants said there is a long-term need for in-house talent across roles such as executives, program staff, product managers, software developers, and engineers who understand data architecture and algorithmic elements.  Key Characteristics of a Digital Services Academy Multiple reports by national advisory groups have suggested that one solution to the lack of digital expertise is that the federal government establish a new service academy—similar to the military academies—to train future civil servants in the digital competencies needed to modernize government (see fig. 4).8  \r\n\r\nA digital services academy could help develop the pipeline of digital services workers to better meet the needs of the federal workforce, according to roundtable participants. Digital services staff could apply advanced technologies, such as AI in health care, or conduct investigative work using machine learning systems. Roundtable participants noted that digital services staff could also use newer technologies to develop services faster or at a lower cost.  Considerations for such an academy include the kinds of skills that would be taught and the composition and size of a graduating class. Digital services staff would require a variety of both digital and government-related skills to meet agencies’ needs. Digital skills include application development, data engineering, and other core AI competencies. Government-related skills include knowing how to navigate the requirements of federal data governance and information assurance regimes. In addition, participants noted that a master’s degree pipeline may be more appropriate than an undergraduate degree pipeline because agencies need staff with advanced skills in leading projects and programs, data curation, and digitalization. A digital services academy composed of a diverse student body may further help address societal impacts. One participant noted that programs may attract a more diverse student body if they have a technical component and a social, mission-driven component. For example, a course on “responsible data science” would likely attract students who are demographically diverse and interested in mission-driven work.  Agency and Government-wide Considerations  Agencies can prepare for a pipeline of qualified digital services staff by taking steps such as integrating mission needs into digital services projects, developing professional growth opportunities, cultivating institutional relationships, establishing support networks, and building a data-centric culture, according to roundtable participants. At the same time, participants discussed government-wide challenges associated with existing policies, infrastructure, laws, and regulations that may hinder agency recruitment and retention of digital services staff. For example: • Modernizing technological infrastructure. Participants said a lack of modern technology infrastructure limits the ability of government agencies to leverage the skills of digital services staff. • Addressing compensation concerns. Current salaries and compensation for federal digital services staff are not competitive with the private sector.  • Streamlining the federal hiring process. Without a more streamlined approach to onboarding staff, many digital services staff would likely not be willing to wait out the lengthy federal hiring process when the private sector can hire more quickly. Factors Affecting Oversight of AI in the Public Sector Our AI Accountability Framework emphasizes substantive approaches third-party assessors and auditors should take to develop credible assurance assessments of AI systems. Experts in our forum discussed how principles on the use of AI can be operationalized into practices for managers and supervisors of these systems, as well as third-party assessors. The forum included topics such as governance factors to consider in auditing AI systems, criteria auditors can use in assessing AI systems, issues and challenges in auditing AI systems in the public sector, and evaluation of AI systems for bias and equity.9 Participants also highlighted challenges that federal agencies are facing, such as having a need for technical expertise, a limited understanding of how AI makes its decisions, and limited access to key information due to commercial procurement of such systems.  Our AI Accountability Framework is organized around four complementary principles, which address governance, data, performance, and monitoring. For each principle, the framework describes key practices for federal agencies and other entities that are considering, selecting, and implementing AI systems. For example: • Governance. This principle describes key practices to promote accountability by establishing processes to manage, operate, and oversee AI implementation. For example, Workforce highlights the importance of recruiting, developing, and retaining personnel with multidisciplinary skills and experience in design, development, deployment, assessment, and monitoring of AI systems. • Data. This principle describes key practices to help entities use data that are appropriate for the intended use of each AI system. For example, Reliability emphasizes the need to ensure the reliability of the data used to develop the models. • Performance. This principle describes key practices to help entities produce results that are consistent with program objectives. For example, Bias describes the necessity of identifying potential biases, inequities, and other societal concerns resulting from the AI system. • Monitoring. This principle describes key practices to help entities ensure their AI systems remain reliable and relevant over time. For example, Traceability discusses how entities will need to document results of monitoring activities and any corrective actions taken to promote traceability and transparency. Additionally, each practice includes a set of questions for entities, auditors, and third-party assessors to consider, as well as procedures for auditors and third-party assessors. For more information on the principles and key practices within the Framework, see Appendix I. In summary, we noted in our AI Accountability Framework that AI is evolving at a pace at which we cannot afford to be reactive to its complexities, risks, and societal consequences. Auditors and the oversight community play a vital role in the “trust but verify” equation and need a blueprint to evaluate this changing technology.  More importantly, organizations that build, purchase, and deploy AI need a framework to understand how AI systems will be evaluated. In recent years, both foreign and domestic stakeholders have developed governance and auditing frameworks, in part, to address the technical and societal issues associated with using AI in the public sector.  GAO looks forward to seeing our Framework in use by federal agencies, and to working with the oversight community, researchers, industry, and the Congress to bring verifiable AI oversight to the cross-cutting work that GAO will continue to undertake. Chairman Peters, Ranking Member Paul, and Members of the Committee, this completes my prepared statement. I would be pleased to respond to any questions that you may have at this time. ", "metadata": {"original_filename": "item274_US_TAKA ARIGA Chief Data Scientist U.S. Government Accountability Office.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:35:14.263324", "updated_at": "2025-08-28T21:35:14.263324", "word_count": 17032}