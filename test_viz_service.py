#!/usr/bin/env python3
"""
Test script to verify the visualization service is working correctly
"""
import asyncio
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.services.visualization_service import VisualizationService
from src.services.analysis_service import get_analysis_service
from src.models.schemas import TaskType

async def test_visualization_service():
    """Test the visualization service with actual data"""
    print("Testing visualization service...")
    
    # Test with the document ID that was failing
    doc_id = "doc_1755536095128"
    
    try:
        # Get the analysis service
        analysis_service = await get_analysis_service()
        
        # Get the results
        results = await analysis_service.get_results(doc_id)
        print(f"Results for {doc_id}: {results is not None}")
        
        if results:
            print(f"Results keys: {list(results.keys())}")
            # Test the visualization service
            viz_service = VisualizationService()
            viz_data = await viz_service.generate_visualization_data(doc_id)
            
            if viz_data:
                print(f"Visualization data generated successfully!")
                print(f"Document ID: {viz_data.doc_id}")
                print(f"Task types: {[t.value for t in viz_data.task_types]}")
                print(f"Charts generated: {len(viz_data.charts)}")
                return True
            else:
                print("Failed to generate visualization data")
                return False
        else:
            print("No results found for document")
            return False
            
    except Exception as e:
        print(f"Error testing visualization service: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_visualization_service())
    if success:
        print("\nVisualization service test PASSED")
    else:
        print("\nVisualization service test FAILED")