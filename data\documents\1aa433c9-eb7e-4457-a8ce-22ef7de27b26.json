{"doc_id": "1aa433c9-eb7e-4457-a8ce-22ef7de27b26", "title": "item054_US_AI and the future of national security", "text": "AI and the future of national security\r\nJan 29, 2025\r\n\r\n·\r\n2 min read\r\n\r\nKW400x400.jpg\r\n<PERSON>\r\nPresident of Global Affairs, Google & Alphabet\r\nShare\r\nan illustration of a laptop and three blue icons including a lock, a shield with the letter G, and stars in a blue circle\r\nTo protect national security, America must secure the digital high ground.\r\n\r\nThe U.S. is racing to pioneer breakthrough technologies like AI and quantum computing. At the same time, each day brings news stories about malicious cyber actors burrowing into American telecommunications networks, energy grids and water plants to hold infrastructure hostage and spy on citizens.\r\n\r\nIt’s a reminder that today’s global conflicts are set, staged and playing out in the digital realm.\r\nNo surprise then that the question many are asking is, “Will threat actors exploit cutting edge tools like AI?”\r\n\r\nBut as we reveal in a new report released today, while generative AI can be used by threat actors to accelerate and amplify attacks, they haven’t yet been able to use AI to develop novel capabilities.\r\n\r\nIn other words, the defenders are still ahead — for now.\r\n\r\nTo keep it that way, particularly as powerful new models — which can be leveraged by a wide variety of actors — begin to gain traction, American industry and government need to work together to support our national and economic security.\r\n\r\nHere are three national security imperatives for the AI era:\r\n\r\nPrivate-sector leadership in AI chips and infrastructure is absolutely critical. The United States holds a narrow lead in the global AI landscape with American companies leading the charge, investing hundreds of billions of dollars in research and development annually. To keep up that momentum, we need government support through strategic approaches to trade and export policies that help American firms outcompete China and its national champions in building the data centers and platforms used by people around the world.\r\nWe need public sector leadership in technology procurement and deployment. AI drives significant gains in efficiency, costs and security. But America needs urgent reforms to seize those opportunities and enable the government to develop and deploy cutting-edge digital services at scale. The U.S. government, including the military and intelligence community, needs to streamline outdated procurement systems to enable adoption of AI, cloud and other game-changing technologies. Here the new Department of Government Efficiency can pave the way for modernization efforts while reducing the government’s reliance on insecure legacy vendors.\r\nToday’s threat environment calls for heightened public-private collaboration on cyber defense. As recent breaches like Salt Typhoon and Volt Typhoon make clear, we need expanded collaboration between industry and government to raise cyber defenses and disrupt threats. That will take building on existing efforts to institutionalize operational collaboration, enabling agencies and the private sector to act more quickly to disrupt threats and respond to incidents. AI brings incredible opportunities for cyber defenders, but we need to act with urgency to develop and share best practices through groups like the Coalition for Secure AI and the Frontier Model Forum. And we need to use AI to assist with red teaming for attack techniques, uncovering vulnerabilities in widely used software and building safe and secure models — while sharing insights to address AI security challenges.\r\nAs I’ve said before, America holds the lead in the AI race — but our advantage may not last. By working together we can build on and accelerate America’s AI edge, boost our national security and seize the opportunity ahead.", "metadata": {"original_filename": "item054_US_AI and the future of national security.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:51:53.935518", "updated_at": "2025-08-28T21:51:53.935518", "word_count": 3722}