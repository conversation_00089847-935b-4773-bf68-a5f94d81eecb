// 国际化配置文件
const i18n = {
    zh: {
        // 通用
        common: {
            loading: '正在分析文档，请稍候...',
            error: '错误',
            success: '成功',
            cancel: '取消',
            confirm: '确认',
            save: '保存',
            delete: '删除',
            edit: '编辑',
            view: '查看',
            export: '导出',
            import: '导入',
            search: '搜索',
            filter: '筛选',
            refresh: '刷新',
            back: '返回',
            next: '下一步',
            previous: '上一步',
            submit: '提交',
            reset: '重置',
            close: '关闭',
            or: '或'
        },
        
        // 导航
        nav: {
            home: '🏠 文档分析',
            visualization: '📊 数据可视化',
            tableView: '📋 表格视图',
            promptEditor: '🔧 提示词编辑器',
            apiDocs: '📚 API文档',
            language: '🌐 语言',
            theme: '🌙 主题'
        },
        
        // 页面标题
        titles: {
            systemName: '📄 智能文档分析系统',
            subtitle: '基于智谱AI的政策文档深度分析工具',
            documentAnalysis: '文档分析',
            dataVisualization: '数据可视化',
            tableView: '数据表格视图',
            promptEditor: '提示词编辑器'
        },
        
        // 文档分析页面
        analysis: {
            title: '智能文档分析',
            description: '上传文档并进行深度分析',
            uploadArea: '点击或拖拽文件到此区域上传',
            supportedFormats: '支持 PDF、Word、TXT 格式',
            maxSize: '文件大小不超过 10MB',
            selectDocument: '选择文档',
            documentId: '文档ID',
            analysisType: '分析类型',
            startAnalysis: '开始分析',
            startAdvancedAnalysis: '高级分析',
            batchAdvancedAnalysis: '批量高级分析',
            analysisProgress: '分析进度',
            analysisComplete: '分析完成',
            viewResults: '查看结果',
            downloadReport: '下载报告',
            apiConfig: 'API配置',
            apiKey: '智谱AI API密钥',
            apiKeyPlaceholder: '请输入您的智谱AI API密钥（可选，服务器端已配置则无需填写）',
            apiKeyHint: '如需获取API密钥，请访问',
            apiKeyLink: '智谱AI开放平台',
            baseUrl: 'API Base URL',
            baseUrlPlaceholder: '默认: https://open.bigmodel.cn/api/paas/v4',
            baseUrlHint: '如果您需要使用自定义的智谱AI端点，请在此输入',
            model: '模型选择',
            modelHint: '选择要使用的GLM模型版本，或输入自定义模型名称',
            saveConfig: '保存配置',
            resetConfig: '重置配置',
            inputDocument: '输入文档',
            uploadFile: '上传文件',
            uploadFolder: '上传文件夹',
            pasteText: '粘贴文本',
            enterUrl: '输入URL',
            uploadHint: '支持 PDF、Word、TXT 格式，最大 10MB',
            textPlaceholder: '请在此粘贴您要分析的文档内容...',
            urlPlaceholder: '请输入文档URL（支持在线PDF、Word等）',
            analysisConfig: '分析配置',
            selectTasks: '选择分析任务',
            actorRelation: '行为者关系分析',
            roleFraming: '角色塑造分析',
            problemScope: '问题范围分析',
            causalMechanism: '因果机制分析',
            quickActions: '快速操作',
            switchTheme: '切换主题'
        },
        
        // 可视化页面
        visualization: {
            title: '分析结果可视化',
            description: '将复杂的分析结果转换为直观的图表展示',
            selectDocument: '选择文档',
            loadVisualization: '🔄 加载可视化',
            loadSampleData: '📋 加载示例数据',
            viewRawData: '📄 查看原始数据',
            viewCharts: '📊 查看图表',
            tableView: '📊 表格视图',
            copyData: '📋 复制数据',
            exportJson: '📄 导出JSON',
            exportCsv: '📊 导出CSV',
            analysisTask: '分析任务',
            totalActors: '总行为者数',
            totalRelations: '总关系数',
            complexityScore: '复杂度分数',
            keyThemes: '关键主题',
            rawResponseData: '📄 原始响应数据',
            viewCharts: '📊 查看图表'
        },
        
        // 表格视图页面
        table: {
            title: '数据表格视图',
            description: '详细展示文档分析的原始数据和结构化信息',
            loadData: '🔄 加载数据',
            exportData: '📥 导出数据',
            returnToCharts: '📈 返回图表视图',
            basicInfo: '📋 基本信息',
            analysisSummary: '📊 分析摘要',
            realAnalysisResults: '🧠 真实分析结果',
            chartConfigInfo: '📈 图表配置信息',
            rawJsonData: '🔍 原始JSON数据',
            selectDocumentPrompt: '请选择文档并加载数据',
            loadingData: '正在加载数据...',
            loadFailed: '❌ 加载失败',
            noDataToExport: '没有可导出的数据',
            dataExported: '数据已导出'
        },
        
        // 分析任务类型
        taskTypes: {
            actorRelation: '行为者关系',
            actor_relation: '行为者关系',
            roleFraming: '角色塑造',
            role_framing: '角色塑造',
            problemScope: '问题范围',
            problem_scope: '问题范围',
            causalMechanism: '因果机制',
            causal_mechanism: '因果机制'
        },
        
        // 图表类型
        chartTypes: {
            pie: '饼图',
            bar: '条形图',
            line: '折线图',
            doughnut: '环形图',
            radar: '雷达图',
            network: '网络图'
        },
        
        // 表格字段
        tableFields: {
            field: '字段',
            value: '值',
            documentId: '文档ID',
            title: '标题',
            analysisDate: '分析日期',
            taskTypes: '任务类型',
            hasRealData: '是否有真实数据',
            yes: '是',
            no: '否',
            status: '状态',
            executionTime: '执行时间',
            actorCount: '行为者数量',
            relationCount: '关系数量',
            name: '名称',
            type: '类型',
            description: '描述',
            actionCount: '行动数量',
            mainActions: '主要行动',
            source: '源',
            target: '目标',
            relationType: '关系类型',
            strength: '强度',
            evidenceCount: '证据数量',
            mainEvidence: '主要证据',
            exampleCount: '示例数量',
            mainExamples: '主要示例',
            stepCount: '步骤数',
            conditionCount: '条件数',
            mainSteps: '主要步骤',
            influenceFactors: '影响因子'
        },
        
        // 角色类型
        roleTypes: {
            heroes: '英雄',
            victims: '受害者',
            villains: '反派'
        },
        
        // 策略类型
        strategyTypes: {
            expansionStrategies: '扩大化策略',
            reductionStrategies: '缩小化策略'
        },

        // 提示词编辑器
        prompts: {
            description: '创建、编辑和优化您的AI提示词模板',
            title: '提示词编辑器',
            createNew: '创建新提示词',
            editPrompt: '编辑提示词',
            savePrompt: '保存提示词',
            deletePrompt: '删除提示词',
            promptName: '提示词名称',
            promptContent: '提示词内容',
            promptType: '提示词类型',
            systemPrompt: '系统提示词',
            userPrompt: '用户提示词',
            assistantPrompt: '助手提示词',
            preview: '预览',
            test: '测试',
            export: '导出',
            import: '导入',
            templates: '模板库',
            variables: '变量',
            insertVariable: '插入变量',
            promptLibrary: '提示词库',
            myPrompts: '我的提示词',
            publicPrompts: '公共提示词',
            search: '搜索提示词',
            category: '分类',
            tags: '标签',
            usage: '使用次数',
            rating: '评分',
            lastModified: '最后修改',
            created: '创建时间',
            loadDefaults: '加载默认模板',
            defaultsLoaded: '默认提示词模板已加载',
            noPromptsFound: '未找到提示词，正在加载默认模板...'
        },
        
        // 错误信息
        errors: {
            fileUploadFailed: '文件上传失败',
            analysisStartFailed: '分析启动失败',
            dataLoadFailed: '数据加载失败',
            networkError: '网络错误',
            serverError: '服务器错误',
            invalidFileFormat: '无效的文件格式',
            fileTooLarge: '文件过大',
            noDocumentSelected: '请选择文档',
            noDataAvailable: '暂无数据',
            noDocumentTitle: '请输入文档标题',
            noDocumentContent: '请输入文档内容',
            noTaskSelected: '请至少选择一个分析任务'
        },
        
        // 成功信息
        success: {
            fileUploaded: '文件上传成功',
            analysisStarted: '分析已启动',
            dataLoaded: '数据加载成功',
            dataCopied: '数据已复制到剪贴板',
            dataExported: '数据导出成功',
            filesLoaded: '已加载 {count} 个文件',
            batchAnalysis: '批量分析 ({count} 个文件)'
        }
    },
    
    en: {
        // Common
        common: {
            loading: 'Analyzing document, please wait...',
            error: 'Error',
            success: 'Success',
            cancel: 'Cancel',
            confirm: 'Confirm',
            save: 'Save',
            delete: 'Delete',
            edit: 'Edit',
            view: 'View',
            export: 'Export',
            import: 'Import',
            search: 'Search',
            filter: 'Filter',
            refresh: 'Refresh',
            back: 'Back',
            next: 'Next',
            previous: 'Previous',
            submit: 'Submit',
            reset: 'Reset',
            close: 'Close',
            or: 'or'
        },
        
        // Navigation
        nav: {
            home: '🏠 Document Analysis',
            visualization: '📊 Data Visualization',
            tableView: '📋 Table View',
            promptEditor: '🔧 Prompt Editor',
            apiDocs: '📚 API Documentation',
            language: '🌐 Language',
            theme: '🌙 Theme'
        },
        
        // Page titles
        titles: {
            systemName: '📄 Intelligent Document Analysis System',
            subtitle: 'Advanced Policy Document Analysis Tool Powered by Zhipu AI',
            documentAnalysis: 'Document Analysis',
            dataVisualization: 'Data Visualization',
            tableView: 'Data Table View',
            promptEditor: 'Prompt Editor'
        },
        
        // Document analysis page
        analysis: {
            title: 'Intelligent Document Analysis',
            description: 'Upload documents for in-depth analysis',
            uploadArea: 'Click or drag files to this area to upload',
            supportedFormats: 'Supports PDF, Word, TXT formats',
            maxSize: 'File size should not exceed 10MB',
            selectDocument: 'Select Document',
            documentId: 'Document ID',
            analysisType: 'Analysis Type',
            startAnalysis: 'Start Analysis',
            startAdvancedAnalysis: 'Advanced Analysis',
            batchAdvancedAnalysis: 'Batch Advanced Analysis',
            analysisProgress: 'Analysis Progress',
            analysisComplete: 'Analysis Complete',
            viewResults: 'View Results',
            downloadReport: 'Download Report',
            apiConfig: 'API Configuration',
            apiKey: 'Zhipu AI API Key',
            apiKeyPlaceholder: 'Enter your Zhipu AI API key (optional if configured on server)',
            apiKeyHint: 'To get an API key, please visit',
            apiKeyLink: 'Zhipu AI Open Platform',
            baseUrl: 'API Base URL',
            baseUrlPlaceholder: 'Default: https://open.bigmodel.cn/api/paas/v4',
            baseUrlHint: 'Enter custom Zhipu AI endpoint if needed',
            model: 'Model Selection',
            modelHint: 'Select GLM model version or enter custom model name',
            saveConfig: 'Save Configuration',
            resetConfig: 'Reset Configuration',
            inputDocument: 'Input Document',
            uploadFile: 'Upload File',
            uploadFolder: 'Upload Folder',
            pasteText: 'Paste Text',
            enterUrl: 'Enter URL',
            uploadHint: 'Supports PDF, Word, TXT formats, max 10MB',
            textPlaceholder: 'Please paste your document content here for analysis...',
            urlPlaceholder: 'Enter document URL (supports online PDF, Word, etc.)',
            analysisConfig: 'Analysis Configuration',
            selectTasks: 'Select Analysis Tasks',
            actorRelation: 'Actor Relations Analysis',
            roleFraming: 'Role Framing Analysis',
            problemScope: 'Problem Scope Analysis',
            causalMechanism: 'Causal Mechanism Analysis',
            quickActions: 'Quick Actions',
            switchTheme: 'Switch Theme'
        },
        
        // Visualization page
        visualization: {
            title: 'Analysis Results Visualization',
            description: 'Transform complex analysis results into intuitive chart displays',
            selectDocument: 'Select Document',
            loadVisualization: '🔄 Load Visualization',
            loadSampleData: '📋 Load Sample Data',
            viewRawData: '📄 View Raw Data',
            viewCharts: '📊 View Charts',
            tableView: '📊 Table View',
            copyData: '📋 Copy Data',
            exportJson: '📄 Export JSON',
            exportCsv: '📊 Export CSV',
            analysisTask: 'Analysis Task',
            totalActors: 'Total Actors',
            totalRelations: 'Total Relations',
            complexityScore: 'Complexity Score',
            keyThemes: 'Key Themes',
            rawResponseData: '📄 Raw Response Data',
            viewCharts: '📊 View Charts'
        },
        
        // Table view page
        table: {
            title: 'Data Table View',
            description: 'Detailed display of raw data and structured information from document analysis',
            loadData: '🔄 Load Data',
            exportData: '📥 Export Data',
            returnToCharts: '📈 Return to Chart View',
            basicInfo: '📋 Basic Information',
            analysisSummary: '📊 Analysis Summary',
            realAnalysisResults: '🧠 Real Analysis Results',
            chartConfigInfo: '📈 Chart Configuration Information',
            rawJsonData: '🔍 Raw JSON Data',
            selectDocumentPrompt: 'Please select a document and load data',
            loadingData: 'Loading data...',
            loadFailed: '❌ Load Failed',
            noDataToExport: 'No data to export',
            dataExported: 'Data exported'
        },
        
        // Analysis task types
        taskTypes: {
            actorRelation: 'Actor Relations',
            actor_relation: 'Actor Relations',
            roleFraming: 'Role Framing',
            role_framing: 'Role Framing',
            problemScope: 'Problem Scope',
            problem_scope: 'Problem Scope',
            causalMechanism: 'Causal Mechanism',
            causal_mechanism: 'Causal Mechanism'
        },
        
        // Chart types
        chartTypes: {
            pie: 'Pie Chart',
            bar: 'Bar Chart',
            line: 'Line Chart',
            doughnut: 'Doughnut Chart',
            radar: 'Radar Chart',
            network: 'Network Chart'
        },
        
        // Table fields
        tableFields: {
            field: 'Field',
            value: 'Value',
            documentId: 'Document ID',
            title: 'Title',
            analysisDate: 'Analysis Date',
            taskTypes: 'Task Types',
            hasRealData: 'Has Real Data',
            yes: 'Yes',
            no: 'No',
            status: 'Status',
            executionTime: 'Execution Time',
            actorCount: 'Actor Count',
            relationCount: 'Relation Count',
            name: 'Name',
            type: 'Type',
            description: 'Description',
            actionCount: 'Action Count',
            mainActions: 'Main Actions',
            source: 'Source',
            target: 'Target',
            relationType: 'Relation Type',
            strength: 'Strength',
            evidenceCount: 'Evidence Count',
            mainEvidence: 'Main Evidence',
            exampleCount: 'Example Count',
            mainExamples: 'Main Examples',
            stepCount: 'Step Count',
            conditionCount: 'Condition Count',
            mainSteps: 'Main Steps',
            influenceFactors: 'Influence Factors'
        },
        
        // Role types
        roleTypes: {
            heroes: 'Heroes',
            victims: 'Victims',
            villains: 'Villains'
        },
        
        // Strategy types
        strategyTypes: {
            expansionStrategies: 'Expansion Strategies',
            reductionStrategies: 'Reduction Strategies'
        },

        // Prompt Editor
        prompts: {
            description: 'Create, edit and optimize your AI prompt templates',
            title: 'Prompt Editor',
            createNew: 'Create New Prompt',
            editPrompt: 'Edit Prompt',
            savePrompt: 'Save Prompt',
            deletePrompt: 'Delete Prompt',
            promptName: 'Prompt Name',
            promptContent: 'Prompt Content',
            promptType: 'Prompt Type',
            systemPrompt: 'System Prompt',
            userPrompt: 'User Prompt',
            assistantPrompt: 'Assistant Prompt',
            preview: 'Preview',
            test: 'Test',
            export: 'Export',
            import: 'Import',
            templates: 'Template Library',
            variables: 'Variables',
            insertVariable: 'Insert Variable',
            promptLibrary: 'Prompt Library',
            myPrompts: 'My Prompts',
            publicPrompts: 'Public Prompts',
            search: 'Search Prompts',
            category: 'Category',
            tags: 'Tags',
            usage: 'Usage Count',
            rating: 'Rating',
            lastModified: 'Last Modified',
            created: 'Created',
            loadDefaults: 'Load Default Templates',
            defaultsLoaded: 'Default prompt templates loaded',
            noPromptsFound: 'No prompts found, loading default templates...'
        },
        
        // Error messages
        errors: {
            fileUploadFailed: 'File upload failed',
            analysisStartFailed: 'Failed to start analysis',
            dataLoadFailed: 'Data loading failed',
            networkError: 'Network error',
            serverError: 'Server error',
            invalidFileFormat: 'Invalid file format',
            fileTooLarge: 'File too large',
            noDocumentSelected: 'Please select a document',
            noDataAvailable: 'No data available',
            noDocumentTitle: 'Please enter document title',
            noDocumentContent: 'Please enter document content',
            noTaskSelected: 'Please select at least one analysis task'
        },
        
        // Success messages
        success: {
            fileUploaded: 'File uploaded successfully',
            analysisStarted: 'Analysis started',
            dataLoaded: 'Data loaded successfully',
            dataCopied: 'Data copied to clipboard',
            dataExported: 'Data exported successfully',
            filesLoaded: 'Loaded {count} files',
            batchAnalysis: 'Batch Analysis ({count} files)'
        }
    }
};

// 当前语言 - 默认使用英文
let currentLanguage = localStorage.getItem('language') || 'en';

// 获取翻译文本
function t(key, params = {}) {
    const keys = key.split('.');
    let value = i18n[currentLanguage];

    for (const k of keys) {
        if (value && typeof value === 'object') {
            value = value[k];
        } else {
            break;
        }
    }

    let result = value || key;

    // 替换参数
    if (typeof result === 'string' && Object.keys(params).length > 0) {
        for (const [paramKey, paramValue] of Object.entries(params)) {
            result = result.replace(`{${paramKey}}`, paramValue);
        }
    }

    return result;
}

// 切换语言
function switchLanguage(lang) {
    if (lang && i18n[lang]) {
        currentLanguage = lang;
        localStorage.setItem('language', lang);
        updatePageLanguage();
    }
}

// 更新页面语言
function updatePageLanguage() {
    // 更新所有带有 data-i18n 属性的元素
    document.querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.getAttribute('data-i18n');
        const text = t(key);

        if (element.tagName === 'INPUT' && (element.type === 'text' || element.type === 'search' || element.type === 'password')) {
            element.placeholder = text;
        } else if (element.tagName === 'TEXTAREA') {
            element.placeholder = text;
        } else if (element.tagName === 'INPUT' && element.type === 'button') {
            element.value = text;
        } else if (element.hasAttribute('title')) {
            element.title = text;
        } else {
            element.textContent = text;
        }
    });

    // 更新页面标题
    const titleElement = document.querySelector('title');
    if (titleElement && titleElement.getAttribute('data-i18n')) {
        titleElement.textContent = t(titleElement.getAttribute('data-i18n'));
    }

    // 更新语言选择器
    updateLanguageSelector();
}

// 更新语言选择器
function updateLanguageSelector() {
    const languageButtons = document.querySelectorAll('.language-selector button');
    languageButtons.forEach(button => {
        button.classList.remove('active');
        if (button.getAttribute('data-lang') === currentLanguage) {
            button.classList.add('active');
        }
    });
}

// 初始化国际化
function initI18n() {
    // 页面加载完成后更新语言
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', updatePageLanguage);
    } else {
        updatePageLanguage();
    }
}

// 自动初始化
initI18n();
