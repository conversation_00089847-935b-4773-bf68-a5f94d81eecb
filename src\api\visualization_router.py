"""
可视化API路由
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
import json
from src.services.visualization_service import VisualizationService
from src.services.analysis_service import AnalysisService
from src.services.zhipu_client import ZhipuAIClient
from src.db.database import get_db
from sqlalchemy.orm import Session
from src.db.models import AnalysisTask
from loguru import logger

router = APIRouter(prefix="/api/visualization", tags=["visualization"])

# 创建可视化服务实例
visualization_service = VisualizationService()

@router.get("/chart/{doc_id}/{task_type}")
async def get_chart_config(
    doc_id: str,
    task_type: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取指定分析任务的图表配置
    
    Args:
        doc_id: 文档ID
        task_type: 任务类型
        db: 数据库会话
        
    Returns:
        ECharts配置对象
    """
    try:
        # 查询分析结果
        task = db.query(AnalysisTask).filter(
            AnalysisTask.doc_id == doc_id,
            AnalysisTask.task_type == task_type,
            AnalysisTask.status == "completed"
        ).first()
        
        if not task:
            raise HTTPException(status_code=404, detail=f"未找到文档 {doc_id} 的 {task_type} 分析结果")
        
        # 解析结果
        result = json.loads(task.result) if isinstance(task.result, str) else task.result
        
        # 根据任务类型生成对应的图表配置
        if task_type == "actor_relation":
            chart_config = visualization_service.generate_actor_relation_chart(result)
        elif task_type == "role_framing":
            chart_config = visualization_service.generate_role_framing_chart(result)
        elif task_type == "problem_scope":
            chart_config = visualization_service.generate_problem_scope_chart(result)
        elif task_type == "causal_mechanism":
            chart_config = visualization_service.generate_causal_mechanism_chart(result)
        else:
            raise HTTPException(status_code=400, detail=f"不支持的任务类型: {task_type}")
        
        return {
            "success": True,
            "task_type": task_type,
            "doc_id": doc_id,
            "chart_config": chart_config
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成图表配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成图表配置失败: {str(e)}")

@router.get("/dashboard/{doc_id}")
async def get_dashboard_config(
    doc_id: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取文档的完整可视化仪表板配置
    
    Args:
        doc_id: 文档ID
        db: 数据库会话
        
    Returns:
        包含所有图表的仪表板配置
    """
    try:
        # 查询所有完成的分析任务
        tasks = db.query(AnalysisTask).filter(
            AnalysisTask.doc_id == doc_id,
            AnalysisTask.status == "completed"
        ).all()
        
        if not tasks:
            raise HTTPException(status_code=404, detail=f"未找到文档 {doc_id} 的分析结果")
        
        # 收集所有结果
        all_results = {}
        for task in tasks:
            result = json.loads(task.result) if isinstance(task.result, str) else task.result
            all_results[task.task_type] = {
                "result": result,
                "success": True,
                "task_type": task.task_type
            }
        
        # 生成仪表板配置
        dashboard_config = visualization_service.generate_summary_dashboard(all_results)
        
        return {
            "success": True,
            "doc_id": doc_id,
            "dashboard": dashboard_config,
            "task_count": len(tasks)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成仪表板配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成仪表板配置失败: {str(e)}")

@router.get("/mock/demo")
async def get_mock_visualization() -> Dict[str, Any]:
    """获取模拟数据的可视化配置（用于演示）
    
    Returns:
        包含模拟数据的可视化配置
    """
    # 模拟数据
    mock_data = {
        "actor_relation": {
            "result": {
                "actors": [
                    {
                        "name": "政府",
                        "type": "政府机构",
                        "description": "负责监管和提供支持资源的主体",
                        "actions": ["加强监管", "提供支持"],
                        "stance": "既强调监管也提倡支持"
                    },
                    {
                        "name": "科技企业",
                        "type": "企业",
                        "description": "创新主体和被监管对象",
                        "actions": ["技术创新", "合规经营"],
                        "stance": "积极创新"
                    },
                    {
                        "name": "民营企业",
                        "type": "企业",
                        "description": "被鼓励承担社会责任的对象",
                        "actions": ["承担社会责任", "为社会发展做贡献"],
                        "stance": "应承担更多社会责任"
                    }
                ],
                "relations": [
                    {"source": "政府", "target": "科技企业", "type": "监管与支持"},
                    {"source": "政府", "target": "民营企业", "type": "引导"},
                    {"source": "民营企业", "target": "社会", "type": "贡献"}
                ],
                "key_findings": [
                    "政府扮演双重角色：监管者和支持者",
                    "企业社会责任被强调",
                    "监管与支持并重的政策导向"
                ]
            }
        },
        "role_framing": {
            "result": {
                "heroes": [
                    {
                        "name": "政府",
                        "description": "被塑造为维护秩序和促进发展的英雄",
                        "evidence": ["加强监管", "提供支持资源"]
                    }
                ],
                "victims": [
                    {
                        "name": "社会",
                        "description": "潜在的受害者，需要保护",
                        "evidence": ["可能受到违规企业影响"]
                    }
                ],
                "villains": [
                    {
                        "name": "违规企业",
                        "description": "被暗示为潜在威胁",
                        "evidence": ["需要加强监管"]
                    }
                ],
                "narratives": [
                    {
                        "type": "监管叙事",
                        "description": "强调监管的必要性和正当性",
                        "examples": ["确保合规经营"]
                    }
                ],
                "key_findings": [
                    "政府被塑造为问题解决者",
                    "企业被区分为合规与违规两类"
                ]
            }
        },
        "problem_scope": {
            "result": {
                "expansion_strategies": [
                    {
                        "type": "泛化",
                        "description": "将问题扩大到所有科技企业",
                        "examples": ["科技企业的监管", "民营企业的社会责任"]
                    }
                ],
                "reduction_strategies": [
                    {
                        "type": "具体化",
                        "description": "将问题限定在特定范围",
                        "examples": ["仅针对违规企业", "特定领域的创新"]
                    }
                ],
                "framing_patterns": [
                    {
                        "type": "责任框架",
                        "description": "强调不同主体的责任",
                        "examples": ["政府监管责任", "企业社会责任"]
                    }
                ],
                "key_findings": [
                    "问题范围被策略性调整",
                    "责任框架清晰明确"
                ]
            }
        },
        "causal_mechanism": {
            "result": {
                "causal_chains": [
                    {
                        "sequence": ["加强监管", "确保合规", "促进健康发展"],
                        "description": "监管促进发展的因果链"
                    },
                    {
                        "sequence": ["提供支持", "激发创新", "推动产业升级"],
                        "description": "支持创新的因果链"
                    }
                ],
                "attribution_patterns": [
                    {
                        "target": "监管需求",
                        "factors": ["企业违规行为", "市场失灵"],
                        "evidence": ["需要政府干预"]
                    }
                ],
                "responsibility_framing": {
                    "responsible_actors": ["政府", "企业"],
                    "absolved_actors": ["社会公众"],
                    "framing_strategy": "共同责任但各有侧重"
                },
                "key_findings": [
                    "因果链条清晰",
                    "责任分配明确"
                ]
            }
        }
    }
    
    # 生成仪表板配置
    dashboard_config = visualization_service.generate_summary_dashboard(mock_data)
    
    return {
        "success": True,
        "doc_id": "mock_demo",
        "dashboard": dashboard_config,
        "message": "这是演示用的模拟数据"
    }
