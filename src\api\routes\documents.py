from fastapi import APIRouter, HTTPException, Depends, status, Body, UploadFile, File, Form
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime
import uuid
import os

from src.models.schemas import DocumentCreate, DocumentResponse
from src.services.document_service import DocumentService, get_document_service
from src.core.auth import auth_deps
from src.core.validation import input_validator
from src.core.security import security_service

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/", response_model=DocumentResponse, status_code=status.HTTP_201_CREATED)
async def create_document(
    document: DocumentCreate,
    document_service: DocumentService = Depends(get_document_service)
):
    """创建新文档"""
    try:
        # 如果没有提供doc_id，则生成一个
        if not document.doc_id:
            document.doc_id = str(uuid.uuid4())
        
        # 检查metadata中是否有API密钥和base URL
        if document.metadata:
            if 'api_key' in document.metadata:
                api_key = document.metadata.pop('api_key')  # 取出并移除
                if api_key:
                    os.environ['TEMP_ZHIPUAI_API_KEY'] = api_key
            
            if 'base_url' in document.metadata:
                base_url = document.metadata.pop('base_url')  # 取出并移除
                if base_url:
                    os.environ['ZHIPUAI_BASE_URL'] = base_url
            
        # 创建文档
        doc_result = await document_service.create_document(document)
        return doc_result
    except Exception as e:
        logger.error(f"创建文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建文档失败: {str(e)}"
        )


@router.get("/{doc_id}", response_model=DocumentResponse)
async def get_document(
    doc_id: str,
    document_service: DocumentService = Depends(get_document_service)
):
    """获取文档信息"""
    try:
        doc = await document_service.get_document(doc_id)
        if not doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"文档 {doc_id} 不存在"
            )
        return doc
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文档失败: {str(e)}"
        )


@router.get("/", response_model=List[DocumentResponse])
async def list_documents(
    skip: int = 0, 
    limit: int = 100,
    document_service: DocumentService = Depends(get_document_service)
):
    """获取文档列表"""
    try:
        documents = await document_service.list_documents(skip, limit)
        return documents
    except Exception as e:
        logger.error(f"获取文档列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文档列表失败: {str(e)}"
        )


@router.delete("/{doc_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_document(
    doc_id: str,
    document_service: DocumentService = Depends(get_document_service)
):
    """删除文档"""
    try:
        deleted = await document_service.delete_document(doc_id)
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"文档 {doc_id} 不存在"
            )
        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除文档失败: {str(e)}"
        )


@router.put("/{doc_id}", response_model=DocumentResponse)
async def update_document(
    doc_id: str,
    document: DocumentCreate,
    document_service: DocumentService = Depends(get_document_service),
    current_user: Dict[str, Any] = Depends(auth_deps.get_current_user)
):
    """更新文档"""
    try:
        # 验证文档ID
        if not doc_id or not doc_id.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文档ID不能为空"
            )
        
        # 确保使用路径中的doc_id
        document.doc_id = doc_id
        updated_doc = await document_service.update_document(document)
        if not updated_doc:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"文档 {doc_id} 不存在"
            )
        
        logger.info(f"用户 {current_user['username']} 更新文档 {doc_id}")
        return updated_doc
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新文档失败: {str(e)}"
        )


@router.post("/upload", response_model=DocumentResponse, status_code=status.HTTP_201_CREATED)
async def upload_document(
    file: UploadFile = File(...),
    title: Optional[str] = Form(None),
    doc_id: Optional[str] = Form(None),
    document_service: DocumentService = Depends(get_document_service),
    current_user: Dict[str, Any] = Depends(auth_deps.get_current_user)
):
    """上传文档文件"""
    try:
        # 验证文件上传
        validation_result = await input_validator.validate_file_upload(file)
        
        # 读取文件内容
        content = await file.read()
        await file.seek(0)  # 重置文件指针
        
        # 根据文件类型处理内容
        text_content = ""
        if file.content_type.startswith('text/'):
            # 文本文件直接读取
            text_content = content.decode('utf-8', errors='ignore')
        else:
            # 其他文件类型需要特殊处理（PDF、Word等）
            # 这里暂时返回错误，实际应该集成相应的文档解析库
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"暂不支持 {file.content_type} 文件类型"
            )
        
        # 验证文本内容
        sanitized_text = input_validator.validate_document_text(text_content)
        
        # 生成文档标题
        document_title = title or file.filename.replace('_', ' ').replace('-', ' ')
        
        # 生成文档ID
        document_id = doc_id or str(uuid.uuid4())
        
        # 创建文档
        document_data = DocumentCreate(
            doc_id=document_id,
            title=document_title,
            text=sanitized_text,
            metadata={
                "original_filename": file.filename,
                "file_size": validation_result["size"],
                "content_type": validation_result["content_type"],
                "uploaded_by": current_user["username"],
                "upload_method": "file_upload"
            }
        )
        
        doc_result = await document_service.create_document(document_data)
        
        logger.info(f"用户 {current_user['username']} 上传文件 {file.filename}，创建文档 {document_id}")
        return doc_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传文档失败: {str(e)}"
        )


@router.post("/batch-upload", response_model=List[DocumentResponse])
async def batch_upload_documents(
    files: List[UploadFile] = File(...),
    document_service: DocumentService = Depends(get_document_service),
    current_user: Dict[str, Any] = Depends(auth_deps.get_current_user)
):
    """批量上传文档文件"""
    try:
        if not files:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="没有选择文件"
            )
        
        # 限制批量上传数量
        if len(files) > 1000:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="批量上传最多支持1000个文件"
            )
        
        results = []
        for file in files:
            try:
                # 验证文件上传
                validation_result = await input_validator.validate_file_upload(file)
                
                # 读取文件内容
                content = await file.read()
                await file.seek(0)
                
                # 处理文本文件
                if file.content_type.startswith('text/'):
                    text_content = content.decode('utf-8', errors='ignore')
                    sanitized_text = input_validator.validate_document_text(text_content)
                    
                    # 创建文档
                    document_id = str(uuid.uuid4())
                    document_data = DocumentCreate(
                        doc_id=document_id,
                        title=file.filename.replace('_', ' ').replace('-', ' '),
                        text=sanitized_text,
                        metadata={
                            "original_filename": file.filename,
                            "file_size": validation_result["size"],
                            "content_type": validation_result["content_type"],
                            "uploaded_by": current_user["username"],
                            "upload_method": "batch_upload"
                        }
                    )
                    
                    doc_result = await document_service.create_document(document_data)
                    results.append(doc_result)
                    
            except Exception as e:
                logger.error(f"处理文件 {file.filename} 失败: {str(e)}")
                continue
        
        logger.info(f"用户 {current_user['username']} 批量上传 {len(results)} 个文件")
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量上传文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量上传文档失败: {str(e)}"
        )
