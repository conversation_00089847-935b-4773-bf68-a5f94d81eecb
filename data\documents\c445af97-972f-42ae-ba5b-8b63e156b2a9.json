{"doc_id": "c445af97-972f-42ae-ba5b-8b63e156b2a9", "title": "item164_US_Tort Law Is the Best Way to Regulate AI; California's SB 1047 would make clear that companies face legal liability if they negligently cause harm.", "text": "Tort Law Is the Best Way to Regulate AI; California's SB 1047 would make clear that companies face legal liability if they negligently cause harm.\r\n\r\nOpenAI released a powerful new artificial-intelligence model this month that modestly but meaningfully increases the risk of catastrophic bioterrorism, according to OpenAI itself. How should the law govern such a potent, rapidly developing technology? Since our understanding of AI safety is nascent, and highly dangerous capabilities haven't yet emerged, instituting command-and-control regulation may be premature. Instead, we should regulate AI development by building on the common law of torts, one of our oldest legal institutions.\r\n\r\nTort law is animated by a simple and powerful idea: If you harm other people by failing to take reasonable care, then fairness requires you to compensate them. By creating an incentive for reasonable precaution rather than imposing detailed bureaucratic restrictions, tort law provides the most basic form of risk governance in a decentralized economy. But what does \"reasonable care\" require when developing extremely powerful AI?\r\n\r\nCalifornia's Senate Bill 1047, first-of-its-kind legislation that the Legislature passed this month, builds on industry safety practices to help answer this question. Before the end of the month, Gov. <PERSON> will decide whether to veto SB 1047 or sign it into law. The bill has prompted a heated debate between ideologically diverse coalitions: <PERSON><PERSON>, leading AI developer <PERSON><PERSON><PERSON> and two Turing Award-winning \"godfathers\" of AI (<PERSON> and <PERSON><PERSON><PERSON>) support it, while venture capital firm a16z, <PERSON> and a \"godmother\" of AI (Fei-Fei Li) are opposed.\r\n\r\nThe stakes are high. California is home to the world's leading AI companies, and SB 1047 is the first serious American effort to regulate AI development. The bill would cover many companies outside the state (and the U.S.), since it would apply to all models developed or released in California. What's more, the bill could provide a blueprint for governing AI in other states, at the federal level or even in other countries. In my view, the SB 1047 model offers a sensible middle path between major command-and-control regulation and no regulation at all. By incrementally refining and enhancing the law of torts—a nimble and well-established body of law—the bill would create a measured and flexible regulatory scheme.\r\n\r\nThe most common criticism of SB 1047 is that it would stifle innovation by holding AI companies liable when wrongdoers use their systems to cause harm. Critics seem to think that such liability is a radical new legal invention. They are wrong. If you negligently store, test or release a highly dangerous tool such as powerful explosives—and, as a result, you foreseeably enable bad actors to harm innocent people— tort law holds you liable . Will the law give special treatment to big tech companies if their negligent AI development foreseeably enables bioterrorist attacks or other grievous harm? The odds are low.\r\n\r\nMany CEOs and employees of AI companies, along with several scholars who helped invent the field, believe that immensely dangerous AI could arrive in a few years. Whether or not that happens, the possibility that careless AI development could cause massive harm over the coming years and decades is entirely foreseeable. That is why every leading AI company has promised, in summits convened by the White House and the governments of the U.K. and South Korea, to take robust precautions against releasing systems that enable biological attacks, cyberattacks on critical infrastructure, and other such calamities. And tort law is especially unforgiving to those who cause harm by dishonoring their commitments.\r\n\r\nSo why is new legislation like SB 1047 needed? For one thing, much of the AI industry seems unaware that AI developers are already exposed to tort liability if they negligently inflict or enable harm. Tort law is supposed to deter negligent behavior, but it can't deter people who don't understand the law, or who falsely believe the law doesn't apply to them. SB 1047 puts it beyond any doubt that big AI developers must heed one of the law's oldest commands: When risking harm to other people or their property, you must take reasonable care.\r\n\r\nBut SB 1047 is more than a codification of existing tort law. It also remedies some of tort law's most significant limitations. Discovery procedures and the trial process can foster transparency and public accountability, but only after a defendant's negligent behavior has already resulted in harm. The public should know about unsafe development practices at big AI companies well before they cause a catastrophe. Thus SB 1047 creates important new whistleblower provisions and basic transparency requirements for companies developing the most powerful new AI systems. The bill also draws on innovative measures pioneered by the industry—such as OpenAI's Preparedness Framework, Google's Frontier Safety Framework, and Anthropic's Responsible Scaling Policy—to help clarify what reasonable care involves in this novel domain.\r\n\r\nAI developers have a duty to take reasonable care. If they don't, and they cause grave harm as a result, tort law will hold them accountable. That is true whether or not Gov. Newsom signs SB 1047. But California, and the world, will be safer if he does.", "metadata": {"original_filename": "item164_US_Tort Law Is the Best Way to Regulate AI; California's SB 1047 would make clear that companies face legal liability if they negligently cause harm..txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:35:12.926618", "updated_at": "2025-08-28T21:35:12.926618", "word_count": 5405}