{"doc_id": "082a2e75-dae2-4f0f-81a6-98cca974c2af", "title": "item180_US_Rethinking Privacy  in the AI Era", "text": "Rethinking Privacy  in the AI Era\r\n\r\nChapter 2: Data Protection and Privacy: Key Concepts and Regulatory Landscape The last two years have seen groundbreaking advances in AI, a period in which generative AI tools became widely available, inspiring and alarming millions of people around the world. Large language models (LLMs) such as GPT-4, PaLM, and Llama, as well as AI image generation systems such as Midjourney and DALL-E, have made a tremendous public splash, while many other less headline-grabbing forms of AI also continued to advance at breakneck speed. While recognizing the recent dominance of LLMs in public discourse, in this paper we consider the data privacy and protection implications of a wider array of AI systems, defined more broadly as “engineered or machine-based system[s] that can, for a given set of objectives, generate outputs such as predictions, recommendations, or decisions influencing real or virtual environments.”6 For example, we consider a range of predictive AI systems, such as those based on machine learning, that analyze vast amounts of data to make classifications and predictions, ranging from facial recognition systems to hiring algorithms, criminal sentencing algorithms, behavioral advertising and profiling, and emotion recognition tools, to name a few. These systems operate with varying levels of autonomy, with “automated decisionmaking” referring to AI systems making decisions (such as awarding a loan or hiring a new employee) without any, or minimal, human involvement.7 While generative AI systems also rely on predictive processes, those systems ultimately focus on creating new content ranging from text to images, video, and audio as their output.    While some policymakers are keen to demonstrate that they are assuaging the public’s growing concerns about the rapid development and deployment of AI by introducing new legislation, there is a growing debate over whether existing laws provide sufficient protection and oversight of AI systems. In response to these widely publicized developments, both policymakers and the general public have called for regulating AI technologies. Since 2020, countries around the world have begun passing AI-specific legislation.8 While the EU finalizes the parameters of its AI Act, the bloc’s attempt to provide overarching regulation of AI technologies, the United States presently lacks a generalized approach to AI regulation, though multiple federal agencies have released policy statements asserting their authority over AI systems that produce outputs in violation of existing law, such as civil rights and consumer protection statutes.9 Several U.S. states and municipalities have also tackled general consumer regulation of AI systems.10 7White Paper Rethinking Privacy in the AI Era 8 While some policymakers are keen to demonstrate that they are assuaging the public’s growing concerns about the rapid development and deployment of AI by introducing new legislation, there is a growing debate over whether existing laws provide sufficient protection and oversight of AI systems.  As we discuss in this white paper, privacy and data protection laws in the United States and the EU already do the work of regulating some—though not all—aspects of AI. Whether these existing laws, and proposed ones based on these frameworks, are adequate to anticipate and respond to emergent forms of AI while also addressing privacy risks and harms is a question we will address later in this paper. Before we delve into the details of our arguments, we provide a brief overview of the present state of data protection and privacy regulations in the EU and the United States that impact AI systems, starting with the foundational Fair Information Practices (FIPs). Those familiar with these regulations may wish to skip ahead to the next chapter. Data Privacy and Data Protection Data privacy and data protection are sometimes used interchangeably in casual conversation. While these terms are related and have some overlap, they differ in significant ways. Data privacy is primarily concerned with who has authorized access to collect, process, and potentially share one’s personal data, and the extent to which one can exercise control over that access, including by opting out of data collection. The term’s scope is fairly broad, as it pertains not just to personal data but to any kind of data that, if accessed by others, would be seen as infringing on one’s right to a private life and personal autonomy. Privacy is often described in terms of personal control over one’s information, though this conception has been challenged by the increasing loss of control that many have over their data. But it is this notion of personal control that underlies both existing privacy regulations and frameworks. What is considered “private” is also contextually contingent, in that data shared in one context may be viewed as appropriate by an individual or data subject (e.g., sharing one’s real time location data with a friend) but not in another (e.g., a third party collecting one’s real time location data and using it for advertising purposes without explicit permission). The relational nature of data has also challenged the idea of privacy as personal control, as data that is social in nature (e.g., shared social media posts) or data that can reveal both biological ties and ethnic identities (e.g., genetic data) continue to grow. White Paper Data Privacy and Data Protection (cont’d) Rethinking Privacy in the AI Era Data protection refers to the act of safeguarding individuals’ personal information using a set of procedural rights, which includes ensuring that data is processed fairly, for specified purposes, and collected on the basis of one of six accepted bases for processing.11 Consent is the strictest basis and allows individuals to withdraw it after the fact. By contrast, legitimate interest provides the greatest latitude—this legal ground for processing data allows processors to justify data processing on the basis of this data being needed to carry out tasks related to their business activity. Data processors must still respect individuals’ fundamental data protection rights, such as providing notice when data is collected, giving access to one’s collected information, providing the means to correct errors, delete, or transfer it (data portability) to other processors, and affording the right to object to the processing itself. But there is a bias toward accepting as a given the collectibility of some forms of personal data by default. The EU formally distinguishes between personal privacy (i.e., respect for an individual’s private life) and data protection, enshrining each in its European Charter of Fundamental Rights. Nevertheless, there are areas of overlap and the concepts complement each other. When data protection principles do not apply because the collected information is not personal data (e.g., anonymized body scanner data), the fundamental right to privacy applies as the collection of bodily information affects a person’s individual autonomy. Conversely, data protection principles can ensure limits on personal data processing, even when such processing is not thought to infringe upon privacy.12 a. Fair Information Practice Principles: The framework behind data protection and privacy Most modern privacy legislation, at its core, is based on the Fair Information Practices (FIPs), a 50-plus-year-old set of principles that are accepted around the globe as the fundamental framework for providing individuals with due process rights for their personal data.13 Proposed as a U.S. federal code of fair information practices for automated personal data systems in the early 1970s, the FIPs introduced five safeguard requirements regarding personal privacy as a means of ensuring “informational due process.”14 They focus on the obligations of record-keeping organizations to allow individuals to know about, prevent alternative uses of, and correct information collected about them.15 As policy expert Mark MacCarthy describes, “All these measures worked together as a coherent whole to enforce the rights of individuals to control the collection and use of information about themselves.”16 Rather than framing information privacy as a fundamental human right, as both the United Nations Universal Declaration of Human Rights and the 9European Charter of Fundamental Rights do with a more general conception of privacy, the FIPs outline a set of rules and obligations between the individual (data subject) and the record-keeper (data processor).17 The FIPs were drafted around a core assumption that the state has a legitimate need to collect data about its citizens for administrative and record-keeping purposes.18  This assumption—that data collection is necessary and appropriate for the workings of the modern state but must be done fairly and with procedural safeguards in place—was incorporated into subsequent revisions of the FIPs, even as they were increasingly applied to the private sector. The most internationally influential version, developed by the Organisation for Economic Cooperation and Development (OECD) in 1980 and amended in 2013, consolidates and expands the original FIPs into eight principles covering collection limitation, data quality, purpose specification, use limitation, security safeguards, openness, individual participation, and accountability.19 The guidelines reflect a broad international consensus on how to approach privacy protection that has translated into a policy convergence around enshrining the FIPs as a core part of information privacy legislation around the world.20 Despite having been conceived long before the emergence of the commercial internet, let alone social media platforms and generative AI tools, core components of the FIPs, such as data minimization and purpose limitation21, directly impact today’s AI systems by limiting how broadly companies can repurpose data collected for one context or purpose to create or train new AI systems. The EU’s General Data Protection Regulation (GDPR), as well as California’s privacy regulations and the proposed American Data Privacy and Protection Act (ADPPA), relies heavily on these principles. These regulations’ attempts to clarify White Paper Rethinking Privacy in the AI Era The FIPs were drafted around a core assumption that the state has a legitimate need to collect data about its citizens for administrative and record-keeping purposes. the application of the FIPs to privacy controls amid exponentially increasing volumes of online consumers and commercial data shed further light on the impact of privacy regulation on AI. b. General Data Protection Regulation: The “global standard” for data protection Passed in 2016 and in effect as of 2018, the General Data Protection Regulation is the EU’s attempt to both update the 1995 Data Protection Directive and harmonize the previous patchwork of fragmented national data privacy regimes across EU member countries and to enable stronger enforcement of Europeans’ data rights.22 At its core, the GDPR is centered on personal data, which is defined as “any information relating to an identified or identifiable natural person.”23 It grants individuals (“data subjects”) rights regarding the processing of their personal data, such as the right to be informed and a limited right to be forgotten, and guides how businesses can process personal information. It is arguably the most significant data protection legislation in the world today, spurring copycat legislation and impacting the framing of data protection around the globe. As a result of the GDPR’s direct applicability to AI and its dominance across 10White Paper Rethinking Privacy in the AI Era 11 the globe, data protection and privacy concerns are largely absent from the EU’s AI Act. The GDPR contains several provisions that apply to AI systems, even though it does not specifically include the term “artificial intelligence.” Instead, Article 22 provides protections to individuals against decisions “based solely on automated processing” of personal data without human intervention, also called automated decision-making (ADM).24 It enshrines the right of individuals not to be subject to ADM where these decisions could produce an adverse legal or similarly significant effect on them. Given the widespread use of ADM as it relates to health, loan approvals, job applications, law enforcement, and other fields, the article plays a crucial role in enforcing a minimum degree of human involvement in such decision-making processes. Beyond Article 22, the GDPR also puts in place several key data protection principles that affect AI systems (see table). Most notably, the purpose limitation principle forbids the processing of personal data for purposes other than those specified at collection, and the data minimization principle restricts the collection and retention of data to that which is absolutely necessary. These principles, in theory, curb unfettered personal data collection (or data mining) that is common for data-intensive AI applications.  Despite the commonly held assumption that more data always makes for better AI, and that such constraints on data collection and use will hamper progress in AI, there is Core Data Protection Principles Data Protection Principle Summary of Relevance Data Minimization Defined in Article 5 of the GDPR as ensuring that collected data is “adequate, relevant and limited to what is necessary in relation to the purposes for which they are processed.” This principle prescribes proportionality: Data processors should not collect as much data as possible, particularly out of the context provided for collection. The intent is to prevent data collectors from engaging in indiscriminate data collection. Purpose Limitation Defined in Article 5 as data “collected for specified, explicit and legitimate purposes and not further processed in a manner that is incompatible with those purposes.” This principle emphasizes the importance of context, restricting uses of data beyond the explicit purpose given at collection. If a data processor wishes to repurpose collected data, they need to seek consent for that new use. Consent Defined in Article 7 and Recital 32 as a key requirement for data processing. Consent must be “given by a clear affirmative act establishing a freely given, specific, informed and unambiguous indication of the data subject’s agreement to the processing of personal data relating to him or her, such as by a written statement, including by electronic means, or an oral statement.” Notably, consent is required for all processing, including if data is collected for multiple purposes. Recital 42 describes the burden of proof data processors must meet to prove data subject consent, noting that “[c]onsent should not be regarded as freely given if the data subject has no genuine or free choice or is unable to refuse or withdraw consent without detriment.”  extensive research demonstrating that building ADM systems within these constraints is feasible and even desirable.25 The GDPR also enshrines transparency obligations in the form of rules about giving notice to individuals when their personal information is processed for the purpose of profiling or ADM.26 It further establishes rules granting individuals the right to access their own data and ensure the accuracy of the data processing. Finally, it introduces Data Protection Impact Assessments (DPIA)—an accountability measure that requires the collecting organization assess the potential risks and harms of data processing activities (as they pertain to the relevant organization but also potential societal-level harms) prior to conducting them.27 c. U.S. State Privacy Laws: Filling the federal privacy vacuum As of 2024, the United States still lacks a federal omnibus consumer privacy law similar to the GDPR. The closest it has come to passing consumer privacy regulation is the American Data Privacy and Protection Act (ADPPA), which was introduced in the House in 2022 but did not advance to a floor vote in that session and has yet to be reintroduced.28 Similar to the GDPR, the ADPPA would have imposed limits on the “collection, use, and sharing of personal information, requiring that such a process be “necessary and proportionate.” It would acknowledge the connection between information privacy and civil rights, strengthening relevant civil rights laws and essentially enacting the privacy section of the Biden administration’s subsequent “Blueprint for an AI Bill of Rights.”29 ADPPA was the result of lengthy bipartisan negotiations and future privacy legislation is likely to hew closely to the original 2022 bill. White Paper Rethinking Privacy in the AI Era [The purpose limitation and data minimization] principles, in theory, curb unfettered personal data collection (or data mining) that is common for data-intensive AI applications. In the absence of consumer-specific federal legislation, several sectoral laws have created a patchwork of privacy protections over the decades, such as the Family Educational Rights and Privacy Act (FERPA), the Children’s Online Privacy Protection Act (COPPA), the Health Insurance Portability and Accountability Act (HIPAA), and even the Video Privacy Protection Act (VPPA), to name a few. In this splintered landscape, U.S. states have been passing their own consumer privacy laws. As of 2023, 12 states have passed consumer privacy regulations, though California’s Consumer Privacy Act (CCPA) remains the most far-reaching.30 For that reason, we will focus on the CCPA for discussion purposes. Sometimes dubbed California’s version of the GDPR, the CCPA—together with its 2022 update, the California Privacy Rights Act (CPRA)—is arguably the most significant state-level effort so far to enact both stringent and broad consumer privacy protections.31 While some scholars have argued that the CCPA consciously creates a fundamentally different data privacy regime for California than the GDPR, it nevertheless marks a landmark shift in the U.S. privacy regulation debate.32 12White Paper Rethinking Privacy in the AI Era 13 The initial version of the CCPA created rights of data access, deletion, and portability, as well as a right to opt out of sales of personal data for two-year cycles, and a purpose limitation provision. Businesses are obliged to provide notice of the types of data they collect, to obtain opt-in consent for data collection from children ages 13 to 16, and to abide by purpose limitations when collecting and using or reusing data, which must be consistent with individuals’ general expectations and the purpose specified upon collection. The subsequent CPRA, passed as a ballot proposition (Proposition 24), amends the CCPA to add a data minimization prong as well as a right to correct personal data, a right to opt out of processing categories of sensitive personal data, and—similar to the GDPR—a right to opt out of some forms of ADM (those with significant effects, such as on housing and employment), which in draft regulations has been interpreted by California’s privacy regulator to include AI systems.33 Businesses must conduct privacy risk assessments and cybersecurity audits, offer alternatives for accessing services for those who opt out, and cannot discriminate against consumers for exercising these rights. A notable difference between California’s privacy regime and other states is that California remains the only state to have created an enforcement agency (the California Privacy Protection Agency, or CPPA) with rulemaking authority, rather than delegating this function to the state’s attorney general’s office, as many such laws do. In practice, this may mean that the CPPA has more in-house expertise than most state attorneys general and latitude to both engage in proactive enforcement via published guidance and tackle complex and emergent issues at the intersection of AI and personal data. Beyond the EU and United States: Data Protection in China In 2021, China’s legislature followed the EU’s example by promulgating a comprehensive and stringent data privacy law. Heavily inspired by the GDPR, China’s Personal Information Protection Law (PIPL) was designed to give Chinese citizens control over their personal and sensitive data by delineating who can access, process, and share their information.34 As such, it incorporates many elements of the FIPs, including data collection limitations, purpose specification requirements, and use limitations. Despite commonly being referred to as a privacy law, the PIPL never directly mentions privacy but instead focuses on curbing the abuse and mishandling of personal information—theoretically by both corporate and state actors, though practically the state’s ability to surveil its citizens remains unchecked.35 Like the GDPR and the CCPA, the law contains explicit provisions banning automated decision-making that enables differential treatment of consumers, including price discrimination. More broadly, it introduces limits on what was largely unfettered data collection by data-hungry AI companies, requiring informed consent for all kinds of data-processing activities and granting individuals key rights over their data, including the right to amend, delete, and request copies of information collected about them. Since the PIPL predominantly acts as a framework law that sets out broad principles and requirements, it was followed by a string of more granular implementing regulations, which have been directly impacting White Paper Rethinking Privacy in the AI Era Beyond the EU and United States: Data Protection in China (cont’d) AI companies, particularly those with facial recognition products.36 However, the true impact of the PIPL on China’s AI ecosystem remains hard to assess given the government’s tendency to use it as a political tool. For example, in 2022 when China’s ride-hailing giant Didi was fined by the government following a comprehensive cybersecurity review, the regulatory decision cited the PIPL and Didi’s illegal collection of data, including facial recognition data.37 However, the unprecedented size of the fine and opaque application of a variety of laws and regulations may point to the PIPL being used as a tool to control the country’s tech giants.38 d. Predictive AI vs. Generative AI: An inflection point for data protection regulation Until generative AI systems broke through the public and policymaker consciousness in late 2022, discussions about AI regulation were focused on predictive AI systems that use data to classify, sort, and predict outcomes. Within the scope of predictive AI, concerns focused primarily on the outputs produced by these systems, with less focus on the data used to train them. Both policy discussions and proposed regulation for AI were primarily concerned with algorithmic audits39 and impact assessments,40 transparency and explainability,41 and enforcing civil rights42 as a means of ensuring decisional outputs were fair and unbiased.43 To the extent that privacy played a role in these discussions, concerns were typically related to the growing awareness of our main argument in this paper—that existing privacy laws such as the GDPR would impact aspects of AI development and that passing AI regulation without comprehensive privacy legislation, as would currently be the case in the United States, would be a job half-finished.44 It is not an overstatement to say that generative AI substantially shifted the terms of the debate. Awe over the capabilities of image generators such as DALL-E or Midjourney and LLMs such as ChatGPT simultaneously raised questions about how these systems were built and what data was used to power them. As it became more widely understood that generative systems are built predominantly on data scraped from across the internet, concerns mounted about exactly what data—and whose data—was powering these systems.45 These weren’t novel concerns. Facial recognition software company Clearview AI had already raised the ire of privacy and civil liberties advocates, as well as European policymakers, for their aggressive acquisition of facial images to power their predictive criminal suspect identification app. Clearview built their software by scraping image data from across the internet, including from online services that explicitly prohibit such scraping. But given Clearview’s niche product (available only to law enforcement organizations) and targeted impact (used to identify criminal suspects), their data use wasn’t widely discussed, despite extensive reporting on the company by Kashmir Hill of The New York Times.46 Clearview has virtually been shut out of the EU marketplace after its data-gathering practices were found to be in gross violation of the GDPR. 47 In the United States, a 2020 lawsuit by the American Civil Liberties Union 14leveraging the state of Illinois’ Biometric Information Privacy Act resulted in a settlement that prohibits the company from making its products available to individuals and companies across the country, as well as also prohibiting use of its products by law enforcement agencies in Illinois.48 Meanwhile, as generative AI systems gained greater exposure, privacy regulators around the world scrambled to understand the impacts of these systems on the public and whether they violated existing laws.49 The G7 data protection authorities went so far as to issue a group statement summarizing their concerns— specifically calling out the legal authority generative systems may have for processing personal information, especially related to children; the potential for generative systems to be used for attacks to extract personal information; and the need to produce compliance documentation about the life cycle of the data used to develop and train their models. The statement also called for “privacy by design,” the practice of taking privacy into account throughout all stages of system development, while reiterating the Italy Scrutinizes ChatGPT’s Data Practices White Paper Rethinking Privacy in the AI Era need for developers to respect data protection rights and the data minimization principle.50 The Italian data protection authority went so far as to ban ChatGPT until OpenAI, its creator, put specific practices in place (see below). The fact that many generative systems are built at least in part on scraped data raises questions about whether and under what contexts data-scraping practices can be compliant with the GDPR, particularly when personally identifiable data is scraped and included in training data, even if that data is publicly available. In particular, it may place consent and legitimate interest at odds, as companies like Clearview argue (albeit unsuccessfully in this instance) that they do not need consent for publicly accessible data.51 Generative systems raise other crucial questions about training data, such as the extent to which procedural data rights will apply to them, if individuals can request to delete their data from training datasets or object to this form of processing, and whether any of this will depend on the context of use of the generative application in making these determinations. On March 20, 2023, the Italian Data Protection Authority (the Garante) received a report that OpenAI—the company that developed GPT-4, the AI model which is the basis for ChatGPT— experienced a breach of user data. The Garante swiftly launched an investigation that found OpenAI was collecting user-generated data to train its AI model, including “users’ conversations and information on payments by subscribers to the service.”56 It deemed the collection of this data to train ChatGPT’s language model unlawful under the GDPR. On March 31, 2023, the Garante demanded that OpenAI block Italian users from having access to ChatGPT. It further required OpenAI to disclose how it utilizes user data to train its AI model, to address concerns that ChatGPT produced inaccurate information about individuals, and to create an age verification mechanism within a month—or risk being fined 20 million euros or 4% of the company’s annual turnover.57 15Italy Scrutinizes ChatGPT’s Data Practices (cont’d) White Paper Rethinking Privacy in the AI Era Throughout April, OpenAI implemented changes to meet the Garante’s demands, including a new information notice describing how personal data is used to train its AI model, as well as a new, ad-hoc form that allows users to opt out from having their data processed to train the algorithms. They also added an age verification system and gave users the ability to erase personal information they deem inaccurate. However, OpenAI stated that “it is technically impossible, as of now, to rectify inaccuracies.”58 The Garante accepted OpenAI’s changes and allowed Italians to access the chatbot again. Yet the regulator continued its investigations into the developer’s data practices, concluding on January 29, 2024, that ChatGPT is in breach of the GDPR and giving OpenAI 30 days to respond with a defense against the alleged breaches.59 In the United States, discussions about the permission needed for data used to build generative AI have tended to shift toward copyright given that, in the absence of a federal consumer privacy law, copyright has offered the clearest path for content creators to demand that companies remove their data from training datasets.52 This approach yields mixed results, given the challenge of reverse engineering the existence of a particular item of content in a system’s training data absent any transparency obligations by the companies to share how and with what they trained their models. It is also a poor approach for resolving privacy issues other than those that may implicate copyrightable content. In July 2023, the Federal Trade Commission (FTC) issued a civil investigative demand to Open AI with detailed requests concerning their training data.53 This highly specific focus on obtaining information about a company’s training data is not without precedent; the FTC has settled multiple investigations with companies that used AI in their product offerings, demanding that the companies delete their model and the associated data because the data used to train it was improperly acquired.54 Lina Khan, chair of the FTC, argued in a New York Times op-ed that “exploitative collection or use of personal data” falls within the agency’s authority to prohibit “unfair or deceptive trade practices.”55 These events demonstrate that both EU and U.S. regulators have some flexibility and regulatory tools at their disposal to adapt enforcement to changes in technology. Nonetheless, relying only on existing legislation, especially in the United States, is akin to bringing a knife to a gunfight. While the GDPR is settled law, as of early 2024 the CCPA remains a work in progress that is unlikely to be finalized until later in the year. As we discuss in the next chapter, incorporating automated decision-making into these regulations provides the necessary latitude for regulators to include AI in their oversight of algorithmic systems, and to potentially broaden their scope to focus on AI-specific issues, such as training data", "metadata": {"original_filename": "item180_US_Rethinking Privacy  in the AI Era.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:51:55.396778", "updated_at": "2025-08-28T21:51:55.396778", "word_count": 31319}