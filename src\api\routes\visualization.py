from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional, Dict, Any
from datetime import datetime
import logging

from src.models.schemas import (
    VisualizationData, ChartData, SummaryMetrics, 
    TaskType, AnalysisResult, AnalysisResponse
)
from src.services.visualization_service import VisualizationService

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Visualization"])


# 依赖注入
async def get_visualization_service() -> VisualizationService:
    """获取可视化服务"""
    return VisualizationService()


@router.get("/data/{doc_id}", response_model=VisualizationData)
async def get_visualization_data(
    doc_id: str,
    task_types: Optional[List[TaskType]] = Query(None, description="要可视化的任务类型"),
    service: VisualizationService = Depends(get_visualization_service)
):
    """获取文档的可视化数据"""
    try:
        viz_data = await service.generate_visualization_data(doc_id, task_types)
        if not viz_data:
            raise HTTPException(status_code=404, detail="未找到文档或分析结果")
        return viz_data
    except Exception as e:
        logger.error(f"获取可视化数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取可视化数据失败")


@router.get("/charts/{doc_id}")
async def get_document_charts(
    doc_id: str,
    task_types: Optional[List[TaskType]] = Query(None, description="要可视化的任务类型"),
    service: VisualizationService = Depends(get_visualization_service)
):
    """获取文档的图表数据"""
    try:
        charts = await service.generate_charts(doc_id, task_types)
        return {"doc_id": doc_id, "charts": charts, "generated_at": datetime.now()}
    except Exception as e:
        logger.error(f"获取图表数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取图表数据失败")


@router.get("/summary/{doc_id}", response_model=SummaryMetrics)
async def get_document_summary(
    doc_id: str,
    service: VisualizationService = Depends(get_visualization_service)
):
    """获取文档的分析摘要"""
    try:
        summary = await service.generate_summary_metrics(doc_id)
        if not summary:
            raise HTTPException(status_code=404, detail="未找到文档或分析结果")
        return summary
    except Exception as e:
        logger.error(f"获取摘要数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取摘要数据失败")


@router.get("/compare/{doc_id1}/{doc_id2}")
async def compare_documents(
    doc_id1: str,
    doc_id2: str,
    service: VisualizationService = Depends(get_visualization_service)
):
    """比较两个文档的分析结果"""
    try:
        comparison = await service.compare_documents(doc_id1, doc_id2)
        return {
            "doc_id1": doc_id1,
            "doc_id2": doc_id2,
            "comparison": comparison,
            "generated_at": datetime.now()
        }
    except Exception as e:
        logger.error(f"文档比较失败: {e}")
        raise HTTPException(status_code=500, detail="文档比较失败")


@router.get("/dashboard")
async def get_dashboard_data(
    limit: int = Query(10, description="返回的文档数量限制"),
    service: VisualizationService = Depends(get_visualization_service)
):
    """获取仪表板数据"""
    try:
        dashboard_data = await service.get_dashboard_data(limit)
        return dashboard_data
    except Exception as e:
        logger.error(f"获取仪表板数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取仪表板数据失败")


@router.post("/export/{doc_id}")
async def export_visualization(
    doc_id: str,
    format: str = Query("json", description="导出格式: json, csv"),
    service: VisualizationService = Depends(get_visualization_service)
):
    """导出可视化数据"""
    try:
        export_data = await service.export_visualization_data(doc_id, format)
        return export_data
    except Exception as e:
        logger.error(f"导出可视化数据失败: {e}")
        raise HTTPException(status_code=500, detail="导出可视化数据失败")