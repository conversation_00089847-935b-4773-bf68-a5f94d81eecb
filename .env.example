# 智谱AI API配置
ZHIPUAI_API_KEY=your_zhipuai_api_key_here

# 数据库配置
DATABASE_URL=sqlite:///./document_analysis.db

# 应用配置
APP_NAME=Document Analysis System
APP_VERSION=1.0.0
DEBUG=True

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# API配置
MAX_WORKERS=4
REQUEST_TIMEOUT=30
MAX_RETRIES=3

# 文件上传配置
MAX_FILE_SIZE=10MB
UPLOAD_DIR=uploads

# 缓存配置
CACHE_TTL=3600
REDIS_URL=redis://localhost:6379/0

# 安全配置
SECRET_KEY=your_strong_secret_key_here_minimum_32_characters
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8000", "http://127.0.0.1:8000"]

# API密钥配置
API_KEY_PREFIX=sk-
API_KEY_LENGTH=32
API_KEY_EXPIRE_DAYS=365

# 速率限制配置
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_REQUESTS_PER_HOUR=1000
RATE_LIMIT_REQUESTS_PER_DAY=10000

# 密码策略
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SPECIAL=true

# 监控配置
ENABLE_METRICS=True
METRICS_PORT=9090