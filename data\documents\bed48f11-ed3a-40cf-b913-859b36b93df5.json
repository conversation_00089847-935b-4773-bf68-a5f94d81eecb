{"doc_id": "bed48f11-ed3a-40cf-b913-859b36b93df5", "title": "item266_US_Can an AI be properly considered an inventor", "text": "Can an AI be properly considered an inventor?\r\n\r\nSeveral years ago, I wrote a piece titled “How AI and copyright would work.”\r\n\r\nAs I looked over the state of several interesting questions at the intersection of artificial intelligence and copyright at that time, my bottom line was pretty simple: If the copyright laws and regulations required a work to contain the expression of a human person, then that body of law (especially the text of the statutes, e.g., Title 17 in the U.S., but also the common law or civil law history of cases) did not yet countenance the assertedly “independent” creations of an AI, of which there are many types.\r\n\r\nThat is, at least in the U.S., essentially still the case. However, there’s been a significant volume of water that’s passed under the policy and lawmaking bridge since then, so I wanted to revisit the question.\r\n\r\nFirst, let’s back up a little.\r\n\r\nI have to admit that my reasoning in 2018 was narrow rather than broad. In this way, it was also based on that of the U.S. Copyright Office or USCO (see, for example, the Compendium of U.S. Copyright Office Practices, Third Edition (2021), p. 384, which is similar on this point to the earlier edition I used at the time), and so I focused on the “expression” requirement for copyrightability.\r\n\r\nThe work – and let’s note that it doesn’t have to be considered aesthetically “good” or have required a lot of skill – must simply be original (meaning that it was independently created and has at least a “modicum” of creativity) and an expression of some sort. This is why an unadorned set of directions, such as stripped-down basic instructions in a baking recipe, does not qualify, but “Mastering the Art of French Cooking” by <PERSON> <PERSON> (a book that contains much more expressive text, which served to make it a bestseller) does.\r\n\r\nWhile I am fully at peace with the personhood of (fictional) Commander Data of “Star Trek” in the 24th century, in our world devices by themselves do not and cannot express anything (even if your copy of <PERSON><PERSON> or <PERSON>i appears to). I can’t say that I know how long that will continue to be the case, but even “Star Trek” suggests that it may be at least 350 years.\r\n\r\nIn 2020, the USCO and the World Intellectual Property Organization (WIPO) hosted a “symposium that took an in-depth look at how the creative community currently is using artificial intelligence (AI) to create original works.” And then, in 2021, the USCO and the U.S. Patent and Trademark Office (USPTO) held a second symposium looking at the potential for change in the treatment of machine-created works.\r\n\r\n\r\nThe legal and regulatory environment for these tools remain at the forefront of copyright policy, and we observe that government agencies entrusted with administering these issues are thinking about them and soliciting views from the public about them. More about that in a moment.\r\n\r\nBeginning in 2019, perhaps jumping the gun a bit on the USCO/USPTO/WIPO’s “let’s think about it” approach, something truly interesting happened in this domain.\r\n\r\nStephen Thaler, owner and developer of a patent-writing program known as DABUS, submitted patent applications in several countries. As a result of these applications, the government of South Africa recognized DABUS as the inventor on a patent.\r\n\r\nThaler, an advocate of recognizing these devices as inventors, clearly believes the time has come, stating, “It’s been more of a philosophical battle, convincing humanity that my creative neural architectures are compelling models of cognition, creativity, sentience, and consciousness. … The recently established fact that DABUS has created patent-worthy inventions is further evidence that the system ‘walks and talks’ just like a conscious human brain.”\r\n\r\n(We should bear in mind, however, that an “author” in copyright is not an identical legal construction with that of an “inventor” in the domain of patents, but they are closely related concepts.) We also need to consider that the South African patent system does not involve an examination of the substance of an application, but unlike in a lot of countries leaves both first consideration and final resolution of patent validity to the courts, and so the patent grant was in some sense automatic and not policy-driven.)\r\n\r\nImportantly, the U.S., the U.K., and the European Patent Offices (all of which do preliminary consideration of patentability) rejected this same patent application on the basis of its ineligibility. Australia, in effect, seconded the South African motion, at least to the degree that the application met its technical requirements “to the letter of the law” of Australian patent statutes.\r\n\r\nWe may point out that while the patent grant under South African law is narrow and technical, the question is no longer merely theoretical and we can perceive a threshold as having been crossed. For the purposes of this essay, I think South Africa and Australia have called the question: “Can AIs be inventors, too? If so, should they be?” The question of whether AI ought to produce “authorship” for purposes of copyright cannot be far behind.\r\n\r\nMost recently, over in the U.K., they are in the midst of an in-depth consultation of the whole issue. And I think the issues they are studying are the right ones:\r\n\r\nCopyright protection for computer-generated works without a human author. These currently may be protected in the U.K. for 50 years. But should they be protected at all? And, if so, how should they be protected?\r\nLicensing or exceptions to copyright for text and data mining. This is often significant in AI use and development.\r\nIs there a case for protecting AI-devised inventions by IP rights? If yes, how should they be protected?\r\nAs I wrote earlier, “In my view, a self-aware, autonomous AI would be the prerequisite for its works to be protectable by copyright. At that time, such a revolution in technology might bring along with it a much greater revolution in society, with the law, including copyright law, changing, as well.”\r\n\r\nI still think we are at the very beginnings of what looks to be a long period of change in the interplay of technology and the law in this domain, but it is equally clear to me that the play has started to move.", "metadata": {"original_filename": "item266_US_Can an AI be properly considered an inventor.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:35:14.183139", "updated_at": "2025-08-28T21:35:14.183139", "word_count": 6248}