#!/usr/bin/env python3
"""
Test script to verify the analysis results endpoint fix
"""
import asyncio
import sys
import os
from datetime import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.services.analysis_service import AnalysisService

async def test_analysis_results():
    """Test the analysis results service"""
    try:
        from src.services.zhipu_client import ZhipuAIClient
        zhipu_client = ZhipuAIClient()
        service = AnalysisService(zhipu_client)
        doc_id = "doc_1755538662379"
        
        # Get results from service
        results = await service.get_results(doc_id)
        
        if not results:
            print(f"No results found for document {doc_id}")
            return
        
        # Create the response format
        response = {
            "doc_id": doc_id,
            "results": results,
            "created_at": datetime.now().isoformat(),
            "success": True
        }
        
        print("SUCCESS: Analysis results endpoint fix works!")
        print(f"Document ID: {response['doc_id']}")
        print(f"Created at: {response['created_at']}")
        print(f"Success: {response['success']}")
        print(f"Available task types: {list(response['results'].keys())}")
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_analysis_results())