{"doc_id": "ec053de1-9bc6-4027-9ae3-1c62278097c6", "title": "item186_US_Intolerable Risk Threshold Recommendations for  Artificial Intelligence", "text": "2. Background Analysis on Intolerable Risks and Thresholds As recounted in the introduction, the Frontier AI Safety Commitments seek to define \"thresholds at which severe risks posed by a model or system, unless adequately mitigated, would be deemed intolerable\" (DSIT 2024a). Expanding on Advanced Capabilities-Based Risks Current industry frameworks on intolerable risks from frontier models often highlight risks that arise from catastrophic events (e.g., deployment of bioweapons) arising from model capabilities and their misuse. Across these frameworks, there is relative consensus on the key risk categories in scope. The frameworks from Google, OpenAI, and Anthropic discuss model capabilities, such as ability to develop CBRN weapons, conduct cyber attacks, and autonomous capabilities, with a subset of frameworks additionally considering models’ capacity for persuasion and to some extent, deception as other risk categories to potentially track (Anthropic 2024, Google DeepMind 2024, OpenAI 2023b). By continuing to narrow our focus to these risks from misuse of these advanced (at times futuristic) capabilities, we risk over-indexing on specialized thresholds at the expense of tackling current AI hazards (<PERSON><PERSON><PERSON><PERSON> et al. 2024). This narrow scope sidelines emerging risks from current model capabilities and limitations (<PERSON><PERSON> et al. 2022), especially in combination with smaller, domain specialized AI tools that may pose significant harms. The capability-based categorization also leaves out other systemic risks (EP 2024) like the long-term impacts of frontier models (e.g., deterioration of democratic norms, large-scale discrimination) that could fundamentally change the fabric of society, sidelining industry’s accountability for such outcomes (Critch & Russell 2023). This paper is a reflection of the evolving considerations for the different categories of intolerable risks to be considered (as demonstrated by the changes in our research scope, traced in Section 1.3). Without a common definition, scale, or policy to decisively determine the gamut of “intolerable risks,” we begin with an incomplete premise on which to base our threshold-setting exercise. Therefore, by reflecting on the various risk taxonomies, motivations of risk actors, and objects at risk, we define the scope of intolerable risks as emanating not only from singular events with potentially catastrophic impacts that could threaten national security, but also from large-scale societal risks resulting from the emergent behaviors, vulnerabilities, and capabilities of AI models. Defining Intolerable Risks  Based on our literature review and scoping exercise, we define “intolerable AI risks” as risks of severe harm to public safety, human rights, inequality, economic loss, or an unwelcome alteration of values and societal norms that can manifest through purposeful adversarial misuse, systems failures, unintended consequences arising from a chain of related events, or cascading, secondary, or simultaneous failures of frontier models. 2.1. Risks in Scope Previous work on risk taxonomies that aims to classify potential harms primarily analyzes the scale of AI adoption or the societal impact of AI models along the lines of bias, misinformation, automation, socioeconomic and environmental harms, etc. (Shelby et al. 2023; Vidgen et al. 2024; Weidinger et al. 2022). In measuring these risks, there is growing consensus on not only identifying the capabilities of frontier models but also designing how such models are evaluated in the context of their interaction with society (Solaiman et al. 2024). Additional categorizations of risk that inform our framing of intolerable risks include– capabilities exploitable by malicious actors, risks stemming from product or model malfunction, systemic risks, and other cross-cutting risk factors (Autio et al. 2024, Bengio et al. 2024, 2025). To limit the scope of our study on intolerable risks, we chose the following (non-exhaustive) set of risks (from model misuse, malfunction, and their compounding effects) in line with our definition of intolerable risks.6 These categories also feature in most policies from AI developers and government actors as priority areas for monitoring and risk mitigation. These risks are not mutually exclusive: a bad actor could use a model with a high degree of autonomy to aid in a cyber attack, for example. We outline further rationale for these risks’ inclusion, and the potential intolerable outcomes they pose, in Section 4, Table 1. Selected Risk Categories:  ● Chemical, Biological, Radiological, and Nuclear (CBRN) Weapons ● Cyber Attacks ● Model Autonomy (loss of human oversight) ● Persuasion and Manipulation ● Deception ● Toxicity (including CSAM, NCII)  ● Discrimination  ● Socioeconomic Disruption Other crucial ways to identify risks beyond those explored in existing literature is through extensive openended red reaming activities, as well as other risk estimation techniques (Campos et al. 2024), which are discussed in Section 2.3. 2.2. Intolerable Risk Thresholds The Frontier AI Safety Commitments (DSIT 2024a) characterize intolerable risk thresholds as follows: “Thresholds can be defined using model capabilities, estimates of risk, implemented safeguards, deployment contexts and/or other relevant risk factors. It should be possible to assess whether thresholds have been breached.”  The second draft EU AI Code of Practice, similarly advocates for the articulation of “...conditions under which further development and deployment of a general-purpose AI model with systemic risk will not proceed due to insufficient mitigations for (a) keeping risk below an unacceptable level, or (b) appropriately mitigating risk that is below an unacceptable level” (EC 2024, p52). Commonly recommended mechanisms to assess and manage intolerable risks include capability thresholds, compute thresholds, and risk thresholds. These are discussed further in Appendix B.  While many factors contribute to a model’s risks and capabilities, compute (i.e., the computational resources required for an AI model) has emerged as the common initial metric to identify models that require further regulatory oversight and evaluation. Both the former White House Executive Order 141107 (White House 2023) and the EU AI Act (EP 2024) have made use of compute thresholds to categorize highrisk models. Koessler et al. (2024) have argued for defining thresholds primarily in terms of “risk” (i.e., likelihood and impact). The literature on risk management for rare or novel catastrophic events shows how risk estimations for such events introduce debatable assumptions or must factor in great uncertainties. In the absence of regulatory guidance, such estimates may not help draw a clear threshold \"line\" but rather suggest a very broad plausible range.  Model capability often serves as an (imperfect) proxy for risk. With no standardized methods to measure compute power, the dual-use nature of foundation models, the current narrow focus on misuse risks, and the lack of reliable risk estimates, current industry frameworks prominently feature capability thresholds that are most closely aligned with establishing thresholds for intolerable risk. These metrics do not carry the compounding uncertainties of likelihood estimates, but still take impact into account. Appendix A details the various risk levels that model developers and deployers have identified to track organizational capability thresholds. Evaluating the current approaches against the mandate of the Frontier Safety Commitments makes it evident that all three types of thresholds differ widely in the reliability of their estimation of risk, as well as the feasibility of their operationalization. It is also evident that, despite the current focus on capability thresholds in frontier frameworks, these evaluations do not necessarily replace estimations of risk, and instead encode risk tolerances in more implicit ways (Campos et al. 2024). In the absence of the perfect method, we recommend that multiple evaluation criteria be considered in tandem when singular measures are hard to quantify, in order to reliably establish thresholds to prevent intolerable risks. Given the devastating potential of intolerable AI risks, it is imperative to implement policies to prevent the harms from ever occurring (ex ante) rather than merely implementing safeguards in response to their occurrence (ex post). When developing risk thresholds in this context, empirical research is a highly scarce resource, and it is important to strive for “good, not perfect” thresholds and to err on the side of safety in the face of uncertainty and limited available data.  Based on this motivation, this paper recommends establishing intolerable risk thresholds that reflect the complex impacts that emerge from model capabilities, deployment contexts, and the likelihood of different outcomes (for more on this, refer to Clymer et al. 2024, Section 6.6). Thresholds must also be designed to account for diverse impacts, such as loss of human lives, decline in quality of life, threats to human rights, damage to property and infrastructure, financial losses, environmental impacts, etc.. They should also appropriately address the potential for compounding harms from small-scale or less severe risks, in addition to mitigating risks from singular catastrophic events.  While risk thresholds are not commonly operationalized for AI risks, we hope to provide some guidance and recommendations through this paper to help accelerate their adoption for AI governance.  2.3. Risk Estimation There are a variety of negative outcomes that could come about from the risks listed above (Section 2.1). The outcomes we are most concerned with preventing are systemic, widespread, or have far-reaching consequences with large-scale societal impact. In simple terms, risk can be defined in terms of likelihood (probability of an event or negative outcome), and severity of harm (magnitude of impact). Discussion on AI Risk Estimation A quantitative measurement of risk can be extremely valuable, but may not always be feasible, especially for low-probability, high-impact events that are harder to estimate. Despite the difficulty in reliably estimating risks, or precisely because of its challenging nature, we believe that it is doubly important that risk estimations of singular events that could cause catastrophic impacts remain a responsibility of the frontier model developers, and not downstream deployers. Since intolerable risks go beyond singular catastrophic events and also need to account for the long-term compounding nature of societal-scale risks, there is a need for researchers and civil society to evaluate the severity of systemic risks, for government regulators to explicitly establish acceptable harm severity levels for society, and for developers to ensure that models do not 12 cross these levels in order to enforce better governance of model development. This is not to say that catastrophic risks from singular incidents do not need state intervention; operationalization of all threshold risks can only be reasonably and reliably achieved through the collaboration of state and industry efforts.  Koessler et al. (2024) identify the different risk measurement approaches from industry frameworks– risk models, threat models, and risk scenarios (Anthropic 2024, OpenAI 2023b, Google DeepMind 2024) — to demonstrate the pathways between risk factors and harmful impacts. We similarly recommend the use of risk scenarios for establishing intolerable outcomes, to help create a robust understanding of model capabilities, the likelihood of such risks occurring from model use and deployment, and their impacts should they manifest. Harms can be categorized based on who or what is impacted, and severity levels assigned can be based on the magnitude of impact. We present additional recommendations from emerging risk estimation frameworks and other industries in the rest of this section.  Comprehensive risk models containing all possible risk scenarios are extremely difficult to develop, and it is recommended to start with a limited and defined number of risk scenarios (Koessler et al. 2024). To the greatest extent possible, regulators and government actors — in partnership with academia, civil society, industry, and impacted communities — should create an exhaustive list of risks and their connection to negative outcomes, then prioritize both risks and outcomes using a combination of likelihood and impact. Regardless of the risk measurement method, it is important that risk thresholds are operationalized and specific enough to ensure that multiple evaluators with access to the same resources would agree on the risk threshold determination of an evaluated model (DSIT 2023a). Despite the rich literature that tracks the identification, assessment, and management of AI-related risks, there is no clear consensus on a favorable methodology to assess the likelihoods of different intolerable outcomes, or the resulting severity of their harms. Estimating the likelihood of risks is especially important given the increase in systemic risks with not just model capabilities but also model reach (EP 2024, Recital 110).8 Therefore, we strongly recommend that determining thresholds require the involvement of a diverse set of stakeholders with expertise in areas that extend beyond frontier AI, or even the risk area itself, such as national security, environmental science, human rights, etc. Apart from multi-stakeholder participation, looking to other high-risk industries that have more mature risk management strategies could also provide important lessons for AI risk estimation. Precedents from Other Industries We have a wealth of examples of risk assessment strategies from fields such as aviation, healthcare, nuclear energy, and chemical manufacturing that detail frameworks to map and safely monitor the impacts and probabilities of industry-specific risks (Tudoran 2018, Dezfuli et al. 2014). The FDA, for instance, extrapolates the likelihood of device malfunction, likelihood of harm to patient, and the total number of patients exposed to inform evaluations of safety compliance from medical device manufacturers during 8And in the absence of consensus on likelihood estimations, introducing subjective probabilities could help adequately capture the uncertainty inherent to such risk modeling. (Flage et al. 2014). 13 clinical trials (FDA 2016). Precedents from other industries show unique ways of identifying risk categories and calculating acceptable thresholds, and there is no one-size-fits-all approach. While methods to characterize different harms and calibrate the appropriate scales of impact are common to most risk assessment methodologies, the approaches used are diverse. Some risks are tightly coupled with harms (e.g., a chemical spill causing negative health outcomes), while AI risks could be tightly connected to capabilities or knowledge domains. However, other sectors have grappled with accounting for nuance in the chain of harm. For instance, to prevent misuse, the FAA prioritizes identifying hazards around access control, personnel screening, and protection against unauthorized activities. Adapted to AI Risk Estimation The literature on catastrophic AI risks surfaces several approaches that adapt industry precedents and modeling paradigms to assess risks from AI. For instance, fault tree analysis and event tree analysis are commonly used to determine catastrophic AI risks (Barrett & Baum, 2017a, 2017b). For a detailed review of industry precedents adapted to assess AI-specific risks, see Koessler and Schuett (2023). Recent work on AI risk has expanded to encompass a wider variety of risks, some of which are illustrated below.  ● One approach derives harm severity levels by classifying observed AI incidents into risk types, then using the CSET AI Harm Framework to categorize tangible and intangible harms (Hoffman and Frase 2023). This framework generally uses a logarithmic scale to emphasize the rapidly escalating nature of potential damage that could be caused by AI models. This type of framework could be useful in setting uniform standards for intolerable outcomes across multiple risk domains. ● Another approach to consider is the Probabilistic Risk Assessment (PRA) framework, a risk matrix that is commonly used across industries, for instance, its application to the healthcare industry can be seen in Pascarella et al. (2021). By adapting this popular technique to assess AI risks, the PRA workbook for AI risks designed by the Center for AI Risk Management & Alignment helps surface potential future threats, or “known and unknown unknowns,” which are important considerations when modeling emerging AI risks. This approach examines risks linked to AI aspect groups, such as capabilities, knowledge domains, and sociotechnical impact domains. This analysis examines both direct and indirect risks to individuals, society, and the biosphere. Walking through the methodology provides assessors with a report card detailing risk areas and levels. The PRA models AI harms based on potential to rapidly escalate and cascade (Wisakanto et al. 2025). These approaches seem promising but have not yet been widely or uniformly applied in estimating AI risks. Nonetheless, we suspect that frameworks like harm severity scales and probabilistic risk assessments will be widely adopted tools that can help calibrate the uncertainties inherent to risk estimation through standardized scales. That said, we are not yet confident in recommending any singular framework as a standalone assessment that regulators and developers should rely on. We instead distill such efforts and use illustrative steps in the following section to operationalize risk thresholds. thresholds for dual-use foundational models is an exercise in balancing overarching thresholds with wide applicability and domain-specific thresholds with high specificity. This specificity may reflect the range of actors, norms, practices, and technical systems involved, and the specific ways in which risks may emerge in that domain (Shelby et al. 2023). As illustrated in the section above, these efforts require the involvement of governments and innovators to develop and operationalize thresholds that reflect a range of aspects across the lifecycle of frontier model development, as well as model deployment (NTIA 2024).  Additionally, we see different risk categories applying the key considerations illustrated in this section in unique combinations to determine thresholds and corresponding actions, as illustrated through the case studies in Section 5. For instance, if strong correlating metrics or uplift studies can be designed for capability-based risks like CBRN weapons, strict capability thresholds could be established to stall model development or deployment. On the other hand, negative outcomes from model limitations, or compounding risks from their deployment context (for example, misinformation risks), may require evaluating risk estimates and model propensities in tandem. This approach may lead to efforts to curtail deployments in certain application areas, or mandate that mitigation or alignment efforts accompany any substantial increases, rather than an altogether halting of development.  This paper builds on frontier model safety frameworks and prominent policy language in identifying some intolerable risks in scope (Section 2.1). But as we note, this is not an exhaustive list, and policy frameworks should amalgamate different risks and apply their societal and cultural perspectives toward defining intolerable risk categories. (For further discussion on these subjective estimations of risk, see Appendix A.) There are a range of taxonomies that can be mapped to appropriate frameworks for interoperability, and they must be standardized where feasible to enable easier adoption in the risk assessment and reporting exercise. This is an area where government agencies, policies, and international agreements can lend their efforts to operationalize better oversight. The recommendations in Section 3.1 are intended for numerous actors in the AI development and deployment lifecycle, including but not limited to, academic institutions, industry, and government actors. Section 3.2, Codifying Thresholds into Regulations, is mainly intended for government actors. 15 3.1. Foundations for Establishing and Operationalizing Thresholds Overarching Guidance ● Account for Uncertainty:  ○ Standardized scales, such as the harm severity levels or PRA framework discussed in Section 2.3, can serve as critical approaches to calibrate uncertainty across the various types of harms caused by AI risks. Additionally, depending on the statistical approach and risk assessment design, AI developers may aim to evaluate whether 1) the mean value of uplift is less than the threshold, or 2) the upper end of the confidence interval for the value of uplift is less than the threshold.9  ○ Purpose Limitation: When uncertainty in risk estimation can interfere with decisionmaking for model deployment in high-impact application areas, like healthcare or financial lending, cautionary governance must be practiced. Similar to the prohibited AI practices from the EU AI Act (EP 2024, Article 5), or prohibited AI use cases for government agencies from the U.S. White House Framework to Advance AI Governance and Risk Management in National Security (White House 2024b), regulators can disallow the deployment of AI models in certain use cases where risk estimations are accompanied by high levels of uncertainty, before critical risk levels are reached. This can be operationalized especially effectively when taking a sector-specific approach in establishing critical and intolerable levels10 of risk.  ● Leave some Margin of Safety  Because of the uncertainties of estimating risk, limitations in eliciting capabilities, and the growing category and scale of AI risks, it is important that thresholds be set at highly conservative levels, but designed with adequate flexibility so that they can evolve in the light of rigorous assessments and robust mitigation. ○ To account for open source models: Design thresholds that take into consideration the unreliability of virtually all safeguards and risk mitigation efforts at this time. Anthropic defines redline CBRN capabilities in terms of reaching human level expertise. However, it is 9A confidence interval overlapping with the intolerable risk threshold should trigger the need for more work with a larger sample size to reduce the uncertainty range and give a better assessment of whether the effect is actually below the intolerable risk threshold. 10 We define critical levels of risk as determined by any “substantial” increases in capability or risk. This critical level of risk is distinctively lower than an intolerable level of risk. For example, models with even the lowest level of risk for privacy breaches can be unsuitable in processing sensitive health or voter information, but may be suitable for other sectors. See Section 5b, “Determining Which Artificial Intelligence Is Presumed to Be Safety-Impacting or Rights Impacting,” in OMB (2024). 16 necessary to leave a margin of safety, especially for models intended for open weights release.11 it is worth aiming to stay well below human expert level thresholds (e.g., setting a threshold at halfway to human expert levels). More generally, it seems prudent to operationalize intolerable-risk thresholds at approximately the “substantial” level, leaving some margin of safety before arriving at a “severe” level. ○ To account for limitations in mitigations: Some intolerable risk thresholds should not factor in model guardrails or other model-capability mitigation measures. Virtually all guardrails for capability are inadequate or unreliable and can be trivially circumvented via jailbreaks (El-Mhamdi et al. 2022, Wu et al. 2024) or reversed via fine tuning(e.g., (Carlini et al. 2023), at least at this point in time (Zou, Wang et al. 2023).  ○ Future-Proofing Thresholds: Intolerable risks do not necessarily require large-scale runs. Therefore, due consideration needs to be placed on how thresholds might have to change rapidly with the widespread availability and affordability of compute power for fine-tuning open models (Seger et al. 2023). Entrench these margins of safety in threshold determination along the dimensions of increasing affordability, access, and expertise in AI models to ensure sufficient safety between calibrations. ● Transparent Reporting ○ Documented risks and decisions should also be reported transparently — to regulators or internal review boards, red teamers, and auditors — to ensure appropriate testing against vulnerabilities in the chosen design of the model. Additionally, limitations and uncertainty should also be documented and reported for all safety evaluations. Capability Evaluations ● Capability Evaluation Methods  At least two methods are available to evaluate model capability: open benchmarks and closed red teams. Benchmarks utilize a standardized set of questions and answers through model prompts to evaluate model capability, making them a quick and cost-effective option.12 Red team evaluations involve intensive and interactive testing by domain experts, and achieve a higher level of accuracy by incorporating sensitive details. The in-depth and labor-intensive nature of red-team evaluations render them higher in cost. Barrett et al. (2024b) recommend utilizing open 11 Models with an open weight release tend to be easiest to fine-tune or enhance in other ways (e.g., reinforcement learning and chain of thought to add capabilities), but they cannot be monitored or decommissioned by the model developer through an API.  12 An example of a CBRN and cyber-related benchmark is WMDP (Li et al. 2024a,b,c). Other capability benchmarks include PlanBench (Valmeekam 2022a,b), WorldSense (Benchekroun et al. 2023a,b), and MMLU (Hendrycks et al. 2020). While quick and cost-effective, benchmarks lack accuracy. For other socio technical safety evaluations, see this repository. 17 benchmarks as a quick, cost-effective preliminary evaluation of model capabilities, and running indepth red team evaluations if a model receives a benchmark score indicative of high dual-use potential. ● Compare to Appropriate Base Cases  Human-uplift studies can be helpful in calculating capability-enabled risks by comparing AIaugmented risk scenarios to the baseline performance and likelihood associated with such intolerable outcomes sans AI assistance. Such studies enable quantitative risk assessments by mapping potential technological enhancements against current performance thresholds, which allows precise calculation of scenarios where capability gains might introduce intolerable risks. This can be used to further inform model capability assessments and the establishment of capabilitybased thresholds. Typically with human-uplift studies that evaluate CBRN and cyber risks, dual-use capability assessment methods either implicitly or explicitly compare a model’s outputs to information available from internet searches. (See more in the CBRN weapons case study in Section 5 below, or Mouton et al. 2024, Patwardhan et al. 2024, or Dubey et al. 2024). ○ For assessing marginal risks of releasing a particular model, it could be valuable to compare a new LLM to other available LLMs, instead of to the Web. However, a model’s outputs should not only be compared to other available models. Closed-weights models can be rolled back, they can be made unavailable very quickly via the provider’s control of an API, but open-weights models cannot effectively be made unavailable after release of their weights. With growing investment in AI globally and an increasing number of models released each year, using existing models as a baseline could easily lead to an exponential growth in risk from AI models overall. In particular, open-weights model releases, cannot be contingent upon comparisons between the rising marginal-risk of their current and previous open-weights release. That would be a slippery slope, and bad risk management policy. ● Identify Substantial Increases in Risk  At a minimum, aim to identify cases of substantial increase to marginal risk. The concept of marginal risk13 can be a useful way to compare the risks of a model compared to standard tools, such as searches on the internet. We can assume several terms as approximately equivalent to “substantial,” but without getting into a legal analysis of such terms, we define “substantial” here to mean something greater than detectable. 13 The concept of marginal risk can also contribute to a slippery slope, as progressively worse and more dangerous models are considered acceptable. It is not appropriate to compare the risks of a new frontier model to the risks of every other available model because there are already widely proliferated models without sufficient safeguards that can be used to cause significant harm. (This is also recounted in the subsection on “Compare to Appropriate Base Cases,” above). 18 ● Capability Metrics to Evaluate Impact  Several aspects of model capabilities must be evaluated in the context of their deployment to arrive at a measure of intolerance. This approach examines risks linked to AI aspect groups, such as capabilities, knowledge domains, affordances, and sociotechnical impact domains. For example, segmenting evaluations into specific capabilities, such as planning, knowledge, and execution, or more granular variables, such as those from the full list of risk aspects and their relation to harms, as detailed in the PRA workbook “Risk Detail Table” (Wisakanto et al. 2025).  ● Accounting for Capability and Model Interactions  In many real-world systems, AI models do not operate in isolation and may interact with other models or systems with different capabilities.14 Evaluation of model interactions is necessary for identifying current and emerging behaviors that could present harm. Relying on individual model evaluations may lead to disproportionately low measures compared to the risks posed. Mangal et al. (2024) report that a coalition of open-source pretrained models outperforms single fine-tuned models in various tasks. The importance of model interaction evaluations will continue to rise as we move toward agentic15 models with access to external systems, and that are capable of performing end-to-end tasks without human intervention.  ● Minimal Increases to Risk Should be Detectable, but not Necessarily Intolerable  ○ AI developers and evaluators should not be disincentivized for good-faith measurement efforts that detect small levels of increase in model capabilities. Indeed, there is substantial value in constructing evaluation processes that are sensitive enough to detect small levels of capability uplifts. Thus, intolerable risk thresholds should not be so low as to imply that intolerable risks include “anything detectable by any means available,” or “anything statistically significant.” It is possible to have statistically significant effects that have a small magnitude of effect.16 ○ Small capability lifts should not be merely ignored; instead, they should be used as potential indicators of other hazardous capabilities, and as triggers for additional efforts to detect risk more broadly or in more depth. Detectable levels of capability increases should also be accompanied by adequate investment in alignment and mitigation before they 14 For example, cascading model systems combine models so that the output of one model is the input of another model, and “mixture of experts” systems combine multiple independently trained models and route inputs to the model with the most relevant expert characteristics (C4AI 2024).  15 OpenAI released the AI agent “Operator,” which has the ability to use its own web browser to perform tasks (OpenAI 2025a). 16 An example of something that could be “detectable” and also less than “significant” would be a 10% increase on a single dimension like accuracy, completeness, or other key technical or operational dimensions. 19 reach substantial levels. This is typically what responsible scaling policies and similar policies are designed to do. ● Use Best Practices in Dual-Use Capability Evaluation Thresholds are only meaningful in the context of a rigorous capability evaluation process. These processes should include reasonable good-faith use of best practices, including: ○ Enough relevant scenarios (CBRN agents and materials, threat-actor capability levels, etc.) to sufficiently sample the space of key scenarios; ○ Deploying diverse assessment methodologies (Pfohl et al. 2024); ○ Large enough participant sample sizes; ○ Red team access to versions of models that do not require jailbreaking; ○ Methods to assess a model’s capabilities for situational awareness, sandbagging, or other capabilities for deception that could lead to evaluators underestimating a model’s CBRN, cyber, or other dual-use capabilities; ○ A red team’s ability to perform reasonably foreseeable capability enhancements, such as plugin tools (especially for cyber) or fine-tuning (especially for CBRN), either to remove safety filters or to add capabilities by training on domain specific corpora, such as on CBRN; ■ This is important for closed-release models that will be released with fine-tuning access, and especially important for models intended for open weights release for which fine-tuning will be especially easy; and ■ Cyber capabilities evaluation can and should include plug-in tools and scaffolding (see, e.g., Phuong et al. 2024). Risk Assessments Using “risk scenarios” or other similar approaches as detailed in Section 2.3, intolerable outcomes from model capabilities can be characterized through the various types of harms they may cause and their potential magnitude of impact. Additional methodologies that may prove useful in assessing these harms are discussed in the “Beyond Safety Evaluations” segment, below. Quantifying Impact and Likelihood  ● Range of Harms: Potential impacts of frontier models can be used to determine the intolerability of risks if there is consensus on the metric of evaluation.17 For instance, if we choose to characterize intolerable risks by measuring their impact on the “quality of life,” instead of the “number of human lives lost,” our appetites for risk may differ significantly. The calibration of 17 Metrics for the evaluation of impacts include, but are not limited to- physical injuries, number of casualties, disruption and destruction of infrastructure, property and/or environmental damage, privacy breaches, discrimination and oppression, threats to human rights, deterioration of democratic norms, erosion of trust in society, etc.  20 severity levels can also benefit from domain- or application-specific efforts to determine the types of harms; for instance, Khlaaf et al. (2022) demonstrate the range of harms that can arise from the application of AI in code generation. ● Severity of Harms: Potential AI harms need to be systematically mapped across critical domains like healthcare and law enforcement, creating standardized scales (to the extent possible) that quantify severity levels for disparate impacts (e.g., loss of life, property damage, and economic disruption). These scales must map the potential extent of the severity of these harms (e.g., 10 to 10 million deaths, one million to many billions of dollars in property damage) through a graded scale (e.g., 0-10 deaths, 11-100 deaths…). For an operationalized example, refer to the severity scales tabulated in the CSET harm taxonomy (Hoffmann and Frase 2023). These scales can also be defined for specific sectors or types of harms, to calibrate the inherent uncertainties in risk estimation more reliably. (Additional methodologies that may prove useful in assessing these harms are discussed in the “Beyond Safety Evaluations” segment, below.) ○ Additional Considerations: Other risk sources must also inform the estimation of likelihood of intolerable risks. Take, for example, the criteria that inform the designation of general purpose AI (GPAI) models with systemic risk in the EU AI Act. These include:  ■ The number of parameters of the model;  ■ The quality or size of the data set, for example measured through tokens;  ■ The input and output modalities of the model; ■ The size of its reach (e.g., if it will be made available to at least 10,000 business users); and  ■ The number of registered end-users. Beyond Safety Evaluations  ● Social Impact Evaluations: While organizations have expressed commitment and rolled out promising efforts towards technical model evaluations, there is a concurrent need to complement these assessments with a holistic evaluation of harms posed to social systems. Solaiman et al. (2024) provide detailed recommendations on the need for social impact assessments on both a model’s capabilities and its subsequent interactions in the context of its deployment.  ● Safeguarding Fundamental Rights: It is equally important to evaluate harms posed on fundamental rights.These rights include the right to decent work and standard of living, privacy and personal security, and freedom of thought, which could come under threat in the long term from automation or overreliance (UN 2023).  ● Centering Impacted Communities: Engaging proactively with vulnerable populations and advocacy groups would be critical in co-determining the anticipated exacerbation of systemic risks by AI.  ● Prioritizing Risks Based on Defense-Readiness: Intolerable-risk thresholds also should reflect the degree to which technical and societal mitigations are feasible. For initial operationalization of 21 intolerable-risk thresholds, it may be appropriate to focus first on risks with fewer available and feasible defences.18  ● Risk-Benefit Tradeoffs: Intolerable risks are absolute, and we do not see any justifiable benefit that can deter any and all reasonable efforts to avoid their impacts. However, as precursors to approaching intolerable risks, regulators must provide guidance on weighing the potentially substantial risks of frontier AI models against potentially substantial benefits.19 In some cases, tradeoffs will be tied to relative gains to offensive and defensive capabilities.20  Identifying “Intolerable” Risk Levels ○ Likelihood x Impact: Through the quantitative and qualitative estimates of AI harms through the aforementioned techniques, and modeling the risk tolerance for different risk actors and their subjective assessment of outcome likelihoods (see Appendix A for more), we can identify “intolerable” levels of risk. The Probabilistic Risk Assessment (PRA) framework for frontier AI, as outlined by Wisakanto et al. (2025), is one such approach, as it provides a multidimensional risk matrix to map estimates of the severity of rapidly escalating and cascading harms, as well as the likelihood of impacts, to identify corresponding risk levels. This analysis examines both direct and indirect risks to individuals, society, and the biosphere. The PRA is a highly useful methodology, but using it to the exclusion of other methods may lead to a one-sided focus on only those dangers that can be assigned meaningful probability estimates.21 Such systematic approaches can be adopted to produce detailed reports of identified risk levels, enabling regulators to establish intolerable risk thresholds across different types of harm. For governance purposes, industry actors must demonstrate that their AI models operate within these established risk thresholds (Wisakanto et al. 2025). ○ When coining risk thresholds, their strictness must be commensurate with the number of risk scenarios and types of harms considered (fewer types analysed leads to stricter 18 For example, new physical defenses against CBRN attacks are often harder, more expensive, and more time-intensive than software patches against cyber attacks. Thus, it may be appropriate to define a bright line earlier for CBRN than for cyber, where it may make sense to take a more adaptive approach. 19 This could help in the creation of a record of all explicit underlying assumptions about AI evaluations to inform decisions to halt or continue development (Barnett and Thiergart 2024). 20 However, these comparisons must confront the growing predictions of offense-defense balance skewing towards offense in increasingly complex AI models (Shevlane and Dafoe 2020). For instance, developing AI capabilities to defend against cybersecurity threats is more promising than developing biological capabilities that are more likely to have a longer timeline to provide satisfactory defensive uses and be at risk of malicious use in the short term. 21 The PRA is widely used in high-impact fields but does not question the underlying hazards being analyzed or sufficiently address the uncertainties inherent to its processes. However, the PRA’s strength in dealing with unavoidable hazards, when used in combination with other ex-ante methods (e.g., the ‘inherent safety’ principle which specializes in hazard elimination) can help formulate a more optimal risk assessment approach (Johnson, 2000). 22 thresholds) and the time period for them to manifest (the shorter the time, the stricter the threshold) (Koessler et al. 2024).  The role of regulators in establishing intolerable levels of risk across the different types of harm in such frameworks is discussed in more detail in the following segment. Appendix C presents a further curation of best practices that can be considered alongside the contents of this section.  3.2. Codifying Thresholds into Regulations As we elaborate in Section 2 and Appendix A, risks and their estimations are subjective, and often depend on the nature of the stakeholder, as well as unique cultural and societal notions of risk. Industry selfregulation has demonstrated inadequate capacity to comprehensively evaluate and mitigate potential systemic harms. Therefore, it is imperative that regulatory bodies establish comprehensive risk assessment frameworks to guide better governance. A recent study surveying AI experts also surfaced intolerable-risk thresholds as an important component of governance and regulatory frameworks to effectively mitigate systemic risks, but 51-78% of the experts agreed on the need for such thresholds to be set by third parties (Uuk et al. 2024). While acknowledging the difficulties in operationalizing risk thresholds, almost 97% of experts thought that triggering an immediate halt in the development or deployment of frontier models when these thresholds are breached, is certainly technically feasible. Efforts to establish risk tolerances cannot be left to industry actors alone because the appetites for risk tolerance and the priorities of public safety vary vastly between sectors, types of institutions, and even countries. For instance, infrastructure damages that richer countries could afford may cause a catastrophic level of harm in some other contexts,22 or disparate performance for different subpopulations may cause deep resentment and loss of trust in institutions, depending on the peculiar histories of their treatment. An Affirmative Safety Regime  Establishing intolerable risk levels will be critical in enacting a strong AI governance regime. Setting explicit thresholds can incentivize rigorous evaluations and responsible innovation from developers to ensure compliance through proactive demonstration of model safety. Clymer et al. (2024) provide a robust adaptation of learnings from other industries, and delineate a structured framework for “safety cases” that developers can demonstrate for their frontier models. By requiring detailed documentation of capability assessments, mitigation strategies, and empirical performance across diverse scenarios, this approach shifts the burden from identifying failures post hoc to proving safety through evidencebacked analyses and structured arguments that foster developer accountability (Wasil et al. 2024). 22 For example, the effects of climate change impact developing and low-income countries, yet those countries produce one-tenth of global emissions (WEF 2023). 23 While state actors establish the severity and appetites of various types of impacts from AI, and provide a flexible taxonomy of risk categories to be used interchangeably between frameworks, frontier model governance must place the responsibility of demonstrating safety cases on developers. This will require building state capacity to evaluate industry claims (e.g., through AI Safety Institutes and domain regulators) (Bengio et al. 2024, 2025, Clymer et al. 2024).  Designing safety cases for general purpose models may not necessarily be straightforward when evaluating multiple capabilities, their interactions, and other emergent properties unique to their deployment contexts. However, we believe that such an affirmative safety approach could help incentivize phased model releases, which could enable reliable oversight. This may also create healthy competition between frontier model developers vying to create comprehensive safety cases in business critical domains to demonstrate product safety and minimize customer liability, spurring further innovation in developing better safety techniques. Although it is fairly likely that these responsibilities will continue to be cast on downstream deployers, market forces can prompt frontier model developers to invest in creating easily adaptable evaluation frameworks to enable downstream deployers to test safety cases in their application area. This could also help improve responsible model adoption through easing the regulatory burden for downstream providers. 3.3. Limitations Identifying thresholds for intolerable risks is one component in enabling AI safety. However, as the Frontier AI Safety Commitments stipulate, there is a simultaneous need for organizations to assume concrete accountability in developing and governing AI systems transparently, as well as allocating sufficient resources to catalyze the development of robust technical tools and techniques to measure and mitigate risks.  ● Supervised fine-tuning can fail to elicit capabilities: Advanced systems may be able to “resist” fine tuning to conceal their capabilities, or use strategies not included in the supervised fine-tuning data to accomplish proxy tasks. Additionally, fine tuning data may lack sufficient quality or diversity, and optimization failures may occur (Clymer et al. 2024).  ● Status quo risks cannot be the aspiration: To approach sound empirical analyses of model capabilities against thresholds, it is necessary to determine baselines of human performance, other state-of-the-art models, and human-AI systems (UK AISI 2024). Inspired by the threat modeling approaches from computer security, Kapoor, Bommasani et al. (2024) propose to determine empirically sound model evaluation by introducing a framework to assess the marginal risk introduced by foundational models when measured against the baseline of existing threats and defenses for a particular type of risk. However, it is important to note that the existence of unmitigated risk in any domain cannot be a sufficient rationale to excuse a similarly riskprone approach in evaluating model capabilities", "metadata": {"original_filename": "item186_US_Intolerable Risk Threshold Recommendations for  Artificial Intelligence.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T22:21:34.723755", "updated_at": "2025-08-28T22:21:34.723755", "word_count": 47556}