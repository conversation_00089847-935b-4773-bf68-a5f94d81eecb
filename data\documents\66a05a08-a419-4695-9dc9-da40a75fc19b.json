{"doc_id": "66a05a08-a419-4695-9dc9-da40a75fc19b", "title": "item252_US_<PERSON>, Professor of Law, Boston University School of Law Fellow, <PERSON><PERSON><PERSON>", "text": "<PERSON>, Professor of Law, Boston University School of Law Fellow, <PERSON><PERSON><PERSON>\r\n\r\nThat's correct. Senator <PERSON>, ranking member <PERSON><PERSON>, and members of the committee, thank you for inviting me to appear before you today. My name is <PERSON> and I'm a professor of law at Boston University. My comments today are based on a decade of researching law and technology issues, and I'm drawing from research on artificial intelligence policy. I conducted as a fellow with colleagues at the Cordell Institute at Washington University in St. Louis committee members. Up to this point, AI policy has largely been made up of industry led approaches like encouraging transparency, mitigating bias, and promoting principles of ethics. I'd like to make one simple point in my testimony today. These approaches are vital, but they are only half measures. They will not fully protect us to bring AI within the rule of law. Lawmakers must go beyond these half measures to ensure that AI systems and the actors that deploy them are worthy of our trust.\r\n\r\nHalf measures like audits, assessments and certifications are necessary for data governance, but industry leverages procedural checks like these to dilute our laws into managerial box checking exercises that entrench harmful surveillance based business models. A checklist is no match for the staggering fortune available to those who exploit our data, our labor, and our precarity to develop and deploy AI systems, and it's no substitute for meaningful liability when AI systems harm the public. Today I'd like to focus on three popular half measures and why lawmakers must do more. First, transparency is a popular proposed solution for opaque systems, but it does not produce accountability on its own. Even if we truly understand the various parts of AI systems. Lawmakers must intervene when these tools are harmful and abusive. A second laudable, but insufficient approach is when companies work to mitigate bias. AI systems are notoriously biased along lines of race, class, gender, and ability.\r\n\r\nWhile mitigating bias in AI systems is critical, self-regulatory efforts to make AI fair are half measures doomed to fail. It's easy to say that AI systems should not be biased. It's very difficult to find consensus on what that means and how to get there. Additionally, it's a mistake to assume that if a system is fair, then it's safe for all people. Even if we ensure that AI systems work equally well for all communities, all we will have done is create a more effective tool that the powerful can use to dominate, manipulate, and discriminate. A third AI half measure is committing to ethical principles. Ethics are important, and these principles sound impressive, but they are a poor substitute for laws. It's easy to commit to ethics, but industry doesn't have the incentive to leave money on the table for the good of society. I have three recommendations for the committee to move beyond ai.\r\n\r\nHalf measures. First, lawmakers must accept that AI systems are not neutral and regulate how they are designed. People often argue that lawmakers should avoid design rules for technologies because there are no bad AI systems, only bad AI users. This view of technologies is wrong. There is no such thing as a neutral technology including AI systems. Facial recognition technologies empower the watcher, generative AI systems replace labor. Lawmakers should embrace established theories of accountability like product liabilities, theory of defective design or consumer protection theory of providing the means and instrumentalities of unfair and deceptive conduct. My second recommendation is to focus on substantive laws that limit abuses of power. AI systems are so complex and powerful that it can seem like trying to regulate magic, but the broader risks and benefits of AI systems are not so new AI systems bestow power. This power is used to benefit some and harm others.\r\n\r\nLawmakers should borrow from established legal approaches to remedying power imbalances to require broad non-negotiable duties of loyalty, care and confidentiality, and implement robust brightline rules that limit harmful secondary uses and disclosures of personal data in AI systems. My final recommendation is to encourage lawmakers to resist the idea that AI is inevitable. When lawmakers go straight to putting up guardrails, they fail to ask questions about whether particular AI systems should exist at all. This dooms us to half measures. Strong rules would include prohibitions on unacceptable AI practices like emotion recognition, biometric surveillance, and public spaces, predictive policing and social scoring. In conclusion, to avoid the mistakes of the past, lawmakers must make the hard calls. Trust and accountability can only exist where the law provides meaningful protections for humans and ai, half measures will certainly not be enough. Thank you, and I welcome your questions.", "metadata": {"original_filename": "item252_US_<PERSON>, Professor of Law, Boston University School of Law Fellow, Cordell.txt", "upload_method": "batch_upload"}, "created_at": "2025-08-28T21:35:14.041347", "updated_at": "2025-08-28T21:35:14.041347", "word_count": 4951}